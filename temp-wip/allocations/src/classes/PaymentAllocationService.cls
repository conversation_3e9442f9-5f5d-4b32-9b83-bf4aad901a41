/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 6/19/23.
 */

public inherited sharing class PaymentAllocationService {
    public static final Map<String,SObjectField> FIELDS             = PaymentAllocation__c.SObjectType.getDescribe().fields.getMap();
    public static final Set<String> OPPY_LINE_EDITOR_SYNC_FIELDS    = new Set<String>();

    public static final String PAYMENT_ALLOCATION_QUERY             = 'SELECT Id, OpportunityLineItem__r.Id, OpportunityLineItem__r.OpportunityId, Payment__c, Amount__c, Percent__c' +
                                                                       ' FROM PaymentAllocation__c' +
                                                                      ' WHERE OpportunityLineItem__r.OpportunityId IN :opportunityIds OR Payment__r.npe01__Opportunity__c IN :opportunityIds';
    public static final String SCHEDULE_QUERY                       = 'SELECT Id, OpportunityLineItem.Id, OpportunityLineItem.OpportunityId, Payment__c, AllocationAmount__c, OpportunityLineItem__c' +
                                                                       ' FROM OpportunityLineItemSchedule' +
                                                                      ' WHERE (OpportunityLineItem.OpportunityId IN :opportunityIds OR Payment__r.npe01__Opportunity__c IN :opportunityIds) AND AccountingType__c = \'PaymentAllocation\'';
    private static Integer suppressSyncCounter = 0;

    public static void suppressSync() {
        suppressSyncCounter++;
    }
    public static void unsuppressSync() {
        suppressSyncCounter--;
    }


    public static void syncPaymentAllocs(List<PaymentAllocation__c> pas, Map<Id, SObject> oldRecordsMap) {
        syncPaymentAllocs(
                pas,
                oldRecordsMap,
                PaymentAllocation__c.Amount__c,
                PaymentAllocation__c.Percent__c,
                PaymentAllocation__c.OpportunityLineItem__c
        );
    }
    public static void syncPaymentAllocs(List<OpportunityLineItemSchedule> olis, Map<Id, SObject> oldRecordsMap) {
        syncPaymentAllocs(
                olis,
                oldRecordsMap,
                OpportunityLineItemSchedule.AllocationAmount__c,
                OpportunityLineItemSchedule.AllocationPercent__c,
                OpportunityLineItemSchedule.OpportunityLineItemId
        );
    }
    public static void syncPaymentAllocs(List<SObject> allocations,
                                        Map<Id, SObject> oldRecordsMap,
                                        SObjectField amountField,
                                        SObjectField pctField,
                                        SObjectField oliField
    ) {
        System.debug('PaymentAllocationService.syncPaymentAllocs');

        Set<Id> oliIds = new Set<Id>();
        for (SObject payAllocNew : allocations) {
            SObject payAllocOld = oldRecordsMap?.get(payAllocNew.Id);
            // if alloNew is a new or deleted record (because alloOld is null), or if the amount/pct/GAU changed
            if (payAllocOld == null
                    || payAllocOld.get(amountField) != payAllocNew.get(amountField)
                    || payAllocOld.get(pctField) != payAllocNew.get(pctField)
            ) {
                oliIds.add((Id)payAllocNew.get(oliField));
            }
        }
        oliIds.remove(null);

        if (oliIds.size() > 0) {
            Map<Id,Opportunity> oppyMap = new Map<Id,Opportunity>([
                    SELECT Id
                    FROM Opportunity
                    WHERE Id IN (
                            SELECT OpportunityId
                            FROM OpportunityLineItem
                            WHERE Id IN :oliIds
                    )
            ]);
            PaymentAllocationService.syncPaymentAllocations(oppyMap.keySet());
        }
    }

    public static void syncPaymentAllocations(Set<Id> opportunityIds) {
        ACSettings settings = ACSettings.getInstance();
        System.debug(settings);
        if (suppressSyncCounter == 0 && settings.isEnabledPaymentAllocations && opportunityIds != null) {
            opportunityIds.remove(null);

            if (opportunityIds.size() > 0) {
                Set<String> oliFieldsToQuery = OpportunityLineItemDomain.getOLIFieldsToQuery();
                Set<String> lineEdtrFields = oliFieldsToQuery.clone();
                lineEdtrFields.removeAll(OpportunityLineItemDomain.BASE_OLI_FIELDS);
                OPPY_LINE_EDITOR_SYNC_FIELDS.addAll(lineEdtrFields);

                // Get the Opportunities, Line Items, and Payments
                String query = 'SELECT Id,\n' +
                        '                               (\n' +
                        '                                       SELECT {oliFieldsToQuery}\n' +
                        '                                         FROM OpportunityLineItems\n' +
                        '                               ),\n' +
                        '                               (\n' +
                        '                                       SELECT Id, npe01__Payment_Amount__c, CashOrWOAccount__c, WriteOffDate__c, npe01__Payment_Date__c, npe01__Scheduled_Date__c\n' +
                        '                                       FROM npe01__OppPayment__r\n' +
                        '                               )\n' +
                        '                          FROM Opportunity\n' +
                        '                         WHERE Id IN :opportunityIds';

                if (String.isNotBlank(settings.paymentAllocationsFilter)) {
                    query += ' AND ' + settings.paymentAllocationsFilter;
                }
                query = StringUtils.format(query).set('{oliFieldsToQuery}', oliFieldsToQuery, ', ').toString();

                Map<Id,Opportunity> opportunityMap = new Map<Id,Opportunity>(
                        (List<Opportunity>)DB.init(false, false, false)
                                            .read(query)
                                            .setParam('opportunityIds', opportunityIds)
                                            .go()
                );

                // Get the Existing Payment Allocations
                Map<Id,Map<Id,List<SObject>>> payAllocsByPymtByOLI = new Map<Id,Map<Id,List<SObject>>>();

                System.debug('useSchedulesForPA: ' + settings.useSchedulesForPA);
                String allocationQuery = settings.useSchedulesForPA ? SCHEDULE_QUERY : PAYMENT_ALLOCATION_QUERY;

                for (SObject payAlloc : DB.init(false, false, false)
                        .read(allocationQuery)
                        .setParam('opportunityIds',opportunityIds)
                        .go()) {
                    Id payId    = (Id)payAlloc.get('Payment__c');
                    Id oliId    = (Id)payAlloc.get('OpportunityLineItem__c');
                    Map<Id,List<SObject>> payAllocs = payAllocsByPymtByOLI.get(payId);
                    if (payAllocs == null) {
                        payAllocs = new Map<Id,List<SObject>>();
                        payAllocsByPymtByOLI.put(payId, payAllocs);
                    }
                    List<SObject> oliAllocs = payAllocs.get(oliId);
                    if (oliAllocs == null) {
                        oliAllocs = new List<SObject>();
                        payAllocs.put(oliId, oliAllocs);
                    }
                    oliAllocs.add(payAlloc);
                }

                List<SObject> allAllocsForUpsert = new List<SObject>();
                Set<Id> allAllocsForDelete = new Set<Id>();
                for (Opportunity oppy : opportunityMap.values()) {
                    System.debug('Payments: ' + oppy.npe01__OppPayment__r.size());
                    System.debug('Line Items: ' + oppy.OpportunityLineItems.size());

                    for (npe01__OppPayment__c pymt : oppy.npe01__OppPayment__r) {
                        PaymentWrapper pw = new PaymentWrapper(pymt, oppy.OpportunityLineItems, payAllocsByPymtByOLI.get(pymt.Id));
                        pw.apportionPaymentAllocs();

                        allAllocsForUpsert.addAll(pw.getRecordsToUpsert());
                        allAllocsForDelete.addAll(pw.getRecordsToDelete());
                    }
                }

                // Insert the payment allocations
                Domain.suppressExecution(Opportunity.SObjectType);
                Domain.suppressExecution(OpportunityLineItem.SObjectType);
                Domain.suppressExecution(npe01__OppPayment__c.SObjectType);
                //Domain.suppressExecution(OpportunityLineItemSchedule.SObjectType);
                //Domain.suppressExecution(PaymentAllocation__c.SObjectType);

                PaymentAllocationService.suppressSync();

                System.debug(JSON.serializePretty(allAllocsForDelete));
                System.debug(JSON.serializePretty(allAllocsForUpsert));

                if (allAllocsForDelete.size() > 0) {
                    DB.init(false, false, false).destroy(allAllocsForDelete);
                }

                if (allAllocsForUpsert.size() > 0) {
                    List<SObject> recordsToInsert   = new List<SObject>();
                    List<SObject> recordsToUpdate   = new List<SObject>();
                    for (SObject so : allAllocsForUpsert) {
                        if (so.Id == null) {
                            recordsToInsert.add(so);
                        } else {
                            recordsToUpdate.add(so);
                        }
                    }

                    DB.init(false, false, false).edit(recordsToUpdate);
                    DB.init(false, false, false).create(recordsToInsert);
                }

                PaymentAllocationService.unsuppressSync();

                //Domain.allowExecution(PaymentAllocation__c.SObjectType);
                //Domain.allowExecution(OpportunityLineItemSchedule.SObjectType);
                Domain.allowExecution(npe01__OppPayment__c.SObjectType);
                Domain.allowExecution(OpportunityLineItem.SObjectType);
                Domain.allowExecution(Opportunity.SObjectType);
            }
        }
    }

    public class PaymentWrapper {
        private final Map<Id,List<Allocation>> existingAllocsByOli;
        public final List<OpportunityLineItem> lineItems;
        public final npe01__OppPayment__c payment;
        public Decimal totalOliAmount;

        public PaymentWrapper (npe01__OppPayment__c payment, List<OpportunityLineItem> lineItems, Map<Id,List<SObject>> existingAllocsByOli) {
            this.existingAllocsByOli    = new Map<Id,List<Allocation>>();
            this.lineItems              = lineItems;
            this.payment                = payment;

            if (existingAllocsByOli != null) {
                for (Id oliId : existingAllocsByOli.keySet()) {
                    List<Allocation> allocs = new List<Allocation>();
                    for (SObject so : existingAllocsByOli.get(oliId)) {
                        allocs.add(new Allocation(so.Id));
                    }
                    this.existingAllocsByOli.put(oliId, allocs);
                }
            }

            this.totalOliAmount = 0;
            for (OpportunityLineItem oli : lineItems) {
                this.totalOliAmount += oli.TotalPrice;
            }
        }

        public void apportionPaymentAllocs () {
            System.debug('apportionPaymentAllocs lines: ' + lineItems);
            if (lineItems == null) {
                return;
            }

            Decimal amtTotal    = 0;
            Decimal pymtTotal   = Utils.nullValue(this.payment.npe01__Payment_Amount__c, 0);
            Allocation lastAlloc;

            // Overwrite existing payment allocations, or add new if none exist
            Integer i,j,apportionedCount=0;
            for (i = 0, j = lineItems.size(); i < j; i++) {
                OpportunityLineItem oli = lineItems.get(i);

                if (oli.TotalPrice != 0 && pymtTotal != 0) { // Don't create allocations for $0 lines or $0 pymts
                    System.debug('Apportioning OLI: ' + oli);

                    List<Allocation> existingAllocations = existingAllocsByOli.get(oli.Id);
                    if (existingAllocations == null) {
                        existingAllocations = new List<Allocation>();
                        existingAllocsByOli.put(oli.Id, existingAllocations);
                    }
                    System.debug('OLI\'s Existing Allocations: ' + existingAllocations);

                    Allocation pymtAlloc;
                    if (existingAllocations.size() > 0) {
                        // Get the first Allocation. Extraneous will be ignored and later deleted
                        pymtAlloc = existingAllocations.get(0);
                    } else {
                        // Make a new one
                        pymtAlloc = new Allocation();
                        existingAllocations.add(pymtAlloc);
                    }
                    System.debug('Using this allocation: ' + pymtAlloc);

                    pymtAlloc.calculatePaymentAllocation(oli, this.payment, this.totalOliAmount, j);

                    if (pymtAlloc.amount != null) {
                        amtTotal += pymtAlloc.amount;
                    }

                    lastAlloc = pymtAlloc;
                    apportionedCount++;
                }
            }

            // Apply any remaining un-apportioned amount to the last allocation
            if (amtTotal != pymtTotal && lastAlloc != null) {
                Decimal delta = (pymtTotal - amtTotal).setScale(2);
                System.debug('Delta: ' + delta);
                lastAlloc.amount += delta;
                amtTotal += delta;
            }

            System.debug('Amount Total: ' + amtTotal);
        }

        public List<SObject> getRecordsToUpsert() {
            List<SObject> records = new List<SObject>();

            for (List<Allocation> allocs : existingAllocsByOli.values()) {
                for (Allocation alloc : allocs) {
                    if (alloc.opportunityLineItemId != null && alloc.paymentId != null) {
                        records.add(alloc.toRecord());
                    }
                }
            }

            return records;
        }

        public Set<Id> getRecordsToDelete() {
            Set<Id> idsToDelete = new Set<Id>();

            for (List<Allocation> allocs : existingAllocsByOli.values()) {
                for (Allocation alloc : allocs) {
                    if ((alloc.opportunityLineItemId == null || alloc.paymentId == null) && alloc.recordId != null) {
                        idsToDelete.add(alloc.recordId);
                    }
                }
            }

            System.debug('pw.getRecordsToDelete: ' + idsToDelete);

            return idsToDelete;
        }
    }

    private class Allocation {
        private final Id recordId;
        private Id opportunityLineItemId;
        private Id paymentId;
        private Decimal amount;
        private Decimal percent;
        private Id revenueAccount;
        private Id receivableAccount;
        private Id cashWOAccount;
        private Date pymtDate;
        private final Map<String,Object> otherFields = new Map<String,Object>();

        private Allocation(Id existingRecordId) {
            this.recordId = existingRecordId;
        }
        private Allocation() {

        }

        private void calculatePaymentAllocation(OpportunityLineItem oli, npe01__OppPayment__c pymt, Decimal totalOliAmount, Integer oppyAllocsCount) {
            System.debug('calculatePaymentAllocation for OLI: ' + oli.Id);
            this.opportunityLineItemId  = oli.Id;
            this.paymentId              = pymt.Id;
            this.amount                 = oli.TotalPrice;
            if (this.amount == null) {
                this.amount = oli.UnitPrice * oli.Quantity;
            }
            this.revenueAccount         = oli.RevenueAccount__c;
            this.receivableAccount      = oli.ReceivablesAccount__c;
            this.cashWOAccount          = pymt.CashOrWOAccount__c;
            this.pymtDate               = (Date)Utils.nullValue(new List<Object>{pymt.WriteOffDate__c, pymt.npe01__Payment_Date__c, pymt.npe01__Scheduled_Date__c, Date.today()});

            /*
             Synchronize any fields that exist on both the OLI and the PA and are writeable on the PA.
             This covers things like Project, Fundraising Campaign, etc.
             */
            for (String fieldName : OPPY_LINE_EDITOR_SYNC_FIELDS) {
                this.otherFields.put(fieldName, oli.get(fieldName));
            }

            System.debug('calculatePaymentAllocation:before = ' + this);
            System.debug('Oppy Amount: ' + totalOliAmount);
            System.debug('Pymt Amount: ' + pymt.npe01__Payment_Amount__c);
            System.debug('Payment Allocation Percent:  ' + this.percent);
            System.debug('Payment Allocation Amount: ' + this.amount);

            if (this.percent != null) {
                this.amount = (pymt.npe01__Payment_Amount__c * this.percent * .01).setScale(2);
            } else {
                Decimal alloPercent;
                if (totalOliAmount != 0) {
                    // Calculate proportional amount for non-percentage based Payment allocations
                    alloPercent = this.amount / totalOliAmount * 100;
                } else {
                    this.amount	    = 0;
                    this.percent	= 0;
                    alloPercent = 1 / oppyAllocsCount * 100;
                }

                this.amount = (pymt.npe01__Payment_Amount__c * alloPercent * .01).setScale(2);

                System.debug('Payment Allocation Amount (new): ' + this.amount);
                System.debug('Payment Allocation Percent (new):  ' + alloPercent);

                // Store Percentage as is, so precision is not lost in future calculations
                this.percent = alloPercent;
            }

            System.debug('calculatePaymentAllocation:after = ' + this);
        }

        private SObject toRecord() {
            System.debug('useSchedulesForPA: ' + ACSettings.getInstance().useSchedulesForPA);
            SObject rec = ACSettings.getInstance().useSchedulesForPA ? (SObject)this.toLineItemSchedule() : (SObject)this.toPaymentAllocation();

            rec.Id = this.recordId;
            rec.put('RevenueAccount__c', this.revenueAccount);
            rec.put('ReceivablesAccount__c', this.receivableAccount);
            rec.put('CashOrWOAccount__c', this.cashWOAccount);

            return rec;
        }
        private PaymentAllocation__c toPaymentAllocation() {
            PaymentAllocation__c newPymtAlloc   = new PaymentAllocation__c();
            newPymtAlloc.OpportunityLineItem__c = this.opportunityLineItemId;
            newPymtAlloc.Payment__c             = this.paymentId;
            newPymtAlloc.Percent__c             = this.percent;
            newPymtAlloc.Amount__c              = this.amount;

            for (String otherFieldKey : this.otherFields.keySet()) {
                if (FIELDS.containsKey(otherFieldKey)) {
                    newPymtAlloc.put(otherFieldKey, this.otherFields.get(otherFieldKey));
                }
            }

            return newPymtAlloc;
        }
        private OpportunityLineItemSchedule toLineItemSchedule() {
            OpportunityLineItemSchedule newPymtAlloc    = new OpportunityLineItemSchedule();
            newPymtAlloc.Type                           = 'Revenue';
            newPymtAlloc.Revenue                        = 0.0; // This is always 0 for the payment allocations.
            newPymtAlloc.ScheduleDate                   = this.pymtDate;
            newPymtAlloc.OpportunityLineItemId          = this.opportunityLineItemId;
            newPymtAlloc.OpportunityLineItem__c         = this.opportunityLineItemId;
            newPymtAlloc.AccountingType__c              = 'PaymentAllocation';
            newPymtAlloc.AllocationAmount__c            = this.amount;
            newPymtAlloc.AllocationPercent__c           = this.percent;
            newPymtAlloc.Payment__c                     = this.paymentId;

            for (String otherFieldKey : this.otherFields.keySet()) {
                if (EarnScheduleService.FIELDS.containsKey(otherFieldKey)) {
                    newPymtAlloc.put(otherFieldKey, this.otherFields.get(otherFieldKey));
                }
            }

            return newPymtAlloc;
        }
    }



    public static void rollupAllocations(List<OpportunityLineItemSchedule> olis, Map<Id,SObject> oldRecordsMap) {
        rollupAllocations(olis, oldRecordsMap, OpportunityLineItemSchedule.Payment__c, OpportunityLineItemSchedule.AllocationAmount__c);
    }
    public static void rollupAllocations(List<PaymentAllocation__c> pas, Map<Id,SObject> oldRecordsMap) {
        rollupAllocations(pas, oldRecordsMap, PaymentAllocation__c.Payment__c, PaymentAllocation__c.Amount__c);
    }
    private static void rollupAllocations(List<SObject> allocations, Map<Id,SObject> oldRecordsMap, SObjectField paymentField, SObjectField amountField) {
        System.debug('PaymentAllocationService.rollupAllocations');
        ACSettings settings = ACSettings.getInstance();
        if (settings.isEnabledPaymentAllocations) {
            // Loop through the records, and if qualified, add the payment id to a list;
            Map<Id,npe01__OppPayment__c> paymentsById = new Map<Id,npe01__OppPayment__c>();
            for (SObject allocNew : allocations) {
                SObject allocOld = oldRecordsMap?.get(allocNew.Id);

                // if allocNew is a new or deleted record (because allocOld is null), or if the amount/pct/GAU changed
                if (allocOld == null
                        || allocOld.get(amountField) != allocNew.get(amountField)
                        || allocOld.get(paymentField) != allocNew.get(paymentField)
                ) {
                    Id pymtId = (Id)allocNew.get(paymentField);
                    paymentsById.put(pymtId, new npe01__OppPayment__c(Id=pymtId, TotalPaymentAllocations__c = null));

                    if (allocOld != null) {
                        paymentsById.put((Id)allocOld.get(paymentField), new npe01__OppPayment__c(Id=(Id)allocOld.get(paymentField), TotalPaymentAllocations__c = null));
                    }
                }
            }
            paymentsById.remove(null);

            if (paymentsById.size() > 0) {
                PaymentDomain.rollupAllocations(paymentsById.values());

                // Update the payment with the new aggregate.
                DB.init(false, false, false).edit(paymentsById.values());
            }
        }
    }
}