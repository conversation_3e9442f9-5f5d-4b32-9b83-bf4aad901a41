/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 8/30/24.
 */

public with sharing class ToolingAPI_EB {
    private static final String RESOURCE_COMPOSITE  = '/services/data/v61.0/composite/sobjects/'; // collections API doesn't support CustomField tooling sobject :(
    private static final String RESOURCE_QUERY      = '/services/data/v61.0/tooling/query?q=';


    /**
     * Uses the current users Session Id, only compatible in a interactive context
     * @throws ToolingAPIException if no Session Id can be resolved (e.g. in a batch context)
     **/
    public ToolingAPI_EB() {
        this(UserInfo.getSessionId(), URL.getOrgDomainUrl().toExternalForm());
    }

    /*
     * Uses the given Session Id, useful when using the API in a batch context
     *
     **/
    public ToolingAPI_EB(String sessionId, String endpoint) {
        if(sessionId==null)
            throw new ToolingAPIException('Unable to obtain Session Id');

        /*service = new ToolingAPIWSDL.SforceService();
        service.SessionHeader = new ToolingAPIWSDL.SessionHeader_element();
        service.SessionHeader.sessionId = sessionId;
        service.endpoint_x = endpoint + endpointSuffix;*/
    }

    public String query(String qry) {
        qry = qry.replaceAll(' ','+');
        HttpRequest req = new HttpRequest();
        req.setHeader('Authorization', 'Bearer ' + UserInfo.getSessionId());
        req.setHeader('Content-Type', 'application/json');
        req.setMethod('GET');

        req.setEndpoint(URL.getOrgDomainUrl().toExternalForm() + RESOURCE_QUERY + qry);
        Http httpreq = new Http();
        HttpResponse res = httpreq.send(req);

        if (res.getStatusCode() >= 400) {
            throw new ToolingAPIException(res.getBody());
        }
        return res.getBody();
    }
    public void updateSObject(ToolingSObjectRecord record, Boolean allOrNone) {
        HttpRequest req = new HttpRequest();
        req.setHeader('Authorization', 'Bearer ' + UserInfo.getSessionId());
        req.setHeader('Content-Type', 'application/json');
        req.setMethod('PATCH');
        req.setEndpoint(URL.getOrgDomainUrl().toExternalForm() + RESOURCE_COMPOSITE);

        JSONGenerator gen = JSON.createGenerator(true);
        gen.writeStartObject();
        gen.writeBooleanField('allOrNone',allOrNone);
        gen.writeFieldName('records');
        gen.writeStartArray();
        /*for (ToolingSObjectRecord r : records) {
            r.writeJSON(gen);
        }*/
        gen.writeEndArray();
        gen.writeEndObject();
        String reqBody = gen.getAsString();

        /*String reqBody = JSON.serialize(new Map<String,Object>{
                'records'   => records,
                'allOrNone' => allOrNone
        }, true);*/
        SYstem.debug(reqBody);
        req.setBody(reqBody);
        Http httpreq = new Http();
        HttpResponse res = httpreq.send(req);

        if (res.getStatusCode() >= 400) {
            throw new ToolingAPIException(res.getBody());
        }

        System.debug(res.getBody());
    }
    /*public void updateSObjects(List<ToolingSObjectRecord> records, Boolean allOrNone) {
        HttpRequest req = new HttpRequest();
        req.setHeader('Authorization', 'Bearer ' + UserInfo.getSessionId());
        req.setHeader('Content-Type', 'application/json');
        req.setMethod('PATCH');
        req.setEndpoint(URL.getOrgDomainUrl().toExternalForm() + RESOURCE_COMPOSITE);

        JSONGenerator gen = JSON.createGenerator(true);
        gen.writeStartObject();
        gen.writeBooleanField('allOrNone',allOrNone);
        gen.writeFieldName('records');
        gen.writeStartArray();
        for (ToolingSObjectRecord r : records) {
            r.writeJSON(gen);
        }
        gen.writeEndArray();
        gen.writeEndObject();
        String reqBody = gen.getAsString();

        *//*String reqBody = JSON.serialize(new Map<String,Object>{
                'records'   => records,
                'allOrNone' => allOrNone
        }, true);*//*
        SYstem.debug(reqBody);
        req.setBody(reqBody);
        Http httpreq = new Http();
        HttpResponse res = httpreq.send(req);

        if (res.getStatusCode() >= 400) {
            throw new ToolingAPIException(res.getBody());
        }

        System.debug(res.getBody());
    }*/

    public class ToolingAPIException extends Exception {}

    public virtual class ToolingSObjectRecord {
        public Map<String,String> attributes;
        public Id Id;
        public String FullName;
        public Metadata Metadata;

        public ToolingSObjectRecord(String type, String FullName, Metadata metadata) {
            this.attributes = new Map<String,String>{'type' => type};
            this.FullName = FullName;
            this.Metadata = metadata;
        }

        public void writeJSON(JSONGenerator gen) {
            gen.writeStartObject();
            gen.writeObjectField('attributes', this.attributes);
            if (this.Id != null) {
                gen.writeStringField('Id', this.Id);
            } else {
                gen.writeStringField('FullName', this.FullName);
            }
            gen.writeObjectField('Metadata', this.Metadata);
            gen.writeEndObject();
        }
    }
    public virtual class Metadata {}
    public class GlobalValueSet extends Metadata {
        public List<PicklistValue> customValue;
        public String description;
        public String masterLabel;
        public Boolean sorted;
    }
    public class CustomField extends Metadata {
        public String ManageableState;
        public String DeveloperName;
        public String NamespacePrefix;
        public String TableEnumOrId;
        public String type;
        public ValueSet valueSet;
    }
    public class ValueSet {
        public String controllingField;
        public Boolean restricted;
        public ValueSetDefinition valueSetDefinition;
        public String valueSetName;
        public Object valueSettings;

        public ValueSet() {
            this.valueSetDefinition = new ValueSetDefinition();
        }
    }
    public class ValueSetDefinition {
        public Boolean sorted;
        public List<PicklistValue> value;

        public ValueSetDefinition() {
            this.value = new List<PicklistValue>();
        }
    }
    public class PicklistValue {
        public String color;
        public String description;
        public Boolean isActive;
        public String label;
        public String urls;
        public String valueName;

        public PicklistValue(){}
        public PicklistValue(String name, String label){
            this.valueName  = name;
            this.label      = label;
        }
    }
}