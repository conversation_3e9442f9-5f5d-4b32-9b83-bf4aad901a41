/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 8/31/24.
 */

public with sharing class ToolingAPIWSDL {
    public virtual class sObject_x {
        public String[] fieldsToNull;
        public String Id;
        private String[] fieldsToNull_type_info = new String[]{'fieldsToNull','urn:sobject.tooling.soap.sforce.com',null,'0','-1','true'};
        private String[] Id_type_info = new String[]{'Id','urn:sobject.tooling.soap.sforce.com',null,'1','1','true'};
        private String[] apex_schema_type_info = new String[]{'urn:sobject.tooling.soap.sforce.com','true','false'};
        private String[] field_order_type_info = new String[]{'fieldsToNull','Id'};
    }
    public class CustomField extends sObject_x {
        public String type = 'CustomField';
        private String[] type_att_info = new String[]{'xsi:type'};

        //public String Description;
        //public String DeveloperName;
        public String FullName;
        public MetadataAPIWSDL.CustomField Metadata;
        //public String NamespacePrefix;
        //public String TableEnumOrId;
        //private String[] Description_type_info = new String[]{'Description','urn:sobject.tooling.soap.sforce.com',null,'0','1','true'};
        //private String[] DeveloperName_type_info = new String[]{'DeveloperName','urn:sobject.tooling.soap.sforce.com',null,'0','1','true'};
        private String[] FullName_type_info = new String[]{'FullName','urn:sobject.tooling.soap.sforce.com',null,'0','1','true'};
        private String[] Metadata_type_info = new String[]{'Metadata','urn:sobject.tooling.soap.sforce.com',null,'0','1','true'};
        //private String[] NamespacePrefix_type_info = new String[]{'NamespacePrefix','urn:sobject.tooling.soap.sforce.com',null,'0','1','true'};
        //private String[] TableEnumOrId_type_info = new String[]{'TableEnumOrId','urn:sobject.tooling.soap.sforce.com',null,'0','1','true'};
        private String[] apex_schema_type_info = new String[]{'urn:sobject.tooling.soap.sforce.com','true','false'};
        private String[] field_order_type_info = new String[]{'FullName','Metadata'};

        public CustomField(String label, String type) {
            this.Metadata = new MetadataAPIWSDL.CustomField(label, type);
        }
    }
    public class GlobalValueSet extends sObject_x {
        public String type = 'GlobalValueSet';
        private String[] type_att_info = new String[]{'xsi:type'};

        public String Description;
        public String DeveloperName;
        public String FullName;
        public String MasterLabel;
        public MetadataAPIWSDL.GlobalValueSet Metadata;
        private String[] Description_type_info = new String[]{'Description','urn:sobject.tooling.soap.sforce.com',null,'0','1','true'};
        private String[] DeveloperName_type_info = new String[]{'DeveloperName','urn:sobject.tooling.soap.sforce.com',null,'0','1','true'};
        private String[] FullName_type_info = new String[]{'FullName','urn:sobject.tooling.soap.sforce.com',null,'0','1','true'};
        private String[] MasterLabel_type_info = new String[]{'MasterLabel','urn:sobject.tooling.soap.sforce.com',null,'0','1','true'};
        private String[] Metadata_type_info = new String[]{'Metadata','urn:sobject.tooling.soap.sforce.com',null,'0','1','true'};
        private String[] apex_schema_type_info = new String[]{'urn:sobject.tooling.soap.sforce.com','true','false'};
        private String[] field_order_type_info = new String[]{'Description','DeveloperName','FullName','MasterLabel','Metadata'};
    }
    public class NameValuePair {
        public String name;
        public String value;
        private String[] name_type_info = new String[]{'name','urn:tooling.soap.sforce.com',null,'1','1','false'};
        private String[] value_type_info = new String[]{'value','urn:tooling.soap.sforce.com',null,'1','1','false'};
        private String[] apex_schema_type_info = new String[]{'urn:tooling.soap.sforce.com','true','false'};
        private String[] field_order_type_info = new String[]{'name','value'};
    }
    public class Warning {
        public ExtendedErrorDetails[] extendedDetails;
        public String message;
        public String statusCode;
        private String[] extendedDetails_type_info = new String[]{'extendedDetails','urn:tooling.soap.sforce.com',null,'0','-1','false'};
        private String[] message_type_info = new String[]{'message','urn:tooling.soap.sforce.com',null,'1','1','false'};
        private String[] statusCode_type_info = new String[]{'statusCode','urn:tooling.soap.sforce.com',null,'1','1','false'};
        private String[] apex_schema_type_info = new String[]{'urn:tooling.soap.sforce.com','true','false'};
        private String[] field_order_type_info = new String[]{'extendedDetails','message','statusCode'};
    }
    public class MetadataWarningsHeader_element {
        public Boolean ignoreSaveWarnings;
        private String[] ignoreSaveWarnings_type_info = new String[]{'ignoreSaveWarnings','urn:tooling.soap.sforce.com',null,'1','1','false'};
        private String[] apex_schema_type_info = new String[]{'urn:tooling.soap.sforce.com','true','false'};
        private String[] field_order_type_info = new String[]{'ignoreSaveWarnings'};
    }
    public class MetadataVersionCheck_element {
        public Fact[] facts;
        public String operation;
        private String[] facts_type_info = new String[]{'facts','urn:tooling.soap.sforce.com',null,'0','-1','false'};
        private String[] operation_type_info = new String[]{'operation','urn:tooling.soap.sforce.com',null,'1','1','false'};
        private String[] apex_schema_type_info = new String[]{'urn:tooling.soap.sforce.com','true','false'};
        private String[] field_order_type_info = new String[]{'facts','operation'};
    }
    public class Error {
        public ExtendedErrorDetails[] extendedErrorDetails;
        public String[] fields;
        public String message;
        public String statusCode;
        private String[] extendedErrorDetails_type_info = new String[]{'extendedErrorDetails','urn:tooling.soap.sforce.com',null,'0','-1','false'};
        private String[] fields_type_info = new String[]{'fields','urn:tooling.soap.sforce.com',null,'0','-1','false'};
        private String[] message_type_info = new String[]{'message','urn:tooling.soap.sforce.com',null,'1','1','false'};
        private String[] statusCode_type_info = new String[]{'statusCode','urn:tooling.soap.sforce.com',null,'1','1','false'};
        private String[] apex_schema_type_info = new String[]{'urn:tooling.soap.sforce.com','true','false'};
        private String[] field_order_type_info = new String[]{'extendedErrorDetails','fields','message','statusCode'};
    }
    public class CallOptions_element {
        public String client;
        private String[] client_type_info = new String[]{'client','urn:tooling.soap.sforce.com',null,'1','1','false'};
        private String[] apex_schema_type_info = new String[]{'urn:tooling.soap.sforce.com','true','false'};
        private String[] field_order_type_info = new String[]{'client'};
    }
    public class SaveResult {
        public Error[] errors;
        public String id;
        public Info[] infos;
        public Boolean success;
        public Warning[] warnings;
        private String[] errors_type_info = new String[]{'errors','urn:tooling.soap.sforce.com',null,'0','-1','false'};
        private String[] id_type_info = new String[]{'id','urn:tooling.soap.sforce.com',null,'1','1','false'};
        private String[] infos_type_info = new String[]{'infos','urn:tooling.soap.sforce.com',null,'0','-1','false'};
        private String[] success_type_info = new String[]{'success','urn:tooling.soap.sforce.com',null,'1','1','false'};
        private String[] warnings_type_info = new String[]{'warnings','urn:tooling.soap.sforce.com',null,'0','-1','false'};
        private String[] apex_schema_type_info = new String[]{'urn:tooling.soap.sforce.com','true','false'};
        private String[] field_order_type_info = new String[]{'errors','id','infos','success','warnings'};
    }
    public class Info {
        public ExtendedErrorDetails[] extendedDetails;
        public String message;
        public String statusCode;
        private String[] extendedDetails_type_info = new String[]{'extendedDetails','urn:tooling.soap.sforce.com',null,'0','-1','false'};
        private String[] message_type_info = new String[]{'message','urn:tooling.soap.sforce.com',null,'1','1','false'};
        private String[] statusCode_type_info = new String[]{'statusCode','urn:tooling.soap.sforce.com',null,'1','1','false'};
        private String[] apex_schema_type_info = new String[]{'urn:tooling.soap.sforce.com','true','false'};
        private String[] field_order_type_info = new String[]{'extendedDetails','message','statusCode'};
    }
    public class ExtendedErrorDetails {
        public String extendedErrorCode;
        private String[] extendedErrorCode_type_info = new String[]{'extendedErrorCode','urn:tooling.soap.sforce.com',null,'1','1','false'};
        private String[] apex_schema_type_info = new String[]{'urn:tooling.soap.sforce.com','true','false'};
        private String[] field_order_type_info = new String[]{'extendedErrorCode'};
    }
    public class Fact {
        public String fact;
        public String subject;
        public String token;
        private String[] fact_type_info = new String[]{'fact','urn:tooling.soap.sforce.com',null,'1','1','false'};
        private String[] subject_type_info = new String[]{'subject','urn:tooling.soap.sforce.com',null,'1','1','false'};
        private String[] token_type_info = new String[]{'token','urn:tooling.soap.sforce.com',null,'1','1','false'};
        private String[] apex_schema_type_info = new String[]{'urn:tooling.soap.sforce.com','true','false'};
        private String[] field_order_type_info = new String[]{'fact','subject','token'};
    }
    public class updateResponse_element {
        public SaveResult[] result;
        private String[] result_type_info = new String[]{'result','urn:tooling.soap.sforce.com',null,'0','-1','false'};
        private String[] apex_schema_type_info = new String[]{'urn:tooling.soap.sforce.com','true','false'};
        private String[] field_order_type_info = new String[]{'result'};
    }
    public class APIPerformanceInfo_element {
        public String encodedIntervalTimerTree;
        public NameValuePair[] handlerMetrics;
        private String[] encodedIntervalTimerTree_type_info = new String[]{'encodedIntervalTimerTree','urn:tooling.soap.sforce.com',null,'1','1','false'};
        private String[] handlerMetrics_type_info = new String[]{'handlerMetrics','urn:tooling.soap.sforce.com',null,'0','-1','false'};
        private String[] apex_schema_type_info = new String[]{'urn:tooling.soap.sforce.com','true','false'};
        private String[] field_order_type_info = new String[]{'encodedIntervalTimerTree','handlerMetrics'};
    }
    public class update_element {
        public sObject_x[] sObjects;
        private String[] sObjects_type_info = new String[]{'sObjects','urn:tooling.soap.sforce.com',null,'0','-1','false'};
        private String[] apex_schema_type_info = new String[]{'urn:tooling.soap.sforce.com','true','false'};
        private String[] field_order_type_info = new String[]{'sObjects'};
    }
    public class SessionHeader_element {
        public String sessionId;
        private String[] sessionId_type_info = new String[]{'sessionId','urn:tooling.soap.sforce.com',null,'1','1','false'};
        private String[] apex_schema_type_info = new String[]{'urn:tooling.soap.sforce.com','true','false'};
        private String[] field_order_type_info = new String[]{'sessionId'};
    }
    public class AllOrNoneHeader_element {
        public Boolean allOrNone;
        private String[] allOrNone_type_info = new String[]{'allOrNone','urn:tooling.soap.sforce.com',null,'1','1','false'};
        private String[] apex_schema_type_info = new String[]{'urn:tooling.soap.sforce.com','true','false'};
        private String[] field_order_type_info = new String[]{'allOrNone'};
    }
    public class SforceService {
        public String endpoint_x = 'https://test.salesforce.com/services/Soap/T/62.0';
        public Map<String,String> inputHttpHeaders_x;
        public Map<String,String> outputHttpHeaders_x;
        public String clientCertName_x;
        public String clientCert_x;
        public String clientCertPasswd_x;
        public Integer timeout_x;
        public AllOrNoneHeader_element AllOrNoneHeader;
        public CallOptions_element CallOptions;
        public MetadataVersionCheck_element MetadataVersionCheck;
        public MetadataWarningsHeader_element MetadataWarningsHeader;
        public APIPerformanceInfo_element APIPerformanceInfo;
        public SessionHeader_element SessionHeader;
        private String AllOrNoneHeader_hns = 'AllOrNoneHeader=urn:tooling.soap.sforce.com';
        private String CallOptions_hns = 'CallOptions=urn:tooling.soap.sforce.com';
        private String MetadataVersionCheck_hns = 'MetadataVersionCheck=urn:tooling.soap.sforce.com';
        private String MetadataWarningsHeader_hns = 'MetadataWarningsHeader=urn:tooling.soap.sforce.com';
        private String APIPerformanceInfo_hns = 'APIPerformanceInfo=urn:tooling.soap.sforce.com';
        private String SessionHeader_hns = 'SessionHeader=urn:tooling.soap.sforce.com';
        private String[] ns_map_type_info = new String[]{'urn:metadata.tooling.soap.sforce.com', 'metadataToolingSoapSforceCom', 'urn:sobject.tooling.soap.sforce.com', 'sobjectToolingSoapSforceCom', 'urn:fault.tooling.soap.sforce.com', 'faultToolingSoapSforceCom', 'urn:tooling.soap.sforce.com', 'toolingSoapSforceCom'};
        public SaveResult[] update_x(sObject_x[] sObjects) {
            update_element request_x = new update_element();
            request_x.sObjects = sObjects;
            updateResponse_element response_x;
            Map<String, updateResponse_element> response_map_x = new Map<String, updateResponse_element>();
            response_map_x.put('response_x', response_x);
            WebServiceCallout.invoke(
                    this,
                    request_x,
                    response_map_x,
                    new String[]{endpoint_x,
                            '',
                            'urn:tooling.soap.sforce.com',
                            'update',
                            'urn:tooling.soap.sforce.com',
                            'updateResponse',
                            'ToolingAPIWSDL.updateResponse_element'}
            );
            response_x = response_map_x.get('response_x');
            return response_x.result;
        }
    }
}