/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 8/31/24.
 */

public with sharing class MetadataAPIWSDL {
    public class ValueSetValuesDefinition {
        public Boolean sorted;
        public CustomValue[] value;
        private String[] sorted_type_info = new String[]{'sorted','urn:metadata.tooling.soap.sforce.com',null,'1','1','false'};
        private String[] value_type_info = new String[]{'value','urn:metadata.tooling.soap.sforce.com',null,'0','-1','false'};
        private String[] apex_schema_type_info = new String[]{'urn:metadata.tooling.soap.sforce.com','true','false'};
        private String[] field_order_type_info = new String[]{'sorted','value'};

        public ValueSetValuesDefinition() {
            this.value  = new List<CustomValue>();
            this.sorted = false;
        }
    }
    public virtual class Metadata {
        private String[] apex_schema_type_info = new String[]{'urn:metadata.tooling.soap.sforce.com','true','false'};
        private String[] field_order_type_info = new String[]{};
    }
    public class CustomField extends Metadata {
        public String businessOwnerGroup;
        public String businessOwnerUser;
        public String businessStatus;
        public Boolean caseSensitive;
        public String complianceGroup;
        public String customDataType;
        public String defaultValue;
        public String deleteConstraint;
        public Boolean deprecated;
        public String description;
        public String displayFormat;
        public String elementType;
        public String encryptionScheme;
        public Boolean escapeMarkup;
        public String externalDeveloperName;
        public Boolean externalId;
        public String formula;
        public String formulaTreatBlanksAs;
        public String inlineHelpText;
        public Boolean isAIPredictionField;
        public Boolean isConvertLeadDisabled;
        public Boolean isFilteringDisabled;
        public Boolean isNameField;
        public Boolean isSortingDisabled;
        public String label;
        public Integer length;
        public String lookupFilter;
        public String maskChar;
        public String maskType;
        public String metadataRelationshipControllingField;
        public Boolean populateExistingRows;
        public Integer precision;
        public String referenceTargetField;
        public String referenceTo;
        public String relationshipLabel;
        public String relationshipName;
        public Integer relationshipOrder;
        public Boolean reparentableMasterDetail;
        public Boolean required;
        public Boolean restrictedAdminField;
        public Integer scale;
        public String securityClassification;
        public Integer startingNumber;
        public Boolean stripMarkup;
        public String summarizedField;
        public String[] summaryFilterItems;
        public String summaryForeignKey;
        public String summaryOperation;
        public Boolean trackFeedHistory;
        public Boolean trackHistory;
        public Boolean trackTrending;
        public Boolean translateData;
        public String type_x;
        public Boolean unique;
        public ValueSet valueSet;
        public Integer visibleLines;
        public Boolean writeRequiresMasterRead;
        private String[] businessOwnerGroup_type_info = new String[]{'businessOwnerGroup','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] businessOwnerUser_type_info = new String[]{'businessOwnerUser','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] businessStatus_type_info = new String[]{'businessStatus','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] caseSensitive_type_info = new String[]{'caseSensitive','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] complianceGroup_type_info = new String[]{'complianceGroup','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] customDataType_type_info = new String[]{'customDataType','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] defaultValue_type_info = new String[]{'defaultValue','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] deleteConstraint_type_info = new String[]{'deleteConstraint','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] deprecated_type_info = new String[]{'deprecated','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] description_type_info = new String[]{'description','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] displayFormat_type_info = new String[]{'displayFormat','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] elementType_type_info = new String[]{'elementType','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] encryptionScheme_type_info = new String[]{'encryptionScheme','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] escapeMarkup_type_info = new String[]{'escapeMarkup','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] externalDeveloperName_type_info = new String[]{'externalDeveloperName','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] externalId_type_info = new String[]{'externalId','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] formula_type_info = new String[]{'formula','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] formulaTreatBlanksAs_type_info = new String[]{'formulaTreatBlanksAs','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] inlineHelpText_type_info = new String[]{'inlineHelpText','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] isAIPredictionField_type_info = new String[]{'isAIPredictionField','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] isConvertLeadDisabled_type_info = new String[]{'isConvertLeadDisabled','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] isFilteringDisabled_type_info = new String[]{'isFilteringDisabled','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] isNameField_type_info = new String[]{'isNameField','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] isSortingDisabled_type_info = new String[]{'isSortingDisabled','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] label_type_info = new String[]{'label','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] length_type_info = new String[]{'length','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] lookupFilter_type_info = new String[]{'lookupFilter','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] maskChar_type_info = new String[]{'maskChar','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] maskType_type_info = new String[]{'maskType','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] metadataRelationshipControllingField_type_info = new String[]{'metadataRelationshipControllingField','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] populateExistingRows_type_info = new String[]{'populateExistingRows','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] precision_type_info = new String[]{'precision','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] referenceTargetField_type_info = new String[]{'referenceTargetField','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] referenceTo_type_info = new String[]{'referenceTo','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] relationshipLabel_type_info = new String[]{'relationshipLabel','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] relationshipName_type_info = new String[]{'relationshipName','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] relationshipOrder_type_info = new String[]{'relationshipOrder','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] reparentableMasterDetail_type_info = new String[]{'reparentableMasterDetail','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] required_type_info = new String[]{'required','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] restrictedAdminField_type_info = new String[]{'restrictedAdminField','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] scale_type_info = new String[]{'scale','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] securityClassification_type_info = new String[]{'securityClassification','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] startingNumber_type_info = new String[]{'startingNumber','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] stripMarkup_type_info = new String[]{'stripMarkup','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] summarizedField_type_info = new String[]{'summarizedField','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] summaryFilterItems_type_info = new String[]{'summaryFilterItems','urn:metadata.tooling.soap.sforce.com',null,'0','-1','false'};
        private String[] summaryForeignKey_type_info = new String[]{'summaryForeignKey','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] summaryOperation_type_info = new String[]{'summaryOperation','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] trackFeedHistory_type_info = new String[]{'trackFeedHistory','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] trackHistory_type_info = new String[]{'trackHistory','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] trackTrending_type_info = new String[]{'trackTrending','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] translateData_type_info = new String[]{'translateData','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] type_x_type_info = new String[]{'type','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] unique_type_info = new String[]{'unique','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] valueSet_type_info = new String[]{'valueSet','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] visibleLines_type_info = new String[]{'visibleLines','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] writeRequiresMasterRead_type_info = new String[]{'writeRequiresMasterRead','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] apex_schema_type_info = new String[]{'urn:metadata.tooling.soap.sforce.com','true','false'};
        private String[] field_order_type_info = new String[]{'businessOwnerGroup','businessOwnerUser','businessStatus','caseSensitive','complianceGroup','customDataType','defaultValue','deleteConstraint','deprecated','description','displayFormat','elementType','encryptionScheme','escapeMarkup','externalDeveloperName','externalId','formula','formulaTreatBlanksAs','inlineHelpText','isAIPredictionField','isConvertLeadDisabled','isFilteringDisabled','isNameField','isSortingDisabled','label','length','lookupFilter','maskChar','maskType','metadataRelationshipControllingField','populateExistingRows','precision','referenceTargetField','referenceTo','relationshipLabel','relationshipName','relationshipOrder','reparentableMasterDetail','required','restrictedAdminField','scale','securityClassification','startingNumber','stripMarkup','summarizedField','summaryFilterItems','summaryForeignKey','summaryOperation','trackFeedHistory','trackHistory','trackTrending','translateData','type_x','unique','valueSet','visibleLines','writeRequiresMasterRead'};

        public CustomField(String label, String type) {
            this.valueSet = new ValueSet();
            this.type_x     = type;
            this.label      = label;
        }
    }
    public class ValueSettings {
        public String[] controllingFieldValue;
        public String valueName;
        private String[] controllingFieldValue_type_info = new String[]{'controllingFieldValue','urn:metadata.tooling.soap.sforce.com',null,'0','-1','false'};
        private String[] valueName_type_info = new String[]{'valueName','urn:metadata.tooling.soap.sforce.com',null,'1','1','false'};
        private String[] apex_schema_type_info = new String[]{'urn:metadata.tooling.soap.sforce.com','true','false'};
        private String[] field_order_type_info = new String[]{'controllingFieldValue','valueName'};
    }
    public class GlobalValueSet extends Metadata {
        public CustomValue[] customValue;
        public String description;
        public String masterLabel;
        public Boolean sorted;
        private String[] customValue_type_info = new String[]{'customValue','urn:metadata.tooling.soap.sforce.com',null,'0','-1','false'};
        private String[] description_type_info = new String[]{'description','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] masterLabel_type_info = new String[]{'masterLabel','urn:metadata.tooling.soap.sforce.com',null,'1','1','false'};
        private String[] sorted_type_info = new String[]{'sorted','urn:metadata.tooling.soap.sforce.com',null,'1','1','false'};
        private String[] apex_schema_type_info = new String[]{'urn:metadata.tooling.soap.sforce.com','true','false'};
        private String[] field_order_type_info = new String[]{'customValue','description','masterLabel','sorted'};
    }
    public class ValueSet {
        public String controllingField;
        public Boolean restricted;
        public ValueSetValuesDefinition valueSetDefinition;
        public String valueSetName;
        public ValueSettings[] valueSettings;
        private String[] controllingField_type_info = new String[]{'controllingField','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] restricted_type_info = new String[]{'restricted','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] valueSetDefinition_type_info = new String[]{'valueSetDefinition','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] valueSetName_type_info = new String[]{'valueSetName','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] valueSettings_type_info = new String[]{'valueSettings','urn:metadata.tooling.soap.sforce.com',null,'0','-1','false'};
        private String[] apex_schema_type_info = new String[]{'urn:metadata.tooling.soap.sforce.com','true','false'};
        private String[] field_order_type_info = new String[]{'controllingField','restricted','valueSetDefinition','valueSetName','valueSettings'};

        public ValueSet() {
            valueSetDefinition = new ValueSetValuesDefinition();
        }
    }
    public class CustomValue {
        public String color;
        public Boolean default_x;
        public String description;
        public Boolean isActive;
        public String label;
        public String valueName;
        private String[] color_type_info = new String[]{'color','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] default_x_type_info = new String[]{'default','urn:metadata.tooling.soap.sforce.com',null,'1','1','false'};
        private String[] description_type_info = new String[]{'description','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] isActive_type_info = new String[]{'isActive','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] label_type_info = new String[]{'label','urn:metadata.tooling.soap.sforce.com',null,'0','1','false'};
        private String[] valueName_type_info = new String[]{'valueName','urn:metadata.tooling.soap.sforce.com',null,'1','1','false'};
        private String[] apex_schema_type_info = new String[]{'urn:metadata.tooling.soap.sforce.com','true','false'};
        private String[] field_order_type_info = new String[]{'color','default_x','description','isActive','label','valueName'};

        public CustomValue() {}
        public CustomValue(String label, String value, Boolean isActive) {
            this.label      = label;
            this.valueName  = value;
            this.isActive   = isActive;
            this.default_x  = false;
        }
    }

}