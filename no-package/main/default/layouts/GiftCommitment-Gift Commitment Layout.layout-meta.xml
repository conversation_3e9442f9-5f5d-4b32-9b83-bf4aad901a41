<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Gift Commitment Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>DonorId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OpportunityId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ScheduleType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EffectiveStartDate</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Status</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Description</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CampaignId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>FormalCommitmentType</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Gift Commitment Fulfillment Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ExpectedTotalCmtAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>TotalPaidTransactionAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastPaidTransactionDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>EffectiveTransactionPeriod</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>NextTransactionDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>WrittenOffAmount</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ExpectedEndDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>TransactionPaymentCount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CurrentGiftCmtScheduleId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>EffectiveTransactionInterval</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>NextTransactionAmount</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Gift Commitment Revenue Summary</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TotalCurrentMonth</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TotalCurrentYear</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TotalCurrentQuarter</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TotalNextYear</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>OwnerId</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedDate</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <platformActionList>
        <actionListContext>Record</actionListContext>
        <platformActionListItems>
            <actionName>PrintableView</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>0</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>ManageSchedule</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>1</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>GiftCommitment.Manage_Default_Designations</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>2</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>UpgradeDowngrade</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>3</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>PauseSchedule</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>4</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>ResumeSchedule</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>5</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>ChangePaymentMethod</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>6</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>ChangeScheduleDates</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>7</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>CloseCommitment</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>8</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Edit</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>9</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Clone</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>10</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>ChangeOwnerOne</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>11</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Delete</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>12</sortOrder>
        </platformActionListItems>
    </platformActionList>
    <relatedLists>
        <fields>Name</fields>
        <fields>Type</fields>
        <fields>StartDate</fields>
        <fields>EndDate</fields>
        <fields>TransactionPeriod</fields>
        <fields>TransactionAmount</fields>
        <relatedList>GiftCommitmentSchedules</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>Name</fields>
        <fields>CurrentAmount</fields>
        <fields>TransactionDate</fields>
        <fields>Status</fields>
        <relatedList>GiftTransactions</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>Name</fields>
        <fields>GiftDesignation</fields>
        <fields>FundraisingCampaign__c</fields>
        <fields>FundraisingCampaignGoal__c</fields>
        <fields>AllocatedPercentage</fields>
        <relatedList>GiftDefaultDesignations</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>Name</fields>
        <fields>Recipient</fields>
        <fields>PartialPercent</fields>
        <fields>PartialAmount</fields>
        <relatedList>GiftDefaultSoftCredits</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>Name</fields>
        <fields>TributeType</fields>
        <fields>HonoreeContact</fields>
        <fields>HonoreeName</fields>
        <relatedList>GiftTributes</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedFileList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>TASK.SUBJECT</fields>
        <fields>TASK.WHO_NAME</fields>
        <fields>ACTIVITY.TASK</fields>
        <fields>TASK.DUE_DATE</fields>
        <fields>CORE.USERS.FULL_NAME</fields>
        <fields>TASK.LAST_UPDATE</fields>
        <relatedList>RelatedHistoryList</relatedList>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00hRu0000095ijB</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>0</sizeY>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
