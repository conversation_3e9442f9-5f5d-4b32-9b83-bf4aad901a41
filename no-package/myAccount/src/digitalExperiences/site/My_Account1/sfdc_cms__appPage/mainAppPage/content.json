{"type": "sfdc_cms__appPage", "title": "main", "contentBody": {"currentThemeId": "Build_Your_Own_LWR", "headMarkup": "<title>My Account</title>\n\n<!-- branding stylesheets -->\n\n<link rel=\"stylesheet\" href=\"{ basePath }/assets/styles/salesforce-lightning-design-system.min.css?{ versionKey }\"/>\n<link rel=\"stylesheet\" href=\"{ basePath }/assets/styles/dxp-site-spacing-styling-hooks.min.css?{ versionKey }\" />\n<link rel=\"stylesheet\" href=\"{ basePath }/assets/styles/dxp-styling-hooks.min.css?{ versionKey }\" />\n<link rel=\"stylesheet\" href=\"{ basePath }/assets/styles/dxp-slds-extensions.min.css?{ versionKey }\" />\n\n\n<!-- branding stylesheets-->\n\n<link rel=\"stylesheet\" href=\"{ styles/styles.css }\" />\n<link rel=\"stylesheet\" href=\"{ styles/print.css }\" media=\"print\"/>\n\n<script src=\"https://www.google.com/recaptcha/api.js?render={SITE_KEY_HERE}\"></script>\n\n\n<style>\n   \t@font-face {\n      font-family: 'Peak';\n      src: url('{ basePath }/sfsites/c/resource/ESPeak_Light') format('woff2');\n      letter-spacing:0 !important;\n    }\n    @font-face {\n      font-family: 'Peak Semibold';\n      src: url('{ basePath }/sfsites/c/resource/ESPeak_Semibold') format('woff2');\n      letter-spacing:0 !important;\n      font-weight:400 !important;\n    }\n    @font-face {\n      font-family: 'Peak Bold';\n      src: url('{ basePath }/sfsites/c/resource/ESPeak_Bold') format('woff2');\n      letter-spacing:0 !important;\n      font-weight:500 !important;\n    }\n</style>", "isLockerServiceEnabled": false, "isRelaxedCSPLevel": true, "templateName": "talon-template-byo"}, "urlName": "main"}