/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * myAccountFlowDisplay
 * @description:
 * @author: <PERSON><PERSON>
 * @date: 4/16/25
 */

import {LightningElement, api} from 'lwc';
import stepOneTemplate from './templates/stepOne.html';
import stepTwoTemplate from './templates/stepTwo.html';
import stepThreeTemplate from './templates/stepThree.html';
import stepFourTemplate from './templates/stepFour.html';
import stepFiveTemplate from './templates/stepFive.html';

export default class MyAccountFlowDisplay extends LightningElement {
    @api accountId;
    @api currentStep;
    @api bookingData = {
        school: null,
        program: null,
        session: null,
        dates: [],
        students: []
    };
    @api schools;
    @api bookingState;

    templateMap = {
        1: stepOneTemplate,
        2: stepTwoTemplate,
        3: stepThreeTemplate,
        4: stepFourTemplate,
        5: stepFiveTemplate,
    };

    render() {
        return this.templateMap[this.currentStep] || stepOneTemplate;
    }

    get students() {
        return this.bookingData?.students || [];
    }

    /**
     * Validates the current step
     * @param {Number} stepNumber - The step number to validate
     * @returns {Promise<Boolean>} Promise resolving to true if the step is valid
     */
    @api
    validateStep(stepNumber) {
        return new Promise((resolve) => {
            let stepComponent;

            switch (stepNumber) {
                case 1:
                    // School selection validation
                    stepComponent = this.refs.schoolSelection;
                    if (stepComponent && typeof stepComponent.validate === 'function') {
                        resolve(stepComponent.validate());
                    } else {
                        resolve(true);
                    }
                    break;

                case 2:
                    // Program selection validation
                    stepComponent = this.refs.programSelection;
                    if (stepComponent && typeof stepComponent.validate === 'function') {
                        resolve(stepComponent.validate());
                    } else {
                        resolve(true);
                    }
                    break;

                case 3:
                    // Survey form - special handling to trigger submission
                    stepComponent = this.refs.surveyForm;

                    if (stepComponent && typeof stepComponent.validate === 'function') {
                        const isValid = stepComponent.validate();

                        if (isValid && typeof stepComponent.submitForm === 'function') {
                            stepComponent.submitForm();
                            resolve(false);

                        } else {
                            resolve(isValid);
                        }
                    } else {
                        resolve(true);
                    }
                    break;

                case 4:
                    // Checkout validation
                    stepComponent = this.refs.checkout;
                    if (stepComponent && typeof stepComponent.validate === 'function') {
                        resolve(stepComponent.validate());
                    } else {
                        resolve(true);
                    }
                    break;

                default:
                    // No validation needed for other steps
                    resolve(true);
            }
        });
    }
}
