/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * myAccountExternalConstants
 * @description: Constants for unpackaged components that extend the My Account functionality
 * @author: <PERSON><PERSON>
 * @date: 4/20/25
 */

/**
 * External pages that are not part of the base package
 */
const getExternalPages = () => {
    return {
        FIELD_TRIP_BOOKING: 'fieldtripbooking',
    };
};

/**
 * External actions that can be triggered from packaged components
 */
const getExternalActions = () => {
    return {
        BOOK_FIELD_TRIP: 'book_field_trip',
    };
};

export {
    getExternalPages,
    getExternalActions,
};