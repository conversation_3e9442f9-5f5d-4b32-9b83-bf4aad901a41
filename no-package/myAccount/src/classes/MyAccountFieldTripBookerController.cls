/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * MyAccountFieldTripBookerController
 * @description: Controller for field trip booking functionality in My Account
 * @author: <PERSON><PERSON>
 * @date: 4/28/25
 */

public with sharing class MyAccountFieldTripBookerController {

	/**
	 * Gets available events grouped by day for easy display in program selection
	 * @return List of EventSessionsByDay objects containing grouped sessions
	 */
	@AuraEnabled(cacheable=true)
	public static List<TixtrackEventData.EventSessionsByDay> getAvailableEventsGroupedByDay(List<String> specificDates) {
		try {
			return MyAccountTicketureBookingService.getAvailableEventsGroupedByDay(specificDates, 'Field Trips');
		} catch (Exception e) {
			System.debug('Error getting available events: ' + e.getMessage() + '\n' + e.getStackTraceString());
			throw new AuraHandledException('Error retrieving available events: ' + e.getMessage());
		}
	}
}