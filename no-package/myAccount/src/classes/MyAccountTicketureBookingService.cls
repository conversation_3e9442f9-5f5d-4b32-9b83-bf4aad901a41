/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * MyAccountTicketureBookingService
 * @description Service class for handling Ticketure booking operations for My Account
 */
public without sharing class MyAccountTicketureBookingService {

	/**
	 * Creates or retrieves Ticketure identities for both user and organization
	 * @param sessionToken The user's authentication token
	 * @param accountId Optional account ID for a school/organization
	 * @return Map<String, String> containing both identities (keys: 'userIdentityId', 'orgIdentityId')
	 */
	@AuraEnabled
	public static Map<String, String> getTicketureIdentities(String sessionToken, String accountId) {
		IdentitiesResult result = getTicketureIdentitiesWithRecords(sessionToken, accountId);

		try {
			if (!result.recordsToUpdate.isEmpty()) {
				update result.recordsToUpdate;
			}

			Map<String, String> identities = new Map<String, String>();
			identities.put('userIdentityId', result.userIdentityId);
			if (String.isNotBlank(result.orgIdentityId)) {
				identities.put('orgIdentityId', result.orgIdentityId);
			}
			return identities;
		} catch (Exception e) {
			throw new AuraHandledException('Error creating Ticketure identities: ' + e.getMessage());
		}
	}

	/**
	 * Creates or retrieves a Ticketure identity for the current user
	 * @param sessionToken The user's authentication token
	 * @return IdentityResult containing the identity ID and any record that needs updating
	 */
	private static IdentityResult createOrGetUserTicketureIdentity(String sessionToken) {
		try {
			flmas.MyAccountMsgs.UserModel user = flmas.MyAccountUserService.getCurrentUser(sessionToken);

			if (user == null || String.isEmpty(user.Id)) {
				throw new AuraHandledException('Invalid session or user not found');
			}

			if (String.isNotBlank(user.ticketureIdentityId)) {
				return new IdentityResult(user.ticketureIdentityId, null);
			}

			Map<String, Object> requestBody = new Map<String, Object>{
					'first_name' => user.firstName,
					'last_name' => user.lastName,
					'email' => user.email
			};

			tixtrack.TixSyncAPI api = tixtrack.TixSyncAPI.setupAPI();
			String response = api.postPath('identity', JSON.serialize(requestBody));

			IdentityResponse identityResponse = (IdentityResponse) JSON.deserialize(response, IdentityResponse.class);

			if (String.isNotBlank(identityResponse.id)) {
				Contact contactToUpdate = new Contact(
						Id = user.Id,
						tixtrack__TicketureIdentityId__c = identityResponse.id
				);
				return new IdentityResult(identityResponse.id, contactToUpdate);
			} else {
				throw new AuraHandledException('Failed to create Ticketure identity: Identity ID not found in response');
			}
		} catch (Exception e) {
			throw new AuraHandledException('Error creating user Ticketure identity: ' + e.getMessage());
		}
	}

	/**
	 * Creates or retrieves a Ticketure identity for an organization
	 * @param accountId The account ID of the organization
	 * @return IdentityResult containing the identity ID and any record that needs updating
	 */
	public static IdentityResult createOrGetOrganizationTicketureIdentity(String accountId) {
		try {
			Account org = [
					SELECT Id, Name, tixtrack__TicketureOrganizationId__c, tixtrack__TicketureIdentityId__c,
							BillingStreet, BillingCity, BillingState, BillingPostalCode, BillingCountry
					FROM Account
					WHERE Id = :accountId
					LIMIT 1
			];

			if (String.isNotBlank(org.tixtrack__TicketureIdentityId__c)) {
				return new IdentityResult(org.tixtrack__TicketureIdentityId__c, null);
			}

			String organizationId = org.tixtrack__TicketureOrganizationId__c;
			String identityId = org.tixtrack__TicketureIdentityId__c;
			Boolean needsUpdate = false;

			tixtrack.TixSyncAPI api = tixtrack.TixSyncAPI.setupAPI();

			// Create organization if needed
			if (String.isBlank(organizationId)) {
				Map<String, Object> orgRequestBody = new Map<String, Object>{
						'name' => org.Name
				};

				// Add address if available
				if (String.isNotBlank(org.BillingStreet)) {
					String address = org.BillingStreet;
					if (String.isNotBlank(org.BillingCity)) {
						address += ', ' + org.BillingCity;
					}
					if (String.isNotBlank(org.BillingState)) {
						address += ', ' + org.BillingState;
					}
					if (String.isNotBlank(org.BillingPostalCode)) {
						address += ' ' + org.BillingPostalCode;
					}
					if (String.isNotBlank(org.BillingCountry)) {
						address += ', ' + org.BillingCountry;
					}
					orgRequestBody.put('address', address);
				}

				String orgResponse = api.postPath('organization?_embed=identity', JSON.serialize(orgRequestBody));

				System.debug('orgResponse: ' + orgResponse);

				OrganizationResponse organizationResponse = (OrganizationResponse) JSON.deserialize(
						orgResponse,
						OrganizationResponse.class
				);

				if (organizationResponse.organization != null &&
						organizationResponse.organization.x_data != null &&
						!organizationResponse.organization.x_data.isEmpty()) {
					organizationId = organizationResponse.organization.x_data[0].ID;
				}

				if (organizationResponse.identity != null &&
						organizationResponse.identity.x_data != null &&
						!organizationResponse.identity.x_data.isEmpty()) {
					identityId = organizationResponse.identity.x_data[0].id;
					organizationId = organizationResponse.identity.x_data[0].organization_id;
				}

				if (String.isBlank(organizationId)) {
					throw new TixtrackWebAPIException('Failed to create Ticketure organization');
				}
				org.tixtrack__TicketureOrganizationId__c = organizationId;

				if (String.isNotBlank(identityId)) {
					org.tixtrack__TicketureIdentityId__c = identityId;
				}
				needsUpdate = true;
			}

			if (String.isBlank(identityId)) {
				Map<String, Object> identityRequestBody = new Map<String, Object>{
						'name' => org.Name,
						'organization_id' => organizationId
				};

				String identityResponse = api.postPath('identity', JSON.serialize(identityRequestBody));
				IdentityResponse identityResponseObj = (IdentityResponse) JSON.deserialize(
						identityResponse,
						IdentityResponse.class
				);
				identityId = identityResponseObj.id;

				if (String.isBlank(identityId)) {
					throw new TixtrackWebAPIException('Failed to create Ticketure identity for organization');
				}

				org.tixtrack__TicketureIdentityId__c = identityId;
				needsUpdate = true;
			}

			return new IdentityResult(identityId, needsUpdate ? org : null);
		} catch (Exception e) {
			throw new AuraHandledException('Error creating organization Ticketure identity: ' + e.getMessage());
		}
	}

	/**
	 * Helper method to get identities and collect records that need updating
	 * @param sessionToken The user's authentication token
	 * @param accountId Optional account ID for a school/organization
	 * @return IdentitiesResult containing identities and records to update
	 */
	private static IdentitiesResult getTicketureIdentitiesWithRecords(String sessionToken, String accountId) {
		IdentitiesResult result = new IdentitiesResult();

		try {
			// Get user identity and any records that need updating
			IdentityResult userResult = createOrGetUserTicketureIdentity(sessionToken);
			result.userIdentityId = userResult.identityId;
			if (userResult.recordToUpdate != null) {
				result.recordsToUpdate.add(userResult.recordToUpdate);
			}

			if (String.isNotBlank(accountId)) {
				// Get organization identity and any records that need updating
				IdentityResult orgResult = createOrGetOrganizationTicketureIdentity(accountId);
				result.orgIdentityId = orgResult.identityId;
				if (orgResult.recordToUpdate != null) {
					result.recordsToUpdate.add(orgResult.recordToUpdate);
				}
			}

			return result;
		} catch (Exception e) {
			throw new AuraHandledException('Error creating Ticketure identities: ' + e.getMessage());
		}
	}

	/**
	 * Creates a new cart in Ticketure
	 * @param sessionToken The user's authentication token
	 * @param accountId Optional account ID for a school/organization
	 * @return CartResponse containing cart details
	 */
	@AuraEnabled
	public static CartResponse createCart(String sessionToken, String accountId) {
		try {
			// Get identities and collect records that need updating
			IdentitiesResult identitiesResult = getTicketureIdentitiesWithRecords(sessionToken, accountId);

			Map<String, Object> requestBody = new Map<String, Object>{
					'channel_id' => 'salesforce',
					'contact_identity_id' => identitiesResult.userIdentityId
			};

			if (String.isNotBlank(identitiesResult.orgIdentityId)) {
				requestBody.put('identity_id', identitiesResult.orgIdentityId);
			} else {
				requestBody.put('identity_id', identitiesResult.userIdentityId);
			}

			tixtrack.TixSyncAPI api = tixtrack.TixSyncAPI.setupAPI();
			String response = api.postPath('cart', JSON.serialize(requestBody));

			CartApiResponse cartApiResponse = (CartApiResponse) JSON.deserialize(response, CartApiResponse.class);

			CartResponse cartResponse = new CartResponse();
			cartResponse.cartId = cartApiResponse.id;
			cartResponse.expiresAt = cartApiResponse.expires_at;

			// Now that the callout is complete, perform any pending DML operations
			if (!identitiesResult.recordsToUpdate.isEmpty()) {
				try {
					update identitiesResult.recordsToUpdate;
				} catch (Exception e) {
					System.debug('Error updating records with Ticketure IDs: ' + e.getMessage());
					// We don't throw here because the cart was successfully created
				}
			}

			return cartResponse;
		} catch (Exception e) {
			throw new AuraHandledException('Error creating cart: ' + e.getMessage());
		}
	}

	/**
	 * Adds tickets to a cart
	 * @param sessionToken The user's authentication token
	 * @param cartId The Ticketure cart ID
	 * @param ticketRequests List of ticket requests containing ticket type ID, face value, and quantity
	 * @param additionalInfoList List of additional info objects with default values
	 * @return ReserveResponse containing reservation details and ticket information
	 */
	@AuraEnabled
	public static ReserveResponse addTicketsToCart(
			String sessionToken,
			String cartId,
			List<TicketRequest> ticketRequests,
			List<Object> additionalInfoList
	) {
		try {
			flmas.MyAccountUserService.getCurrentUser(sessionToken);
			List<Object> tickets = new List<Object>();

			for (TicketRequest request : ticketRequests) {
				Integer quantity = request.quantity != null ? Integer.valueOf(request.quantity) : 1;
				for (Integer i = 0; i < quantity; i++) {
					Map<String, Object> ticket = new Map<String, Object>{
							'ticket_type_id' => request.ticketTypeId,
							'face_value' => String.isNotBlank(request.faceValue) ? String.valueOf(request.faceValue) : '0'
					};

					if (String.isNotBlank(request.eventSessionId)) {
						ticket.put('event_session_id', request.eventSessionId);
					}

					if (request.additionalProperties != null) {
						for (String key : request.additionalProperties.keySet()) {
							ticket.put(key, request.additionalProperties.get(key));
						}
					}

					tickets.add(ticket);
				}
			}

			Map<String, Object> requestBody = new Map<String, Object>{
					'tickets' => tickets
			};

			if (additionalInfoList != null && !additionalInfoList.isEmpty()) {
				requestBody.put('additional_info', additionalInfoList);
			}

			tixtrack.TixSyncAPI api = tixtrack.TixSyncAPI.setupAPI();
			String response = api.postPath('staff/cart/' + cartId + '/reserve', JSON.serialize(requestBody));

			String transformedResponse = transformJsonResponse(response);

			ReserveResponse reserveResponse;
			reserveResponse = (ReserveResponse) JSON.deserialize(transformedResponse, ReserveResponse.class);

			reserveResponse.success = true;
			reserveResponse.cartId = cartId;

			return reserveResponse;
		} catch (Exception e) {
			String errorMessage = e.getMessage();

			if (errorMessage.contains('{') && errorMessage.contains('}')) {
				String jsonPart = errorMessage.substring(errorMessage.indexOf('{'), errorMessage.lastIndexOf('}') + 1);

				String transformedJson = transformJsonResponse(jsonPart);

				TicketureErrorResponse errorResponse = (TicketureErrorResponse) JSON.deserialize(
						transformedJson,
						TicketureErrorResponse.class
				);
				String userFriendlyError = errorResponse.getFirstErrorDescription();
				throw new AuraHandledException(userFriendlyError);
			}
			throw new AuraHandledException('Error adding tickets to cart: ' + errorMessage);
		}
	}

	/**
	 * Removes tickets from a cart
	 * @param sessionToken The user's authentication token
	 * @param cartId The Ticketure cart ID
	 * @param ticketIds List of ticket IDs to remove
	 * @return Boolean indicating success
	 */
	@AuraEnabled
	public static Boolean removeTicketsFromCart(
			String sessionToken,
			String cartId,
			List<String> ticketIds
	) {
		try {
			flmas.MyAccountUserService.getCurrentUser(sessionToken);

			Map<String, Object> requestBody = new Map<String, Object>{
					'tickets' => ticketIds
			};

			tixtrack.TixSyncAPI api = tixtrack.TixSyncAPI.setupAPI();
			String response = api.postPath('cart/' + cartId + '/remove', JSON.serialize(requestBody));

			return response != null;
		} catch (Exception e) {
			throw new AuraHandledException('Error removing tickets from cart: ' + e.getMessage());
		}
	}

	/**
	 * Updates a cart with additional information (survey responses)
	 * @param sessionToken The user's authentication token
	 * @param cartId The Ticketure cart ID
	 * @param ticketGroupId The ticket group ID
	 * @param surveyResponses Map of survey question keys to answers
	 * @return Boolean indicating success
	 */
	@AuraEnabled
	public static TixtrackEventData.SessionResponse updateCartWithSurveyResponses(String sessionToken, String cartId, String ticketGroupId, Map<String, Object> surveyResponses) {
		try {
			flmas.MyAccountUserService.getCurrentUser(sessionToken);

			Map<String, Object> additionalInfo = new Map<String, Object>{
					'ticket_group_id' => ticketGroupId,
					'data' => surveyResponses
			};

			List<Object> additionalInfoList = new List<Object>{
					additionalInfo
			};

			Map<String, Object> requestBody = new Map<String, Object>{
					'additional_info' => additionalInfoList
			};

			System.debug(requestBody);

			tixtrack.TixSyncAPI api = tixtrack.TixSyncAPI.setupAPI();
			String endpoint = tixtrack.TixSettings.getSettings().syncEndpoint + '/v1/cart/' + cartId;

			System.debug(endpoint);
//			String endpoint = tixtrack.TixSettings.getSettings().cmsURL + '/api/cart/' + cartId;

			System.debug(JSON.serializePretty(requestBody));

			Map<String, Object> args = new Map<String, Object>{
					'theBody' => JSON.serialize(requestBody),
					'endpoint' => endpoint,
					'httpVerb' => 'PATCH',
					'headers' => null
			};
//			tixtrack.TixSyncAPI api = tixtrack.TixSyncAPI.setupAPI();
//			String response = api.putPath('cart/' + cartId, JSON.serialize(requestBody));

//			try {
			HttpResponse response = (HttpResponse) api.call('doCallout', args);
			System.debug(response);
			System.debug(response.getBody());
				String transformedResult = transformJsonResponse(response.getBody());
////
				return (TixtrackEventData.SessionResponse) JSON.deserialize(
						transformedResult, TixtrackEventData.SessionResponse.class
				);
		} catch (Exception e) {
			System.debug('Error calling Tixtrack API: ' + e.getMessage() + '\n' + e.getStackTraceString());
			throw new TixtrackWebAPIException('Error retrieving session data: ' + e.getMessage());
		}
//		} catch (Exception e) {
//			throw new AuraHandledException('Error updating cart with survey responses: ' + e.getMessage());
//		}
//		return true;
	}

	/**
	 * Books a cart (completes the checkout process)
	 * @param sessionToken The user's authentication token
	 * @param cartId The Ticketure cart ID
	 * @param sendEmail Whether to send a receipt email
	 * @return BookResponse containing booking details
	 */
	@AuraEnabled
	public static BookResponse bookCart(String sessionToken, String cartId, Boolean sendEmail) {
		try {
			flmas.MyAccountUserService.getCurrentUser(sessionToken);

			Map<String, Object> requestBody = new Map<String, Object>{
					'send_email' => sendEmail
			};

			tixtrack.TixSyncAPI api = tixtrack.TixSyncAPI.setupAPI();
			String response = api.postPath('cart/' + cartId + '/book', JSON.serialize(requestBody));

			Map<String, Object> responseMap = (Map<String, Object>) JSON.deserializeUntyped(response);

			BookResponse bookResponse = new BookResponse();
			bookResponse.success = true;
			bookResponse.orderId = (String) responseMap.get('order_id');

			return bookResponse;
		} catch (Exception e) {
			throw new AuraHandledException('Error booking cart: ' + e.getMessage());
		}
	}

	/**
     * Retrieves available events from the Tixtrack API
     * @param specificDates Optional list of specific dates to filter events
     * @param category Optional category to filter events
     * @return EventDataResponse containing available events
     */
	public static TixtrackEventData.EventDataResponse getAvailableEvents(
			List<String> specificDates,
			String category
	) {
		Map<String, Object> params = new Map<String, Object>();

		params.put('_embed', 'ticket_group,ticket_type,meta,ticket_group.meta,ticket_type.meta');

		if (specificDates != null && !specificDates.isEmpty()) {
			params.put('_ondate', formatDateForAPI(specificDates[0]));
		} else {
			params.put('event_session.start_datetime._gt', Datetime.now().format('yyyy-MM-dd\'T\'HH:mm:ss\'Z\''));
		}

		if (String.isNotBlank(category)) {
			params.put('event_template.category', EncodingUtil.urlEncode(category, 'UTF-8'));
		}

		String result = tixtrack.TixSyncAPI.setupAPI().getPath('staff/events/available', params);
		String transformedResult = transformJsonResponse(result);

		try {
			return (TixtrackEventData.ConcreteEventDataResponse) JSON.deserialize(
					transformedResult, TixtrackEventData.ConcreteEventDataResponse.class
			);
		} catch (JSONException e) {
			throw new TixtrackWebAPIException('Error deserializing response: ' + e.getMessage());
		}
	}

	/**
	 * Retrieves event sessions for a specific event template and ticket group
	 * @param eventTemplateId The ID of the event template
	 * @param ticketGroupId The ID of the ticket group
	 * @param rowLimit Optional rowLimit for the number of sessions to retrieve (default 1000)
	 * @return SessionResponse containing event sessions for the specified ticket group
	 */
	public static TixtrackEventData.SessionResponse getEventSessionsByTicketGroup(
			String eventTemplateId,
			String ticketGroupId,
			Integer rowLimit
	) {
		if (rowLimit == null) {
			rowLimit = 1000;
		}

		String encodedTicketGroupId = EncodingUtil.urlEncode(ticketGroupId, 'UTF-8');
		String encodedEventTemplateId = EncodingUtil.urlEncode(eventTemplateId, 'UTF-8');

		String endpoint = tixtrack.TixSettings.getSettings().webURL + '/cached_api/events/' + encodedEventTemplateId + '/sessions';

		Map<String, String> queryParams = new Map<String, String>();
		queryParams.put('ticket_group_id', encodedTicketGroupId);
		queryParams.put('oversold_out', 'false');
		queryParams.put('_limit', String.valueOf(rowLimit));

		Boolean firstParam = true;
		for (String key : queryParams.keySet()) {
			endpoint += (firstParam ? '?' : '&') + key + '=' + queryParams.get(key);
			firstParam = false;
		}

		Map<String, Object> args = new Map<String, Object>{
				'theBody' => null,
				'endpoint' => endpoint,
				'httpVerb' => 'GET',
				'headers' => null
		};

		try {
			HttpResponse response = (HttpResponse) tixtrack.TixSyncAPI.setupAPI().call('doCallout', args);

			String transformedResult = transformJsonResponse(response.getBody());

			return (TixtrackEventData.SessionResponse) JSON.deserialize(
					transformedResult, TixtrackEventData.SessionResponse.class
			);
		} catch (Exception e) {
			System.debug('Error calling Tixtrack API: ' + e.getMessage() + '\n' + e.getStackTraceString());
			throw new TixtrackWebAPIException('Error retrieving session data: ' + e.getMessage());
		}
	}

	/**
	 * Gets available events grouped by day for field trip booking
	 * @param specificDates Optional list of specific dates to filter events
	 * @param category The category of events to retrieve (e.g., 'Field Trips')
	 * @return List of EventSessionsByDay objects containing events grouped by day
	 */
	public static List<TixtrackEventData.EventSessionsByDay> getAvailableEventsGroupedByDay(
			List<String> specificDates,
			String category
	) {
		try {
			// First, get all event templates and ticket groups
			TixtrackEventData.EventDataResponse baseResponse = getAvailableEvents(specificDates, category);

			// Create enhanced response to store sessions by ticket group
			TixtrackEventData.EnhancedEventDataResponse enhancedResponse =
					new TixtrackEventData.EnhancedEventDataResponse(baseResponse);

			// For each event template and ticket group, get the sessions
			if (baseResponse.event_template != null && baseResponse.event_template.x_data != null &&
					baseResponse.ticket_group != null && baseResponse.ticket_group.x_data != null) {

				// Group ticket groups by event template for efficiency
				Map<String, List<TixtrackEventData.TicketGroup>> ticketGroupsByTemplate =
						new Map<String, List<TixtrackEventData.TicketGroup>>();

				for (TixtrackEventData.TicketGroup ticketGroup : baseResponse.ticket_group.x_data) {
					if (!ticketGroupsByTemplate.containsKey(ticketGroup.event_template_id)) {
						ticketGroupsByTemplate.put(ticketGroup.event_template_id,
								new List<TixtrackEventData.TicketGroup>());
					}
					ticketGroupsByTemplate.get(ticketGroup.event_template_id).add(ticketGroup);
				}

				// For each event template, get sessions for each ticket group
				for (TixtrackEventData.EventTemplate template : baseResponse.event_template.x_data) {
					if (ticketGroupsByTemplate.containsKey(template.id)) {
						for (TixtrackEventData.TicketGroup ticketGroup : ticketGroupsByTemplate.get(template.id)) {
							// Get sessions for this ticket group
							TixtrackEventData.SessionResponse sessionResponse =
									getEventSessionsByTicketGroup(template.id, ticketGroup.id, 1000);

							if (sessionResponse != null && sessionResponse.event_session != null &&
									sessionResponse.event_session.x_data != null) {
								// Add sessions to the enhanced response
								enhancedResponse.addSessionsForTicketGroup(
										ticketGroup.id, sessionResponse.event_session.x_data);
							}
						}
					}
				}
			}

			// Get sessions organized by ticket group and day
			List<TixtrackEventData.EventSessionsByDay> result = enhancedResponse.getSessionsWithTemplatesByTicketGroup();

			// Sort the results by date
			result.sort();

			return result;

		} catch (Exception e) {
			System.debug('Error getting available events: ' + e.getMessage() + '\n' + e.getStackTraceString());
			throw new TixtrackWebAPIException('Error retrieving available events: ' + e.getMessage());
		}
	}

	/**
	 * Formats a date string to yyyy-MM-dd format for API requests
	 * @param dateStr The date string to format
	 * @return A properly formatted date string in yyyy-MM-dd format
	 */
	private static String formatDateForAPI(String dateStr) {
		if (String.isBlank(dateStr)) {
			// Return today's date as fallback
			Date today = Date.today();
			return today.year() + '-' +
					String.valueOf(today.month()).leftPad(2, '0') + '-' +
					String.valueOf(today.day()).leftPad(2, '0');
		}

		// If the date contains a timestamp, strip it off
		if (dateStr.contains('T')) {
			dateStr = dateStr.split('T')[0];
		}

		// Validate the date format
		Pattern datePattern = Pattern.compile('^\\d{4}-\\d{2}-\\d{2}$');
		if (datePattern.matcher(dateStr).matches()) {
			return dateStr; // Already in correct format
		}

		try {
			// Try to parse and reformat if it's not in the correct format
			Date parsedDate = Date.valueOf(dateStr);
			return parsedDate.year() + '-' +
					String.valueOf(parsedDate.month()).leftPad(2, '0') + '-' +
					String.valueOf(parsedDate.day()).leftPad(2, '0');
		} catch (Exception e) {
			// If parsing fails, use current date as fallback
			Date today = Date.today();
			return today.year() + '-' +
					String.valueOf(today.month()).leftPad(2, '0') + '-' +
					String.valueOf(today.day()).leftPad(2, '0');
		}
	}

	/**
	 * Enum for datetime comparison operators
	 */
	public enum DateTimeOperator {
		EQUALS,      // =
		GREATER,     // >
		GREATER_EQ,  // >=
		LESS,        // <
		LESS_EQ      // <=
	}

	/**
	 * Helper method to convert DateTimeOperator to API suffix
	 */
	private String getOperatorSuffix(DateTimeOperator op) {
		if (op == null) {
			return '';
		}

		switch on op {
			when EQUALS {
				return '';
			}
			when GREATER {
				return '._gt';
			}
			when GREATER_EQ {
				return '._gte';
			}
			when LESS {
				return '._lt';
			}
			when LESS_EQ {
				return '._lte';
			}
			when else {
				return '';
			}
		}
	}

	/**
	 * Transforms API response by replacing underscore-prefixed properties with x_ prefixed properties
	 * and handling reserved keywords
	 * @param jsonResponse The original JSON response string
	 * @return The transformed JSON string
	 */
	private static String transformJsonResponse(String jsonResponse) {
		if (String.isBlank(jsonResponse)) {
			return jsonResponse;
		}

		String transformed = jsonResponse.replace('"_total":', '"x_total":')
				.replace('"_count":', '"x_count":')
				.replace('"_data":', '"x_data":')
				.replace('"_rank":', '"x_rank":')
				.replace('"_status":', '"x_status":')
				.replace('"_description":', '"x_description":')
				.replace('"_context":', '"x_context":')
				.replace('"_code":', '"x_code":')
				.replace('"enum":', '"x_enum":');

		return transformed;
	}

	/**
	 * Custom exception class for TixtrackWebAPI errors
	 */
	public class TixtrackWebAPIException extends Exception {
	}

	/**
	 * Response class for cart creation
	 */
	public class CartResponse {
		@AuraEnabled public String cartId;
		@AuraEnabled public String expiresAt;
	}

	/**
	 * Response class for reserve operations
	 */
	public class ReserveResponse {
		@AuraEnabled public Boolean success { get; set; }
		@AuraEnabled public String cartId { get; set; }

		@AuraEnabled public CartData cart { get; set; }
		@AuraEnabled public TicketData ticket { get; set; }

		public ReserveResponse() {
			this.success = false;
		}
	}

	/**
	 * Class representing the cart data in the response
	 */
	public class CartData {
		@AuraEnabled public Integer x_count { get; set; }
		@AuraEnabled public List<CartItem> x_data { get; set; }
		@AuraEnabled public Integer x_total { get; set; }
	}

	/**
	 * Class representing a cart item
	 */
	public class CartItem {
		@AuraEnabled public String cart_number { get; set; }
		@AuraEnabled public String channel_id { get; set; }
		@AuraEnabled public String expires_at { get; set; }
		@AuraEnabled public Integer expires_in { get; set; }
		@AuraEnabled public String id { get; set; }
		@AuraEnabled public String identity_id { get; set; }
		@AuraEnabled public Boolean invalid { get; set; }
		@AuraEnabled public Boolean managed { get; set; }
		@AuraEnabled public String name { get; set; }
		@AuraEnabled public String portal_id { get; set; }
		@AuraEnabled public String state { get; set; }
	}

	/**
	 * Class representing the ticket data in the response
	 */
	public class TicketData {
		@AuraEnabled public Integer x_count { get; set; }
		@AuraEnabled public List<TicketItem> x_data { get; set; }
		@AuraEnabled public Integer x_total { get; set; }
	}

	/**
	 * Class representing a ticket item
	 */
	public class TicketItem {
		@AuraEnabled public String id { get; set; }
		@AuraEnabled public String portal_id { get; set; }
		@AuraEnabled public String seller_id { get; set; }
		@AuraEnabled public String cart_id { get; set; }
		@AuraEnabled public String scan_code { get; set; }
		@AuraEnabled public String admit_name { get; set; }
		@AuraEnabled public String admit_email { get; set; }
		@AuraEnabled public String ticket_type_id { get; set; }
		@AuraEnabled public String ticket_group_id { get; set; }
		@AuraEnabled public String event_template_id { get; set; }
		@AuraEnabled public String event_session_id { get; set; }
		@AuraEnabled public String identity_id { get; set; }
		@AuraEnabled public String created_identity_id { get; set; }
		@AuraEnabled public String currency_code { get; set; }
		@AuraEnabled public String face_value { get; set; }
		@AuraEnabled public String adjusted_value { get; set; }
		@AuraEnabled public String state { get; set; }
		@AuraEnabled public Integer redeemed_count { get; set; }
		@AuraEnabled public String start_datetime { get; set; }
		@AuraEnabled public String end_datetime { get; set; }
		@AuraEnabled public String handler { get; set; }
		@AuraEnabled public String venue_id { get; set; }
		@AuraEnabled public String timezone { get; set; }
		@AuraEnabled public String category { get; set; }
		@AuraEnabled public Boolean dedicated_order { get; set; }
	}

	/**
	 * Response class for cart booking
	 */
	public class BookResponse {
		@AuraEnabled public Boolean success;
		@AuraEnabled public String orderId;
	}

	/**
	 * Class representing a ticket request
	 */
	public class TicketRequest {
		@AuraEnabled public String ticketTypeId { get; set; }
		@AuraEnabled public String faceValue { get; set; }
		@AuraEnabled public String quantity { get; set; }
		@AuraEnabled public Map<String, Object> additionalProperties { get; set; }
		@AuraEnabled public String eventSessionId { get; set; }

		public TicketRequest() {
		}

		public TicketRequest(String ticketTypeId, String faceValue, Integer quantity, Map<String, Object> additionalProperties) {
			this.ticketTypeId = ticketTypeId;
			this.faceValue = faceValue;
			this.quantity = String.valueOf(quantity);
			this.additionalProperties = additionalProperties;
		}

		public TicketRequest(String ticketTypeId, String faceValue, Integer quantity, Map<String, Object> additionalProperties, String eventSessionId) {
			this.ticketTypeId = ticketTypeId;
			this.faceValue = faceValue;
			this.quantity = String.valueOf(quantity);
			this.additionalProperties = additionalProperties;
			this.eventSessionId = eventSessionId;
		}
	}

	/**
 * Class to hold identity information and any record that needs updating
 */
	public class IdentityResult {
		public String identityId;
		public SObject recordToUpdate;

		public IdentityResult(String identityId, SObject recordToUpdate) {
			this.identityId = identityId;
			this.recordToUpdate = recordToUpdate;
		}
	}

	/**
	 * Class to hold multiple identity results
	 */
	public class IdentitiesResult {
		public String userIdentityId;
		public String orgIdentityId;
		public List<SObject> recordsToUpdate;

		public IdentitiesResult() {
			this.recordsToUpdate = new List<SObject>();
		}
	}

	/**
	 * Class to represent an identity response from Ticketure API
	 */
	private class IdentityResponse {
		public String id;
		public String organization_id;
		public String name;
		public String email;
		public String first_name;
		public String last_name;
	}

	/**
	 * Class to represent embedded identity data in organization response
	 */
	private class EmbeddedIdentityData {
		public Integer x_count;
		public List<IdentityResponse> x_data;
		public Integer x_total;
	}

	/**
	 * Class to represent embedded organization data in organization response
	 */
	private class EmbeddedOrganizationData {
		public Integer x_count;
		public List<OrganizationData> x_data;
		public Integer x_total;
	}

	/**
	 * Class to represent organization data
	 */
	private class OrganizationData {
		public String ID;
		public String Name;
		public String Address;
	}

	/**
	 * Class to represent an organization response from Ticketure API
	 */
	private class OrganizationResponse {
		public EmbeddedOrganizationData organization;
		public EmbeddedIdentityData identity;
	}

	/**
	 * Class to represent a cart response from Ticketure API
	 */
	private class CartApiResponse {
		public String id;
		public String expires_at;
	}

	/**
	 * Class to represent Ticketure API error responses
	 */
	private class TicketureErrorResponse {
		public Integer HTTPStatusCode;
		public String x_status;
		public List<TicketureError> x_data;
		public Integer x_count;

		public String getFirstErrorDescription() {
			if (x_data != null && !x_data.isEmpty() && x_data[0].x_description != null) {
				return x_data[0].x_description;
			}
			return 'Unknown error occurred';
		}
	}

	/**
	 * Class to represent individual error in Ticketure API response
	 */
	private class TicketureError {
		public String x_description;
		public String x_context;
		public String x_code;
	}
}
