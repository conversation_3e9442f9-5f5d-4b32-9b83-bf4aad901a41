<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Build_Contact_record</name>
        <label>Build Contact record</label>
        <locationX>176</locationX>
        <locationY>431</locationY>
        <assignmentItems>
            <assignToReference>Get_Related_Contact.MyAccountPersona__c</assignToReference>
            <operator>AddItem</operator>
            <value>
                <stringValue>Educator</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Contact</targetReference>
        </connector>
    </assignments>
    <environments>Default</environments>
    <interviewLabel>My Account Auth Session: Update related contact {!$Flow.CurrentDateTime}</interviewLabel>
    <label>My Account Auth Session: Update related contact</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Related_Contact</name>
        <label>Get Related Contact</label>
        <locationX>176</locationX>
        <locationY>323</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Build_Contact_record</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Contact__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Contact</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Contact</name>
        <label>Update Contact</label>
        <locationX>176</locationX>
        <locationY>539</locationY>
        <inputReference>Get_Related_Contact</inputReference>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Related_Contact</targetReference>
        </connector>
        <object>MyAccountAuthSession__c</object>
        <recordTriggerType>Create</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Obsolete</status>
</Flow>
