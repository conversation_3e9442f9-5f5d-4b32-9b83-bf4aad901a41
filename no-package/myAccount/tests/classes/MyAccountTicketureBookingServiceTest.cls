/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Test class for MyAccountTicketureBookingService
 * <AUTHOR>
 * @date 1/15/25
 */
@IsTest
private class MyAccountTicketureBookingServiceTest {
    
    /**
     * Test data setup method
     */
    @TestSetup
    static void makeData() {
        MyAccountTestHarness.createTestData();
    }
    
    /**
     * Test successful booking creation
     */
    @IsTest
    static void testCreateBookingSuccess() {
        // Get test data
        MyAccountAuthSession__c authSession = [SELECT SessionToken__c FROM MyAccountAuthSession__c LIMIT 1];
        Account testOrg = [SELECT Id FROM Account WHERE Type = 'Organization' LIMIT 1];
        
        // Create test booking request
        Map<String, Object> bookingRequest = new Map<String, Object>{
            'eventId' => 'test_event_123',
            'quantity' => 2,
            'ticketType' => 'General Admission',
            'organizationId' => testOrg.Id,
            'eventDate' => Date.today().addDays(30),
            'contactEmail' => '<EMAIL>'
        };
        
        Test.startTest();
        String result = MyAccountTicketureBookingService.createBooking(
            authSession.SessionToken__c, 
            JSON.serialize(bookingRequest)
        );
        Test.stopTest();
        
        // Assertions
        System.assertNotEquals(null, result, 'Result should not be null');
        System.assertNotEquals('', result, 'Result should not be empty');
    }
    
    /**
     * Test booking creation with invalid session token
     */
    @IsTest
    static void testCreateBookingInvalidToken() {
        // Create test booking request
        Map<String, Object> bookingRequest = new Map<String, Object>{
            'eventId' => 'test_event_123',
            'quantity' => 2,
            'ticketType' => 'General Admission'
        };
        
        Test.startTest();
        try {
            String result = MyAccountTicketureBookingService.createBooking(
                'invalid_token', 
                JSON.serialize(bookingRequest)
            );
            System.assert(false, 'Expected AuraHandledException to be thrown');
        } catch (AuraHandledException e) {
            System.assert(e.getMessage().contains('Invalid session') || e.getMessage().contains('Session not found'), 
                         'Error message should indicate invalid session: ' + e.getMessage());
        }
        Test.stopTest();
    }
    
    /**
     * Test booking creation with null session token
     */
    @IsTest
    static void testCreateBookingNullToken() {
        // Create test booking request
        Map<String, Object> bookingRequest = new Map<String, Object>{
            'eventId' => 'test_event_123',
            'quantity' => 2
        };
        
        Test.startTest();
        try {
            String result = MyAccountTicketureBookingService.createBooking(
                null, 
                JSON.serialize(bookingRequest)
            );
            System.assert(false, 'Expected AuraHandledException to be thrown');
        } catch (AuraHandledException e) {
            System.assert(e.getMessage().contains('Session token') || e.getMessage().contains('required'), 
                         'Error message should indicate token is required: ' + e.getMessage());
        }
        Test.stopTest();
    }
    
    /**
     * Test booking creation with invalid booking request
     */
    @IsTest
    static void testCreateBookingInvalidRequest() {
        // Get test data
        MyAccountAuthSession__c authSession = [SELECT SessionToken__c FROM MyAccountAuthSession__c LIMIT 1];
        
        Test.startTest();
        try {
            String result = MyAccountTicketureBookingService.createBooking(
                authSession.SessionToken__c, 
                'invalid_json'
            );
            System.assert(false, 'Expected AuraHandledException to be thrown');
        } catch (AuraHandledException e) {
            System.assert(e.getMessage().contains('Invalid') || e.getMessage().contains('request') || e.getMessage().contains('JSON'), 
                         'Error message should indicate invalid request: ' + e.getMessage());
        }
        Test.stopTest();
    }
    
    /**
     * Test booking creation with missing required fields
     */
    @IsTest
    static void testCreateBookingMissingFields() {
        // Get test data
        MyAccountAuthSession__c authSession = [SELECT SessionToken__c FROM MyAccountAuthSession__c LIMIT 1];
        
        // Create incomplete booking request (missing eventId)
        Map<String, Object> bookingRequest = new Map<String, Object>{
            'quantity' => 2,
            'ticketType' => 'General Admission'
        };
        
        Test.startTest();
        try {
            String result = MyAccountTicketureBookingService.createBooking(
                authSession.SessionToken__c, 
                JSON.serialize(bookingRequest)
            );
            System.assert(false, 'Expected AuraHandledException to be thrown');
        } catch (AuraHandledException e) {
            System.assert(e.getMessage().contains('required') || e.getMessage().contains('missing') || e.getMessage().contains('eventId'), 
                         'Error message should indicate missing required fields: ' + e.getMessage());
        }
        Test.stopTest();
    }
    
    /**
     * Test successful retrieval of user bookings
     */
    @IsTest
    static void testGetUserBookingsSuccess() {
        // Get test data
        MyAccountAuthSession__c authSession = [SELECT SessionToken__c FROM MyAccountAuthSession__c LIMIT 1];
        
        Test.startTest();
        List<MyAccountMsgs.BookingModel> result = MyAccountTicketureBookingService.getUserBookings(authSession.SessionToken__c);
        Test.stopTest();
        
        // Assertions
        System.assertNotEquals(null, result, 'Result should not be null');
        // Result could be empty list if no bookings exist, which is valid
    }
    
    /**
     * Test retrieval of user bookings with invalid session token
     */
    @IsTest
    static void testGetUserBookingsInvalidToken() {
        Test.startTest();
        try {
            List<MyAccountMsgs.BookingModel> result = MyAccountTicketureBookingService.getUserBookings('invalid_token');
            System.assert(false, 'Expected AuraHandledException to be thrown');
        } catch (AuraHandledException e) {
            System.assert(e.getMessage().contains('Invalid session') || e.getMessage().contains('Session not found'), 
                         'Error message should indicate invalid session: ' + e.getMessage());
        }
        Test.stopTest();
    }
    
    /**
     * Test successful booking cancellation
     */
    @IsTest
    static void testCancelBookingSuccess() {
        // Get test data
        MyAccountAuthSession__c authSession = [SELECT SessionToken__c FROM MyAccountAuthSession__c LIMIT 1];
        Account testOrg = [SELECT Id FROM Account WHERE Type = 'Organization' LIMIT 1];
        
        // Create test booking request
        Map<String, Object> bookingRequest = new Map<String, Object>{
            'eventId' => 'test_event_123',
            'quantity' => 1,
            'organizationId' => testOrg.Id
        };
        
        Test.startTest();
        String bookingId = MyAccountTicketureBookingService.createBooking(
            authSession.SessionToken__c, 
            JSON.serialize(bookingRequest)
        );
        
        // Now cancel the booking
        MyAccountTicketureBookingService.cancelBooking(authSession.SessionToken__c, bookingId);
        Test.stopTest();
        
        // Assertions - verify booking was cancelled
        // The specific assertion will depend on your implementation
        System.assertNotEquals(null, bookingId, 'Booking ID should not be null');
    }
    
    /**
     * Test booking cancellation with invalid booking ID
     */
    @IsTest
    static void testCancelBookingInvalidId() {
        // Get test data
        MyAccountAuthSession__c authSession = [SELECT SessionToken__c FROM MyAccountAuthSession__c LIMIT 1];
        
        Test.startTest();
        try {
            MyAccountTicketureBookingService.cancelBooking(authSession.SessionToken__c, 'invalid_booking_id');
            System.assert(false, 'Expected AuraHandledException to be thrown');
        } catch (AuraHandledException e) {
            System.assert(e.getMessage().contains('Booking not found') || e.getMessage().contains('Invalid booking') || e.getMessage().contains('not found'), 
                         'Error message should indicate booking not found: ' + e.getMessage());
        }
        Test.stopTest();
    }
    
    /**
     * Test booking modification
     */
    @IsTest
    static void testModifyBookingSuccess() {
        // Get test data
        MyAccountAuthSession__c authSession = [SELECT SessionToken__c FROM MyAccountAuthSession__c LIMIT 1];
        Account testOrg = [SELECT Id FROM Account WHERE Type = 'Organization' LIMIT 1];
        
        // Create initial booking
        Map<String, Object> bookingRequest = new Map<String, Object>{
            'eventId' => 'test_event_123',
            'quantity' => 1,
            'organizationId' => testOrg.Id
        };
        
        Test.startTest();
        String bookingId = MyAccountTicketureBookingService.createBooking(
            authSession.SessionToken__c, 
            JSON.serialize(bookingRequest)
        );
        
        // Modify the booking
        bookingRequest.put('quantity', 3);
        MyAccountTicketureBookingService.modifyBooking(
            authSession.SessionToken__c, 
            bookingId, 
            JSON.serialize(bookingRequest)
        );
        Test.stopTest();
        
        // Assertions - verify booking was modified
        System.assertNotEquals(null, bookingId, 'Booking ID should not be null');
    }
    
    /**
     * Test bulk operations performance
     */
    @IsTest
    static void testBulkBookingOperations() {
        // Get test data
        MyAccountAuthSession__c authSession = [SELECT SessionToken__c FROM MyAccountAuthSession__c LIMIT 1];
        Account testOrg = [SELECT Id FROM Account WHERE Type = 'Organization' LIMIT 1];
        
        List<String> bookingIds = new List<String>();
        
        Test.startTest();
        // Create multiple bookings
        for (Integer i = 0; i < 5; i++) {
            Map<String, Object> bookingRequest = new Map<String, Object>{
                'eventId' => 'test_event_' + i,
                'quantity' => i + 1,
                'organizationId' => testOrg.Id
            };
            
            String bookingId = MyAccountTicketureBookingService.createBooking(
                authSession.SessionToken__c, 
                JSON.serialize(bookingRequest)
            );
            bookingIds.add(bookingId);
        }
        Test.stopTest();
        
        // Assertions
        System.assertEquals(5, bookingIds.size(), 'Should have created 5 bookings');
        
        // Verify governor limits weren't exceeded
        System.assert(Limits.getDMLStatements() < Limits.getLimitDMLStatements(), 'Should not exceed DML limits');
        System.assert(Limits.getQueries() < Limits.getLimitQueries(), 'Should not exceed query limits');
    }
    
    /**
     * Test error handling with database exceptions
     */
    @IsTest
    static void testDatabaseExceptionHandling() {
        // Get test data
        MyAccountAuthSession__c authSession = [SELECT SessionToken__c FROM MyAccountAuthSession__c LIMIT 1];
        
        // Create booking request with invalid organization ID to trigger exception
        Map<String, Object> bookingRequest