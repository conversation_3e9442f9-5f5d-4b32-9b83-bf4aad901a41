/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Test class for MyAccountFieldTripBookerController
 * <AUTHOR>
 * @date 1/15/25
 */
@IsTest
private class MyAccountFieldTripBookerControllerTest {
    
    /**
     * Test data setup method
     */
    @TestSetup
    static void makeData() {
        MyAccountTestHarness.createTestData();
    }
    
    /**
     * Test successful retrieval of field trip booker data
     */
    @IsTest
    static void testGetFieldTripBookerSuccess() {
        // Get test data
        MyAccountAuthSession__c authSession = [SELECT SessionToken__c FROM MyAccountAuthSession__c LIMIT 1];
        
        Test.startTest();
        MyAccountMsgs.FieldTripBookerModel result = MyAccountFieldTripBookerController.getFieldTripBooker(authSession.SessionToken__c);
        Test.stopTest();
        
        // Assertions
        System.assertNotEquals(null, result, 'Result should not be null');
        System.assertNotEquals(null, result.user, 'User should not be null');
        System.assertEquals('Test', result.user.firstName, 'First name should match');
        System.assertEquals('User', result.user.lastName, 'Last name should match');
        System.assertEquals('<EMAIL>', result.user.email, 'Email should match');
    }
    
    /**
     * Test field trip booker retrieval with invalid session token
     */
    @IsTest
    static void testGetFieldTripBookerInvalidToken() {
        Test.startTest();
        try {
            MyAccountMsgs.FieldTripBookerModel result = MyAccountFieldTripBookerController.getFieldTripBooker('invalid_token');
            System.assert(false, 'Expected AuraHandledException to be thrown');
        } catch (AuraHandledException e) {
            System.assert(e.getMessage().contains('Invalid session') || e.getMessage().contains('Session not found'),
                         'Error message should indicate invalid session: ' + e.getMessage());
        }
        Test.stopTest();
    }
    
    /**
     * Test field trip booker retrieval with null session token
     */
    @IsTest
    static void testGetFieldTripBookerNullToken() {
        Test.startTest();
        try {
            MyAccountMsgs.FieldTripBookerModel result = MyAccountFieldTripBookerController.getFieldTripBooker(null);
            System.assert(false, 'Expected AuraHandledException to be thrown');
        } catch (AuraHandledException e) {
            System.assert(e.getMessage().contains('Session token') || e.getMessage().contains('required'),
                         'Error message should indicate token is required: ' + e.getMessage());
        }
        Test.stopTest();
    }
    
    /**
     * Test field trip booker retrieval with expired session token
     */
    @IsTest
    static void testGetFieldTripBookerExpiredToken() {
        // Get test data and expire the session
        MyAccountAuthSession__c authSession = [SELECT Id, SessionToken__c FROM MyAccountAuthSession__c LIMIT 1];
        authSession.ExpiresAt__c = DateTime.now().addMinutes(-1); // Expire 1 minute ago
        update authSession;
        
        Test.startTest();
        try {
            MyAccountMsgs.FieldTripBookerModel result = MyAccountFieldTripBookerController.getFieldTripBooker(authSession.SessionToken__c);
            System.assert(false, 'Expected AuraHandledException to be thrown');
        } catch (AuraHandledException e) {
            System.assert(e.getMessage().contains('Session expired') || e.getMessage().contains('expired'),
                         'Error message should indicate session expired: ' + e.getMessage());
        }
        Test.stopTest();
    }
    
    /**
     * Test field trip booker retrieval with user having multiple affiliations
     */
    @IsTest
    static void testGetFieldTripBookerMultipleAffiliations() {
        // Get test data
        Contact testContact = [SELECT Id FROM Contact WHERE Email = '<EMAIL>' LIMIT 1];
        MyAccountAuthSession__c authSession = [SELECT SessionToken__c FROM MyAccountAuthSession__c LIMIT 1];
        
        // Create additional organization and affiliation
        Account additionalOrg = new Account(
            Name = 'Additional Test Organization',
            Type = 'Organization'
        );
        insert additionalOrg;
        
        Affiliation__c additionalAffiliation = new Affiliation__c(
            Contact__c = testContact.Id,
            Organization__c = additionalOrg.Id,
            Role__c = 'Administrator',
            Status__c = 'Current'
        );
        insert additionalAffiliation;
        
        Test.startTest();
        MyAccountMsgs.FieldTripBookerModel result = MyAccountFieldTripBookerController.getFieldTripBooker(authSession.SessionToken__c);
        Test.stopTest();
        
        // Assertions
        System.assertNotEquals(null, result, 'Result should not be null');
        System.assertNotEquals(null, result.user, 'User should not be null');
        System.assertNotEquals(null, result.user.affiliations, 'Affiliations should not be null');
        System.assert(result.user.affiliations.size() >= 2, 'Should have at least 2 affiliations');
    }
    
    /**
     * Test field trip booker retrieval with user having no affiliations
     */
    @IsTest
    static void testGetFieldTripBookerNoAffiliations() {
        // Get test data and delete affiliations
        delete [SELECT Id FROM Affiliation__c];
        MyAccountAuthSession__c authSession = [SELECT SessionToken__c FROM MyAccountAuthSession__c LIMIT 1];
        
        Test.startTest();
        MyAccountMsgs.FieldTripBookerModel result = MyAccountFieldTripBookerController.getFieldTripBooker(authSession.SessionToken__c);
        Test.stopTest();
        
        // Assertions
        System.assertNotEquals(null, result, 'Result should not be null');
        System.assertNotEquals(null, result.user, 'User should not be null');
        System.assertEquals(0, result.user.affiliations.size(), 'Should have 0 affiliations');
    }

    /**
     * Test field trip booker retrieval with database exception
     */
    @IsTest
    static void testGetFieldTripBookerDatabaseException() {
        // Get test data
        MyAccountAuthSession__c authSession = [SELECT SessionToken__c FROM MyAccountAuthSession__c LIMIT 1];

        // Delete the contact to cause a database issue
        delete [SELECT Id FROM Contact WHERE Email = '<EMAIL>'];

        Test.startTest();
        try {
            MyAccountMsgs.FieldTripBookerModel result = MyAccountFieldTripBookerController.getFieldTripBooker(authSession.SessionToken__c);
            // Depending on implementation, this might succeed with null user or throw exception
        } catch (Exception e) {
            // Expected behavior for missing contact
            System.assert(true, 'Exception expected when contact is missing');
        }
        Test.stopTest();
}
}
