/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Test class for MyAccountFieldTripBookerController
 * <AUTHOR>
 * @date 1/15/25
 */
@IsTest
private class MyAccountFieldTripBookerControllerTest {
    
    /**
     * Test data setup method
     */
    @TestSetup
    static void makeData() {
        // Create test user
        User testUser = TestDataFactory.createUser('<EMAIL>', 'Test', 'User');
        insert testUser;
        
        // Create test contact
        Contact testContact = TestDataFactory.createContact('Test', 'Contact', '<EMAIL>');
        testContact.User__c = testUser.Id;
        insert testContact;
        
        // Create test account (organization)
        Account testOrg = TestDataFactory.createAccount('Test Organization', 'Organization');
        insert testOrg;
        
        // Create test affiliation
        Affiliation__c testAffiliation = TestDataFactory.createAffiliation(testContact.Id, testOrg.Id, 'Teacher');
        insert testAffiliation;
        
        // Create test auth session
        MyAccountAuthSession__c authSession = TestDataFactory.createAuthSession(testContact.Id);
        insert authSession;
    }
    
    /**
     * Test successful retrieval of field trip booker data
     */
    @IsTest
    static void testGetFieldTripBookerSuccess() {
        // Get test data
        MyAccountAuthSession__c authSession = [SELECT SessionToken__c FROM MyAccountAuthSession__c LIMIT 1];
        
        Test.startTest();
        MyAccountMsgs.FieldTripBookerModel result = MyAccountFieldTripBookerController.getFieldTripBooker(authSession.SessionToken__c);
        Test.stopTest();
        
        // Assertions
        System.assertNotEquals(null, result, 'Result should not be null');
        System.assertNotEquals(null, result.user, 'User should not be null');
        System.assertEquals('Test', result.user.firstName, 'First name should match');
        System.assertEquals('Contact', result.user.lastName, 'Last name should match');
        System.assertEquals('<EMAIL>', result.user.email, 'Email should match');
    }
    
    /**
     * Test field trip booker retrieval with invalid session token
     */
    @IsTest
    static void testGetFieldTripBookerInvalidToken() {
        Test.startTest();
        try {
            MyAccountMsgs.FieldTripBookerModel result = MyAccountFieldTripBookerController.getFieldTripBooker('invalid_token');
            System.assert(false, 'Expected AuraHandledException to be thrown');
        } catch (AuraHandledException e) {
            System.assert(e.getMessage().contains('Invalid session'), 'Error message should indicate invalid session');
        }
        Test.stopTest();
    }
    
    /**
     * Test field trip booker retrieval with null session token
     */
    @IsTest
    static void testGetFieldTripBookerNullToken() {
        Test.startTest();
        try {
            MyAccountMsgs.FieldTripBookerModel result = MyAccountFieldTripBookerController.getFieldTripBooker(null);
            System.assert(false, 'Expected AuraHandledException to be thrown');
        } catch (AuraHandledException e) {
            System.assert(e.getMessage().contains('Session token is required'), 'Error message should indicate token is required');
        }
        Test.stopTest();
    }
    
    /**
     * Test field trip booker retrieval with expired session token
     */
    @IsTest
    static void testGetFieldTripBookerExpiredToken() {
        // Get test data and expire the session
        MyAccountAuthSession__c authSession = [SELECT Id, SessionToken__c FROM MyAccountAuthSession__c LIMIT 1];
        authSession.ExpiresAt__c = DateTime.now().addMinutes(-1); // Expire 1 minute ago
        update authSession;
        
        Test.startTest();
        try {
            MyAccountMsgs.FieldTripBookerModel result = MyAccountFieldTripBookerController.getFieldTripBooker(authSession.SessionToken__c);
            System.assert(false, 'Expected AuraHandledException to be thrown');
        } catch (AuraHandledException e) {
            System.assert(e.getMessage().contains('Session expired'), 'Error message should indicate session expired');
        }
        Test.stopTest();
    }
    
    /**
     * Test field trip booker retrieval with user having multiple affiliations
     */
    @IsTest
    static void testGetFieldTripBookerMultipleAffiliations() {
        // Get test data
        Contact testContact = [SELECT Id FROM Contact LIMIT 1];
        MyAccountAuthSession__c authSession = [SELECT SessionToken__c FROM MyAccountAuthSession__c LIMIT 1];
        
        // Create additional organization and affiliation
        Account additionalOrg = TestDataFactory.createAccount('Additional Organization', 'Organization');
        insert additionalOrg;
        
        Affiliation__c additionalAffiliation = TestDataFactory.createAffiliation(testContact.Id, additionalOrg.Id, 'Administrator');
        insert additionalAffiliation;
        
        Test.startTest();
        MyAccountMsgs.FieldTripBookerModel result = MyAccountFieldTripBookerController.getFieldTripBooker(authSession.SessionToken__c);
        Test.stopTest();
        
        // Assertions
        System.assertNotEquals(null, result, 'Result should not be null');
        System.assertNotEquals(null, result.user, 'User should not be null');
        System.assertNotEquals(null, result.user.affiliations, 'Affiliations should not be null');
        System.assertEquals(2, result.user.affiliations.size(), 'Should have 2 affiliations');
    }
    
    /**
     * Test field trip booker retrieval with user having no affiliations
     */
    @IsTest
    static void testGetFieldTripBookerNoAffiliations() {
        // Get test data and delete affiliations
        delete [SELECT Id FROM Affiliation__c];
        MyAccountAuthSession__c authSession = [SELECT SessionToken__c FROM MyAccountAuthSession__c LIMIT 1];
        
        Test.startTest();
        MyAccountMsgs.FieldTripBookerModel result = MyAccountFieldTripBookerController.getFieldTripBooker(authSession.SessionToken__c);
        Test.stopTest();
        
        // Assertions
        System.assertNotEquals(null, result, 'Result should not be null');
        System.assertNotEquals(null, result.user, 'User should not be null');
        System.assertEquals(0, result.user.affiliations.size(), 'Should have 0 affiliations');
    }
}