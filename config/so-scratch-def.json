{"orgName": "Foglight Solutions, Inc. - AC-NPC SO", "edition": "Developer", "features": ["AccountingSubledgerGrowthEdition", "IndustriesActionPlan", "AnalyticsQueryService", "PublicSectorAccess", "Fundraising", "IndustriesSalesExcellenceAddOn", "IndustriesServiceExcellenceAddOn", "MarketingUser", "ProgramManagement", "OmniStudioDesigner", "OmniStudioRuntime", "EnableSetPasswordInApi", "PersonAccounts", "ContactsToMultipleAccounts", "PublishExpBuilderBasedSna", "Communities", "StateAndCountryPicklist"], "settings": {"lightningExperienceSettings": {"enableS1DesktopEnabled": true}, "mobileSettings": {"enableS1EncryptedStoragePref2": false}, "chatterSettings": {"enableChatter": true}, "apexSettings": {"enableDisableParallelApexTesting": true}, "enhancedNotesSettings": {"enableEnhancedNotes": true}, "nameSettings": {"enableMiddleName": true, "enableNameSuffix": true}, "productSettings": {"enableQuantitySchedule": true, "enableRevenueSchedule": true}, "IndustriesSettings": {"enableFundraising": true, "enableGroupMembershipPref": true}, "InterestTaggingSettings": {"enableInterestTagging": true}, "customAddressFieldSettings": {"enableCustomAddressField": true}, "AccountSettings": {"enableRelateContactToMultipleAccounts": true}, "experienceBundleSettings": {"enableExperienceBundleMetadata": true}, "communitiesSettings": {"enableNetworksEnabled": true}}}