<?xml version="1.0" encoding="UTF-8"?>
<RelationshipGraphDefinition xmlns="http://soap.sforce.com/2006/04/metadata">
    <isActive>true</isActive>
    <isTemplate>false</isTemplate>
    <masterLabel>Organization Relationship Graph</masterLabel>
    <relationshipGraphDefVersions>
        <graphDefinition>{
  &quot;graph&quot; : {
    &quot;rootNode&quot; : {
      &quot;id&quot; : &quot;a44c64a6-13aa-498e-90db-420c480bcdd2&quot;,
      &quot;object&quot; : {
        &quot;entity&quot; : &quot;Account&quot;
      },
      &quot;configurationType&quot; : &quot;Primary&quot;,
      &quot;nodeUiConfig&quot; : {
        &quot;label&quot; : &quot;Account&quot;,
        &quot;fieldsToDisplay&quot; : [ ],
        &quot;showFieldLabels&quot; : true,
        &quot;actions&quot; : { }
      },
      &quot;childRelationships&quot; : [ {
        &quot;ManyToMany&quot; : {
          &quot;targetObjectNode&quot; : {
            &quot;sortFields&quot; : [ {
              &quot;field&quot; : {
                &quot;field&quot; : &quot;LastModifiedDate&quot;,
                &quot;whichEntity&quot; : &quot;TARGET&quot;
              },
              &quot;order&quot; : &quot;DESC&quot;
            } ],
            &quot;id&quot; : &quot;8372d77e-9ed3-48b1-98e0-9afcad40ecd0&quot;,
            &quot;object&quot; : {
              &quot;entity&quot; : &quot;Account&quot;
            },
            &quot;configurationType&quot; : &quot;Custom&quot;,
            &quot;nodeUiConfig&quot; : {
              &quot;label&quot; : &quot;Alliances&quot;,
              &quot;fieldsToDisplay&quot; : [ {
                &quot;field&quot; : &quot;Name&quot;,
                &quot;whichEntity&quot; : &quot;TARGET&quot;
              }, {
                &quot;field&quot; : &quot;PartyRoleRelation.RoleName&quot;,
                &quot;whichEntity&quot; : &quot;JUNCTION&quot;
              } ],
              &quot;showFieldLabels&quot; : true,
              &quot;actions&quot; : {
                &quot;containerActions&quot; : [ {
                  &quot;action&quot; : &quot;New&quot;
                } ],
                &quot;recordActions&quot; : [ {
                  &quot;action&quot; : &quot;Edit&quot;
                } ],
                &quot;junctionRecordActions&quot; : [ {
                  &quot;action&quot; : &quot;Edit&quot;
                } ]
              }
            },
            &quot;childRelationships&quot; : [ ]
          },
          &quot;relationshipUiConfig&quot; : { },
          &quot;filter&quot; : {
            &quot;filterCriteria&quot; : [ ],
            &quot;booleanFilter&quot; : &quot;&quot;
          },
          &quot;junctionObject&quot; : {
            &quot;entity&quot; : &quot;AccountAccountRelation&quot;
          },
          &quot;sourceField&quot; : {
            &quot;field&quot; : &quot;AccountId&quot;
          },
          &quot;targetField&quot; : {
            &quot;field&quot; : &quot;RelatedAccountId&quot;
          }
        }
      }, {
        &quot;ManyToMany&quot; : {
          &quot;targetObjectNode&quot; : {
            &quot;sortFields&quot; : [ {
              &quot;field&quot; : {
                &quot;field&quot; : &quot;LastModifiedDate&quot;,
                &quot;whichEntity&quot; : &quot;TARGET&quot;
              },
              &quot;order&quot; : &quot;DESC&quot;
            } ],
            &quot;id&quot; : &quot;88db6283-4106-4af2-b2c3-509c24683666&quot;,
            &quot;object&quot; : {
              &quot;entity&quot; : &quot;PersonAccount&quot;
            },
            &quot;configurationType&quot; : &quot;Primary&quot;,
            &quot;nodeUiConfig&quot; : {
              &quot;label&quot; : &quot;Affiliations&quot;,
              &quot;fieldsToDisplay&quot; : [ {
                &quot;field&quot; : &quot;Roles&quot;,
                &quot;whichEntity&quot; : &quot;JUNCTION&quot;
              } ],
              &quot;showFieldLabels&quot; : true,
              &quot;actions&quot; : {
                &quot;containerActions&quot; : [ {
                  &quot;action&quot; : &quot;AddRelation&quot;
                } ],
                &quot;recordActions&quot; : [ ],
                &quot;junctionRecordActions&quot; : [ ]
              }
            },
            &quot;childRelationships&quot; : [ ]
          },
          &quot;relationshipUiConfig&quot; : { },
          &quot;filter&quot; : {
            &quot;filterCriteria&quot; : [ ],
            &quot;booleanFilter&quot; : &quot;&quot;
          },
          &quot;junctionObject&quot; : {
            &quot;entity&quot; : &quot;AccountContactRelation&quot;
          },
          &quot;sourceField&quot; : {
            &quot;field&quot; : &quot;AccountId&quot;
          },
          &quot;targetField&quot; : {
            &quot;field&quot; : &quot;ContactId&quot;
          }
        }
      } ]
    },
    &quot;globalUiConfig&quot; : { }
  }
}</graphDefinition>
        <graphType>HorizontalHierarchy</graphType>
    </relationshipGraphDefVersions>
</RelationshipGraphDefinition>
