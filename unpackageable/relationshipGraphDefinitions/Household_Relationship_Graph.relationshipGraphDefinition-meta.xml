<?xml version="1.0" encoding="UTF-8"?>
<RelationshipGraphDefinition xmlns="http://soap.sforce.com/2006/04/metadata">
    <isActive>true</isActive>
    <isTemplate>false</isTemplate>
    <masterLabel>Household Relationship Graph</masterLabel>
    <relationshipGraphDefVersions>
        <graphDefinition>{
  &quot;graph&quot; : {
    &quot;rootNode&quot; : {
      &quot;id&quot; : &quot;********-39e2-4179-a578-5d0ad88fe3db&quot;,
      &quot;object&quot; : {
        &quot;entity&quot; : &quot;Account&quot;
      },
      &quot;configurationType&quot; : &quot;Primary&quot;,
      &quot;nodeUiConfig&quot; : {
        &quot;label&quot; : &quot;Account&quot;,
        &quot;fieldsToDisplay&quot; : [ ],
        &quot;showFieldLabels&quot; : true,
        &quot;actions&quot; : { }
      },
      &quot;childRelationships&quot; : [ {
        &quot;ManyToMany&quot; : {
          &quot;targetObjectNode&quot; : {
            &quot;sortFields&quot; : [ {
              &quot;field&quot; : {
                &quot;field&quot; : &quot;LastModifiedDate&quot;,
                &quot;whichEntity&quot; : &quot;TARGET&quot;
              },
              &quot;order&quot; : &quot;DESC&quot;
            } ],
            &quot;id&quot; : &quot;b2907158-bf9d-420e-97bb-da53ea279b92&quot;,
            &quot;object&quot; : {
              &quot;entity&quot; : &quot;Contact&quot;
            },
            &quot;configurationType&quot; : &quot;Primary&quot;,
            &quot;nodeUiConfig&quot; : {
              &quot;label&quot; : &quot;Household Members&quot;,
              &quot;fieldsToDisplay&quot; : [ {
                &quot;field&quot; : &quot;Roles&quot;,
                &quot;whichEntity&quot; : &quot;JUNCTION&quot;
              }, {
                &quot;field&quot; : &quot;IsPrimaryMember&quot;,
                &quot;whichEntity&quot; : &quot;JUNCTION&quot;
              } ],
              &quot;showFieldLabels&quot; : true,
              &quot;actions&quot; : {
                &quot;containerActions&quot; : [ {
                  &quot;action&quot; : &quot;AddRelation&quot;
                } ],
                &quot;recordActions&quot; : [ {
                  &quot;action&quot; : &quot;Edit&quot;
                }, {
                  &quot;action&quot; : &quot;Delete&quot;
                } ],
                &quot;junctionRecordActions&quot; : [ {
                  &quot;action&quot; : &quot;EditRelation&quot;
                } ]
              },
              &quot;junctionActionsLabel&quot; : &quot;Edit Relationship&quot;
            },
            &quot;childRelationships&quot; : [ ]
          },
          &quot;relationshipUiConfig&quot; : { },
          &quot;filter&quot; : {
            &quot;filterCriteria&quot; : [ ],
            &quot;booleanFilter&quot; : &quot;&quot;
          },
          &quot;junctionObject&quot; : {
            &quot;entity&quot; : &quot;AccountContactRelation&quot;
          },
          &quot;sourceField&quot; : {
            &quot;field&quot; : &quot;AccountId&quot;
          },
          &quot;targetField&quot; : {
            &quot;field&quot; : &quot;ContactId&quot;
          }
        }
      }, {
        &quot;ManyToMany&quot; : {
          &quot;targetObjectNode&quot; : {
            &quot;sortFields&quot; : [ {
              &quot;field&quot; : {
                &quot;field&quot; : &quot;LastModifiedDate&quot;,
                &quot;whichEntity&quot; : &quot;TARGET&quot;
              },
              &quot;order&quot; : &quot;DESC&quot;
            } ],
            &quot;id&quot; : &quot;91f5bf80-75eb-4095-9cd5-859ffdaac4dc&quot;,
            &quot;object&quot; : {
              &quot;entity&quot; : &quot;Account&quot;
            },
            &quot;configurationType&quot; : &quot;Custom&quot;,
            &quot;nodeUiConfig&quot; : {
              &quot;label&quot; : &quot;Related Accounts&quot;,
              &quot;fieldsToDisplay&quot; : [ {
                &quot;field&quot; : &quot;PartyRoleRelation.RelatedRoleName&quot;,
                &quot;whichEntity&quot; : &quot;JUNCTION&quot;
              } ],
              &quot;showFieldLabels&quot; : true,
              &quot;actions&quot; : {
                &quot;containerActions&quot; : [ {
                  &quot;action&quot; : &quot;New&quot;
                } ],
                &quot;recordActions&quot; : [ {
                  &quot;action&quot; : &quot;Edit&quot;
                } ],
                &quot;junctionRecordActions&quot; : [ {
                  &quot;action&quot; : &quot;Edit&quot;
                } ]
              }
            },
            &quot;childRelationships&quot; : [ ]
          },
          &quot;relationshipUiConfig&quot; : { },
          &quot;filter&quot; : {
            &quot;filterCriteria&quot; : [ ],
            &quot;booleanFilter&quot; : &quot;&quot;
          },
          &quot;junctionObject&quot; : {
            &quot;entity&quot; : &quot;AccountAccountRelation&quot;
          },
          &quot;sourceField&quot; : {
            &quot;field&quot; : &quot;AccountId&quot;
          },
          &quot;targetField&quot; : {
            &quot;field&quot; : &quot;RelatedAccountId&quot;
          }
        }
      } ]
    },
    &quot;globalUiConfig&quot; : { }
  }
}</graphDefinition>
        <graphType>HorizontalHierarchy</graphType>
    </relationshipGraphDefVersions>
</RelationshipGraphDefinition>
