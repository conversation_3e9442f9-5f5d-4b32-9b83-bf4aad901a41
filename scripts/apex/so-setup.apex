/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

List<PermissionSetAssignment> permSetAssigns = new List<PermissionSetAssignment>();
List<PermissionSet> permSets = [
        SELECT Id,
                Name,
                NamespacePrefix,
                IsCustom
        FROM PermissionSet
        WHERE Name = 'GroupMembershipPsl'
        OR (
                NamespacePrefix IN ('tixtrack','flmas')
                AND IsCustom = TRUE
        )
];
for (PermissionSet permSet : permSets) {
    permSetAssigns.add(
            new PermissionSetAssignment(PermissionSetId = permSet.Id, AssigneeId = UserInfo.getUserId())
    );
}
Database.SaveResult[] dsrs = Database.insert(permSetAssigns, false);
Integer i = 0;
for (Database.SaveResult dsr : dsrs) {
    if (!dsr.isSuccess()) {
        System.debug(permSets.get(i).Name);
        System.debug(JSON.serializePretty(dsr.getErrors()));
    }
    i++;
}