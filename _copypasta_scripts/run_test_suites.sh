#!/bin/bash
#
# Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
#
# The code below is part of the Foglight Arts & Culture package and is not
# to be redistributed without express written consent by Foglight.
#

# example:
FORCEAPP_SUITES="force-app/main/default/testSuites/*"
mkdir ./junit
for testSuite in $FORCEAPP_SUITES
do
  printf "Executing test suite %s ... \n" $testSuite
      printf "sfdx apex:test:run -w 10 -r junit -d test-results/apex -s $testSuite\n"
      sfdx apex:test:run -w 10 -r junit -d junit -s $testSuite
      exitCode=$?
      if [[ ! $exitCode == '0' ]]; then
          finalExitCode=$exitCode
      fi
      printf "Last exit code %s\n" $exitCode
  done
  printf "Final exit code %s\n" $finalExitCode
  return $finalExitCode