<?xml version="1.0" encoding="UTF-8"?>
<CustomApplication xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Campaign_Record_Page</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Campaign</pageOrSobjectType>
    </actionOverrides>
    <formFactors>Large</formFactors>
    <isNavAutoTempTabsDisabled>false</isNavAutoTempTabsDisabled>
    <isNavPersonalizationDisabled>false</isNavPersonalizationDisabled>
    <isNavTabPersistenceDisabled>false</isNavTabPersistenceDisabled>
    <isOmniPinnedViewEnabled>false</isOmniPinnedViewEnabled>
    <label>Fundraising Operations</label>
    <navType>Console</navType>
    <tabs>standard-home</tabs>
    <tabs>standard-Account</tabs>
    <tabs>standard-Case</tabs>
    <tabs>standard-GiftBatch</tabs>
    <tabs>standard-GiftEntry</tabs>
    <tabs>standard-GiftCommitment</tabs>
    <tabs>standard-GiftTransaction</tabs>
    <tabs>standard-GiftDesignation</tabs>
    <tabs>standard-Dashboard</tabs>
    <uiType>Lightning</uiType>
    <workspaceConfig>
        <mappings>
            <tab>standard-Account</tab>
        </mappings>
        <mappings>
            <tab>standard-Case</tab>
        </mappings>
        <mappings>
            <tab>standard-Dashboard</tab>
        </mappings>
        <mappings>
            <tab>standard-GiftBatch</tab>
        </mappings>
        <mappings>
            <tab>standard-GiftCommitment</tab>
        </mappings>
        <mappings>
            <tab>standard-GiftDesignation</tab>
        </mappings>
        <mappings>
            <tab>standard-GiftEntry</tab>
        </mappings>
        <mappings>
            <tab>standard-GiftTransaction</tab>
        </mappings>
        <mappings>
            <tab>standard-home</tab>
        </mappings>
    </workspaceConfig>
</CustomApplication>
