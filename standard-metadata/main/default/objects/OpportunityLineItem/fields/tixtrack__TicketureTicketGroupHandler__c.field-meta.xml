<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>tixtrack__TicketureTicketGroupHandler__c</fullName>
    <deprecated>false</deprecated>
    <description>Populated with the ticket group&apos;s handler (membership, admission, etc.)</description>
    <label>Ticketure Ticket Group Handler</label>
    <required>false</required>
    <type>Picklist</type>
    <valueSet>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>tickets</fullName>
                <default>false</default>
                <label>Tickets</label>
            </value>
            <value>
                <fullName>passes</fullName>
                <default>false</default>
                <label>Passes</label>
            </value>
            <value>
                <fullName>seated</fullName>
                <default>false</default>
                <label>Seated</label>
            </value>
            <value>
                <fullName>ledger</fullName>
                <default>false</default>
                <label>Ledger</label>
            </value>
            <value>
                <fullName>donation</fullName>
                <default>false</default>
                <label>Donation</label>
            </value>
            <value>
                <fullName>membership</fullName>
                <default>false</default>
                <label>Membership</label>
            </value>
            <value>
                <fullName>codes</fullName>
                <default>false</default>
                <label>Codes</label>
            </value>
            <value>
                <fullName>fee</fullName>
                <default>false</default>
                <label>Fee</label>
            </value>
        </valueSetDefinition>
    </valueSet>
</CustomField>
