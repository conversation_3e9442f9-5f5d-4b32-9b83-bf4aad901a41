<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>tixtrack__TicketureTransferredFrom__c</fullName>
    <deleteConstraint>SetNull</deleteConstraint>
    <deprecated>false</deprecated>
    <description>When an opportunity line item is moved from one product to another by the TixTrack Connector, the
        previous product is stored here.</description>
    <inlineHelpText>When an opportunity line item is moved from one product to another by the TixTrack Connector, the
        previous product is stored here.</inlineHelpText>
    <label>Ticketure Transferred From</label>
    <referenceTo>Product2</referenceTo>
    <relationshipLabel>Opportunity Product</relationshipLabel>
    <relationshipName>TicketureHistoricalOppLineItems</relationshipName>
    <required>false</required>
    <type>Lookup</type>
</CustomField>
