<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>tixtrack__TicketureNamedMember6Id__c</fullName>
    <deprecated>false</deprecated>
    <description>This will be mapped to scan_code during sync. This represents the existing, or requested, member id.</description>
    <externalId>false</externalId>
    <inlineHelpText>Leaving this blank will cause Tick<PERSON><PERSON> to generate a new member ID for new members, or continue
        using any existing member IDs for existing members. Specify a value here only if you need to override the
        “scan_code” in Ticketure.</inlineHelpText>
    <label>Ticketure Named Member 6 Id</label>
    <length>255</length>
    <required>false</required>
    <trackFeedHistory>false</trackFeedHistory>
    <trackTrending>false</trackTrending>
    <type>Text</type>
    <unique>false</unique>
</CustomField>
