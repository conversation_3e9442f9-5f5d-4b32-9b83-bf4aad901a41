# CREDIT   some parts from: https://circleci.com/developer/orbs/orb/jsc/salesforce#orb-source
version: 2.1
orbs:
  github-cli: circleci/github-cli@2.2.0
description: The build script for the "Arts & Culture Solution by Foglight Solutions" package.
commands:
  auth:
    description: |
      Authenticates with a persistent org (such as a production instance or a sandbox) using a pre-authorized username and JWT.
    steps:
      - run:
          command: |
            #! /bin/bash
            openssl enc -nosalt -aes-256-cbc -d -in assets/server.key.enc -out assets/server.key -base64 -K $DECRYPTION_KEY -iv $DECRYPTION_IV
            sf force:auth:jwt:grant --client-id $HUB_CONSUMER_KEY --jwt-key-file assets/server.key --username $HUB_SFDC_USER --set-default-dev-hub -a hub
          name: "Authenticate Org: $HUB_SFDC_USER"
  install:
    description: |
      Downloads and installs the latest SFDX CLI version from the official Salesforce channels.
    steps:
      - run:
          command: |
            #! /bin/bash
            set -e

            Install() {
                if command -v sfdx &> /dev/null; then
                    exit 0
                fi
                mkdir -p ~/tmp && cd ~/tmp
                wget https://developer.salesforce.com/media/salesforce-cli/sf/channels/stable/sf-linux-x64.tar.xz
                mkdir -p ~/cli/sf
                tar xJf sf-linux-x64.tar.xz -C ~/cli/sf --strip-components 1
                sudo ln -sf ~/cli/sf/bin/sf /bin/sf
            }

            ORB_TEST_ENV="bats-core"
            if [ "${0#*"$ORB_TEST_ENV"}" == "$0" ]; then
                Install
            fi
          name: Install Latest CLI
      - run:
          command: |
            sf version
          name: Verify CLI Installation
jobs:
  build-dev:
    working_directory: ~/ci_app
    docker:
      - image: "cimg/base:stable"
    steps:
      - checkout
      - install
      - auth
      - run:
          name: Create Scratch Org
          command: |
            #Create scratch org w/o pushing code or installing dependencies
            ./_copypasta_scripts/scratch_org_setup.sh -s Foglight-ARTS-CULTURE-NPC-$CIRCLE_BUILD_NUM -w 30 -v "hub" -f config/so-scratch-def.json -d 1 -t
            sf org open --target-org Foglight-ARTS-CULTURE-NPC-$CIRCLE_BUILD_NUM --urlonly
      - run:
          name: Install Dependencies
          command: |
            ./_copypasta_scripts/scratch_org_setup.sh -s Foglight-ARTS-CULTURE-NPC-$CIRCLE_BUILD_NUM -w 30 -v "hub" -f config/so-scratch-def.json -u -n
          no_output_timeout: 60m
      - run:
          name: Push Code
          command: |
            ./_copypasta_scripts/scratch_org_setup.sh -s Foglight-ARTS-CULTURE-NPC-$CIRCLE_BUILD_NUM -w 30 -v "hub" -f config/so-scratch-def.json -u -p -t
          no_output_timeout: 60m
      - run:
          name: Run Apex Tests
          command: |
            mkdir -p ~/junit
            sf apex run --target-org Foglight-ARTS-CULTURE-NPC-$CIRCLE_BUILD_NUM --file scripts/apex/so-setup.apex --json > tmp/so-setup.json
            sf apex run test --targetusername Foglight-ARTS-CULTURE-NPC-$CIRCLE_BUILD_NUM --codecoverage --outputdir ~/junit --resultformat junit --wait 45
          no_output_timeout: 60m
      - run:
          name: Delete Useless NPSP Scratch Org
          command: |
            # sf force:org:delete -u Foglight-ARTS-CULTURE-NPC-$CIRCLE_BUILD_NUM --noprompt
          when: always
      - store_test_results:
          path: ~/junit
      - store_artifacts:
          path: tmp
    resource_class: large
  build-beta-package:
    working_directory: ~/ci_app
    docker:
      - image: "cimg/base:stable"
    steps:
      - add_ssh_keys:
          fingerprints:
            - "SHA256:KLrrhHJRC9HjqzeTzPQwBBav/2hD9A5lPRz1e2ElmEE"
      - checkout
      - install
      - auth
      # Install the gh cli
      - github-cli/install:
          version: 2.24.3
      - run:
          name: Check gh auth status using GH_TOKEN Env Var
          command: |
            gh auth status
      - run:
          name: Create New Base Package Version
          command: |
            mkdir tmp
            #Create package version
            sf package version create --target-dev-hub hub --path force-app/ --definition-file config/packaging-scratch-def.json --branch $CIRCLE_BRANCH --installation-key "$PACKAGE_INSTALL_KEY" --wait 120 --skip-ancestor-check --code-coverage --json > tmp/createpackage.json 
            PACKAGEVERSIONID="$(cat tmp/createpackage.json | jq '.result.SubscriberPackageVersionId' | tr -d '"')"
            echo $PACKAGEVERSIONID
            #Get the package info
            PACKAGEVERSIONINFO="$(sf package:version:list -p "$SFDX_PACKAGE" --created-last-days 0 -v hub --json | jq '.result[] | select(.SubscriberPackageVersionId == "'$PACKAGEVERSIONID'")')"
            echo $PACKAGEVERSIONINFO > tmp/packageversioninfo.txt
            PACKAGEVERSIONNUM="$(echo $PACKAGEVERSIONINFO | jq '.Version' | tr -d '"')"
            echo $PACKAGEVERSIONNUM
            cp sfdx-project.json tmp/sfdx-project.json
            git config user.email "<EMAIL>"
            git config user.name "Eddie Blazer"
            git add sfdx-project.json
            git commit -m "v$PACKAGEVERSIONNUM PkgId: $PACKAGEVERSIONID [skip ci]"
            git tag -a v$PACKAGEVERSIONNUM -m "PkgId: $PACKAGEVERSIONID [skip ci]"
            #echo "pushing new tag"
            #git push origin $CIRCLE_BRANCH --tags
            #git add sfdx-project.json
            #git commit -m "v$PACKAGEVERSIONNUM PkgId: $PACKAGEVERSIONID [skip ci]"
            #echo "pushing sfdx-project and tags"
            git push origin $CIRCLE_BRANCH --tags
          no_output_timeout: 240m
      - run:
          name: Create Scratch Org for Installing Package Version
          command: |
            #todo Setup Scratch Org Snapshots https://help.salesforce.com/s/articleView?id=release-notes.rn_dev_environments_scratch_org_snapshots.htm&release=248&type=5
            #Modify project file to remove namespace, so that the new SO is not 
            #created with a namespace. We don't want to install a namespaced
            #package into an org with the same namespace.
            mv sfdx-project.json tmp/sfdx-project.json
            cat tmp/sfdx-project.json | jq 'del(.namespace)' > sfdx-project.json
            #Create scratch org w/o pushing code or installing dependencies
            ./_copypasta_scripts/scratch_org_setup.sh -s Foglight-ARTS-CULTURE-NPC-PKG-$CIRCLE_BUILD_NUM -w 30 -v "hub" -f config/so-scratch-def.json -d 3
            sf org display --target-org Foglight-ARTS-CULTURE-NPC-PKG-$CIRCLE_BUILD_NUM
            sf org open --target-org Foglight-ARTS-CULTURE-NPC-PKG-$CIRCLE_BUILD_NUM --url-only
            rm sfdx-project.json
            mv tmp/sfdx-project.json ./sfdx-project.json
          no_output_timeout: 45m
      - run:
          name: Install Package Dependencies in QA Org
          command: |
            # TixTrack Connector
            ./_copypasta_scripts/scratch_org_setup.sh -s Foglight-ARTS-CULTURE-NPC-PKG-$CIRCLE_BUILD_NUM -w 30 -v "hub" -f config/so-scratch-def.json -u -n
          no_output_timeout: 60m
      - run:
          name: Install Base package in QA Org
          command: |
            #Get PackageVersionId
            #PackageVersionId=$(<tmp/packageversion.txt)
            PACKAGEVERSIONINFO=$(<tmp/packageversioninfo.txt)
            echo $PACKAGEVERSIONINFO
            PACKAGEVERSIONID="$(echo $PACKAGEVERSIONINFO | jq '.SubscriberPackageVersionId' | tr -d '"')"
            echo $PACKAGEVERSIONID
            PACKAGEVERSIONNUM="$(echo $PACKAGEVERSIONINFO | jq '.Version' | tr -d '"')"
            echo $PACKAGEVERSIONNUM
            #Install package in scratch org
            sf package install --package $PACKAGEVERSIONID --publish-wait 10 --wait 10 --target-org Foglight-ARTS-CULTURE-NPC-PKG-$CIRCLE_BUILD_NUM --no-prompt --security-type AllUsers --installation-key "$PACKAGE_INSTALL_KEY"
          no_output_timeout: 45m
      - run:
          name: Prep Org w/ Default Settings
          command: |
            #./_copypasta_scripts/push_directory.sh -s Foglight-ARTS-CULTURE-NPC-PKG-$CIRCLE_BUILD_NUM -p qa-app > tmp/push_qa-app.json
            ./_copypasta_scripts/scratch_org_setup.sh -s Foglight-ARTS-CULTURE-NPC-PKG-$CIRCLE_BUILD_NUM -w 30 -v "hub" -f config/project-scratch-def.json -u -t
      - store_artifacts:
          path: tmp
  release-package:
    working_directory: ~/ci_app
    docker:
      - image: "cimg/base:stable"
    steps:
      - add_ssh_keys:
          fingerprints:
            - "07:35:aa:4d:9e:b8:fb:17:f5:55:e0:8f:c5:50:69:c8"
      - checkout
      - install
      - auth
      # Install the gh cli
      - github-cli/install:
          version: 2.24.3
      # Authorize the gh cli using SSH key
      - run:
          name: Check gh auth status using GH_TOKEN Env Var
          command: |
            gh auth status
      - run:
          name: Promote Tagged Package Version
          command: |
            mkdir tmp
            echo 'Git Tag: ' $CIRCLE_TAG
            VERSION="$(echo $CIRCLE_TAG | tr -d 'r')"
            echo 'Version: ' $VERSION
            #Get Salesforce's subscriber package version id for the tagged version
            sf package version list --target-dev-hub hub --packages "Arts & Culture by Foglight Solutions (NPC)" --created-last-days 30 --json > tmp/promotepackage.json
            PACKAGEVERSIONID="$(cat tmp/promotepackage.json | jq -r '.result[] | select((.Version == "'$VERSION'") and (.Branch == "master")).SubscriberPackageVersionId')"
            echo $PACKAGEVERSIONID
            sf package version promote --package $PACKAGEVERSIONID --no-prompt
            # Get the release notes
            gh release view $CIRCLE_TAG --json body --repo Foglight-Solutions/$CIRCLE_PROJECT_REPONAME | jq -r '.body' > tmp/ReleaseNotes.txt
            # Append the Install URL
            echo " " >> tmp/ReleaseNotes.txt
            echo "### Package Installation URL" >> tmp/ReleaseNotes.txt
            echo "/packaging/installPackage.apexp?p0=$PACKAGEVERSIONID" >> tmp/ReleaseNotes.txt
            gh release edit $CIRCLE_TAG --notes-file tmp/ReleaseNotes.txt --repo Foglight-Solutions/$CIRCLE_PROJECT_REPONAME
          no_output_timeout: 45m
      - run:
          name: Update sfdx-project.json and Commit
          command: |
            # Update sfdx-project.json flmas-app with the new ancestor Id version
            NEXTVERSION="$(echo $CIRCLE_TAG | tr -d 'r' | awk -F. -v OFS=. '{$2 += 1; print}' | awk -F. '{print $1"."$2"."$3}')"
            echo 'Next Version: ' $NEXTVERSION
            #cat sfdx-project.json | jq '(.packageDirectories[] | select(.path == "force-app") ).ancestorId |= "'$PACKAGEVERSIONID'"' > tmp/sfdx-project2.json
            cat sfdx-project.json | jq '(.packageDirectories[] | select(.path == "force-app") ).versionName |= "ver '$NEXTVERSION'"' > tmp/sfdx-project3.json
            cat tmp/sfdx-project3.json | jq '(.packageDirectories[] | select(.path == "force-app") ).versionNumber |= "'$NEXTVERSION'.NEXT"' > tmp/sfdx-project4.json
            cp tmp/sfdx-project4.json sfdx-project.json
            # Commit & Push to origin
            git config user.email "<EMAIL>"
            git config user.name "Eddie Blazer"
            git add sfdx-project.json
            git commit -m "Increment version [skip ci]"
            PUSH="$(git branch --contains tags/$CIRCLE_TAG | awk '/\*/ {print}' | tr -d "* ")"
            echo 'Push Branch: ' $PUSH
            # this should update the originating branch, but it's hard to determine that when we're on a tag-branch... maybe someday
            git push origin master
      - store_artifacts:
          path: tmp
workflows:
  commit:
    jobs:
      - build-dev:
          filters:
            branches:
              ignore:
                - master
                - qa
      - build-beta-package:
          filters:
            branches:
              only:
                - qa
                - master
      - release-package:
          filters:
            tags:
              only: /^r\d+\.\d+\.\d+.\d+$/
            branches:
              ignore: /.*/
