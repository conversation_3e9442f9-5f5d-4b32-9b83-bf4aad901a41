<?xml version="1.0" encoding="UTF-8"?>
<LightningComponentBundle xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>61.0</apiVersion>
    <description>Designation Editor</description>
    <isExposed>true</isExposed>
    <masterLabel>Designation Editor</masterLabel>
    <targets>
        <target>lightning__FlowScreen</target>
    </targets>
    <targetConfigs>
        <targetConfig targets="lightning__FlowScreen">

            <propertyType name="T" extends="SObject" label="Input Type" description="Generic sObject data type used for input sObject properties." />
            <property label="Records" name="records" type="{T[]}" description="Optional list of SObjects to be passed into the editor, used to read from and save records outside this component. If none are passed in, the Parent Id will be used to retrieve records based on the 'Object API Name' property."/>
            <property label="Records JSON" name="recordsJson" type="String" description="Optional JSON representation of records to be passed into the editor, used to read from and save records outside this component. If none are passed in, the Parent Id will be used to retrieve records based on the 'Object API Name' property."/>
            <property label="Records to Delete" name="recordsToDelete" role="outputOnly" type="{T[]}" />
            <property label="Object API Name" name="objectApiName" role="inputOnly" required="true" type="String" default="GiftTransactionDesignation" description="The API name of the records to be displayed in the editor, i.e., the API name of the records passed into the 'Records' property."/>
            <property label="Parent Id" name="parentId" role="inputOnly" type="String" description="The ID of the parent record used to retrieve existing child records and create new ones. If none is specified, ‘Records’ are expected to be passed in."/>
            <property label="Total Amount" name="totalAmount" role="inputOnly" type="String" description="The total amount the records in the editor should not exceed."/>
            <property label="Field Set Name" name="fieldSetName" role="inputOnly"  type="String" required="true" default="flmas__ManageDesignations" description="API name of the fieldset used to add additional fields to the editor."/>
        </targetConfig>
    </targetConfigs>
</LightningComponentBundle>
