/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * batchGiftEntryExecute
 * @description:
 * @author: <PERSON><PERSON>
 * @date: 11/7/24
 */
import FlowLauncher from "c/flowLauncher";

export default class BatchGiftEntryExecute extends FlowLauncher {

    constructor() {
        super();
        this._handlerClass = 'BatchGiftExecuteFlowLauncher';
        this._params = {
            "isDryRun": false
        }
        this._successMessage = 'The gift batch process is in progress and can take some time.';
        this._failureMessage = 'There was an error executing the gift batch process.';
    }
}