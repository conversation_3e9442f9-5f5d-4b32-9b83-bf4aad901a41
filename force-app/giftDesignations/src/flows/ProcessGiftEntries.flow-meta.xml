<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Create_GiftTransactionDesignations</name>
        <label>Create GiftTransactionDesignations</label>
        <locationX>176</locationX>
        <locationY>650</locationY>
        <actionName>FlowRecordHandler</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Is_Dry_Run2</targetReference>
        </connector>
        <dataTypeMappings>
            <typeName>T__deleteCollection</typeName>
            <typeValue>GiftTransactionDesignation</typeValue>
        </dataTypeMappings>
        <dataTypeMappings>
            <typeName>T__upsertCollection</typeName>
            <typeValue>GiftTransactionDesignation</typeValue>
        </dataTypeMappings>
        <dataTypeMappings>
            <typeName>U__deleteCollection</typeName>
            <typeValue>GiftTransactionDesignation</typeValue>
        </dataTypeMappings>
        <dataTypeMappings>
            <typeName>U__upsertCollection</typeName>
            <typeValue>GiftTransactionDesignation</typeValue>
        </dataTypeMappings>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>UpdateGiftEntryOnFailure</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>deleteCollection</name>
            <value>
                <elementReference>Get_Gift_Transaction_Designation</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>isDryRun</name>
            <value>
                <elementReference>isDryRun</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>objectApiName</name>
            <value>
                <stringValue>GiftTransactionDesignation</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>parentIdField</name>
            <value>
                <stringValue>GiftTransactionId</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>parentIdValue</name>
            <value>
                <elementReference>ProcessGiftEntries.giftTransactionId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>upsertCollectionJSON</name>
            <value>
                <elementReference>Get_Gift_Entry_Record.GiftDesignations__c</elementReference>
            </value>
        </inputParameters>
        <nameSegment>FlowRecordHandler</nameSegment>
        <offset>0</offset>
        <outputParameters>
            <assignToReference>Get_Gift_Entry_Record.GiftDesignations__c</assignToReference>
            <name>upsertCollectionJSON</name>
        </outputParameters>
        <versionSegment>1</versionSegment>
    </actionCalls>
    <actionCalls>
        <name>Process_Ticketure_Membership</name>
        <label>Process Ticketure Membership</label>
        <locationX>176</locationX>
        <locationY>1166</locationY>
        <actionName>MembershipController</actionName>
        <actionType>apex</actionType>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>UpdateGiftEntryOnFailure</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>giftTransactionId</name>
            <value>
                <elementReference>ProcessGiftEntries.giftTransactionId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>isDryRun</name>
            <value>
                <elementReference>isDryRun</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>jsonData</name>
            <value>
                <elementReference>Get_Gift_Entry_Record.TicketureMemberInfo__c</elementReference>
            </value>
        </inputParameters>
        <nameSegment>MembershipController</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
        <versionSegment>1</versionSegment>
    </actionCalls>
    <actionCalls>
        <description>Calls an action that either does a dry run of processing gift entries for the gift entry record that was passed into the flow or processes the gift entries for the gift entry record that was passed into the flow. Stores the results in the Outputs from ProcessGiftEntries action resource.</description>
        <name>ProcessGiftEntries</name>
        <label>Process Gift Entries</label>
        <locationX>176</locationX>
        <locationY>134</locationY>
        <actionName>processGiftEntries</actionName>
        <actionType>processGiftEntries</actionType>
        <connector>
            <targetReference>Get_Gift_Entry_Record</targetReference>
        </connector>
        <faultConnector>
            <targetReference>UpdateGiftEntryOnFailure</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>giftEntryId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>isDryRun</name>
            <value>
                <elementReference>isDryRun</elementReference>
            </value>
        </inputParameters>
        <nameSegment>processGiftEntries</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
        <versionSegment>1</versionSegment>
    </actionCalls>
    <actionCalls>
        <description>Calls an action to do a dry run of the update to or update the record returned by ProcessGiftEntries when ProcessGiftEntries encounters errors, setting the gift processing status to Failure.</description>
        <name>UpdateGiftEntryOnFailure</name>
        <label>Update Gift Entry On Failure</label>
        <locationX>1056</locationX>
        <locationY>242</locationY>
        <actionName>updateProcessedGiftEntries</actionName>
        <actionType>updateProcessedGiftEntries</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>giftEntryId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>giftProcessingStatus</name>
            <value>
                <stringValue>Failure</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>giftProcessingErrorDetails</name>
            <value>
                <elementReference>$Flow.FaultMessage</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>isDryRun</name>
            <value>
                <elementReference>isDryRun</elementReference>
            </value>
        </inputParameters>
        <nameSegment>updateProcessedGiftEntries</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
        <versionSegment>1</versionSegment>
    </actionCalls>
    <actionCalls>
        <description>Calls an action to do a dry run of the update to or update the record returned by ProcessGiftEntries when ProcessGiftEntries finishes without errors.</description>
        <name>UpdateGiftEntryOnSuccess</name>
        <label>Update Gift Entry On Success</label>
        <locationX>176</locationX>
        <locationY>1058</locationY>
        <actionName>updateProcessedGiftEntries</actionName>
        <actionType>updateProcessedGiftEntries</actionType>
        <connector>
            <targetReference>Process_Ticketure_Membership</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>giftEntryId</name>
            <value>
                <elementReference>ProcessGiftEntries.giftEntryId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>giftProcessingStatus</name>
            <value>
                <elementReference>ProcessGiftEntries.giftProcessingStatus</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>giftProcessingErrorDetails</name>
            <value>
                <elementReference>ProcessGiftEntries.giftProcessingErrorDetails</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>giftTransactionId</name>
            <value>
                <elementReference>ProcessGiftEntries.giftTransactionId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>giftCommitmentId</name>
            <value>
                <elementReference>ProcessGiftEntries.giftCommitmentId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>donorId</name>
            <value>
                <elementReference>ProcessGiftEntries.donorId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>isDryRun</name>
            <value>
                <elementReference>isDryRun</elementReference>
            </value>
        </inputParameters>
        <nameSegment>updateProcessedGiftEntries</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
        <versionSegment>1</versionSegment>
    </actionCalls>
    <apiVersion>62.0</apiVersion>
    <decisions>
        <name>Is_Dry_Run2</name>
        <label>Is Dry Run?</label>
        <locationX>176</locationX>
        <locationY>758</locationY>
        <defaultConnector>
            <targetReference>Set_Gift_Designations_JSON_on_Gift_Entry</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>False</defaultConnectorLabel>
        <rules>
            <name>True2</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>isDryRun</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>UpdateGiftEntryOnSuccess</targetReference>
            </connector>
            <label>True</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Dry_Run_1</name>
        <label>Is Dry Run?</label>
        <locationX>176</locationX>
        <locationY>350</locationY>
        <defaultConnector>
            <targetReference>Get_Gift_Transaction_Designation</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>False</defaultConnectorLabel>
        <rules>
            <name>True1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>isDryRun</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Create_GiftTransactionDesignations</targetReference>
            </connector>
            <label>True</label>
        </rules>
    </decisions>
    <description>Does a dry run of processing and updating gift entries for the specified gift entry record or processes and updates the gift entries for the specified gift entry record.</description>
    <environments>Default</environments>
    <interviewLabel>Process Gift Entries {!$Flow.CurrentDateTime}</interviewLabel>
    <isOverridable>true</isOverridable>
    <label>Process Gift Entries</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Gift_Entry_Record</name>
        <label>Get Gift Entry Record</label>
        <locationX>176</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Is_Dry_Run_1</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>GiftEntry</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Retrieve the Gift Transaction Designation records that the Process Gift Entry action just created. It should only be one record.</description>
        <name>Get_Gift_Transaction_Designation</name>
        <label>Get Gift Transaction Designation</label>
        <locationX>264</locationX>
        <locationY>458</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Create_GiftTransactionDesignations</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>GiftTransactionId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>ProcessGiftEntries.giftTransactionId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>GiftTransactionDesignation</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Set_Gift_Designations_JSON_on_Gift_Entry</name>
        <label>Set Gift Designations JSON on Gift Entry</label>
        <locationX>264</locationX>
        <locationY>866</locationY>
        <connector>
            <targetReference>UpdateGiftEntryOnSuccess</targetReference>
        </connector>
        <inputReference>Get_Gift_Entry_Record</inputReference>
    </recordUpdates>
    <sourceTemplate>frops_flow__ProcessGiftEntries</sourceTemplate>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>ProcessGiftEntries</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <description>Indicates whether the gift entry is to be processed as a dry run without creating or updating records to determine if there are any errors.</description>
        <name>isDryRun</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <description>Stores the ID of a gift entry record that is passed into the flow.</description>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
