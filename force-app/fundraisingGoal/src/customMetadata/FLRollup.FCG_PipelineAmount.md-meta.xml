<?xml version="1.0" encoding="UTF-8"?>
<CustomMetadata xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <label>FCG: Pipeline Amount</label>
    <protected>false</protected>
    <values>
        <field>AmountField__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>DateField__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Description__c</field>
        <value xsi:type="xsd:string">Computes the total pipeline amount for fundraising campaign goals based on all non-lost Opportunity designations.</value>
    </values>
    <values>
        <field>DetailObject__c</field>
        <value xsi:type="xsd:string">GiftDefaultDesignation</value>
    </values>
    <values>
        <field>FLRollupFilter__c</field>
        <value xsi:type="xsd:string">GDD_NotClosedOppy</value>
    </values>
    <values>
        <field>FieldToRollUp__c</field>
        <value xsi:type="xsd:string">AllocatedAmount__c</value>
    </values>
    <values>
        <field>IsActive__c</field>
        <value xsi:type="xsd:boolean">true</value>
    </values>
    <values>
        <field>IsDistinctAggregates__c</field>
        <value xsi:type="xsd:boolean">false</value>
    </values>
    <values>
        <field>Mode__c</field>
        <value xsi:type="xsd:string">Scheduled</value>
    </values>
    <values>
        <field>Operation__c</field>
        <value xsi:type="xsd:string">Sum</value>
    </values>
    <values>
        <field>ParentAmountField__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>ParentObject__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>RelationshipPathToParent__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>RelationshipPathToTarget__c</field>
        <value xsi:type="xsd:string">flmas__FundraisingCampaignGoal__r</value>
    </values>
    <values>
        <field>TargetField__c</field>
        <value xsi:type="xsd:string">PipelineAmount__c</value>
    </values>
    <values>
        <field>TargetObject__c</field>
        <value xsi:type="xsd:string">FundraisingCampaignGoal__c</value>
    </values>
    <values>
        <field>TimeFrameNumber__c</field>
        <value xsi:type="xsd:double">0.0</value>
    </values>
    <values>
        <field>TimeFrame__c</field>
        <value xsi:type="xsd:string">AllTime</value>
    </values>
</CustomMetadata>
