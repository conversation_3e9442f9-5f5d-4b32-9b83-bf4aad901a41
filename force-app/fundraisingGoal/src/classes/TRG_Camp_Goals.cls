/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 11/27/24.
 */

public without sharing class TRG_Camp_Goals extends Domain {

    public TRG_Camp_Goals(){}
    public TRG_Camp_Goals(List<Campaign> records) {
        super(records);
    }

    public override void doAfterInsert() {
        buildBudgets(this.triggerRecords, null);
    }

    public override void doAfterUpdate(Map<Id, SObject> oldRecordsMap) {
        buildBudgets(this.triggerRecords, null);
    }

    public static void buildBudgets(List<Campaign> newRecords, Map<Id,Campaign> oldRecordsMap) {
        System.debug('buildBudgets:enter');
        oldRecordsMap = oldRecordsMap ?? new Map<Id,Campaign>();

        List<FundraisingCampaignGoal__c> newFundraisingCampaignBudgets = new List<FundraisingCampaignGoal__c>();
        Map<Id,Integer> countOfFCBs = new Map<Id,Integer>();
        for (Campaign cNew : newRecords) {
            Campaign cOld = oldRecordsMap.get(cNew.Id);

            if ((cOld == null || !cOld.IsFundraisingCampaign__c) && cNew.IsFundraisingCampaign__c && cNew.StartDate != null && cNew.EndDate != null) {
                countOfFCBs.put(cNew.Id, 0);
            }
        }

        // Query to make sure the campaign has no child Fundraising Campaign Budgets
        for (AggregateResult ar : [
                SELECT FundraisingCampaign__c, COUNT(Id) cnt
                FROM FundraisingCampaignGoal__c
                WHERE FundraisingCampaign__c IN :countOfFCBs.keySet()
                GROUP BY FundraisingCampaign__c
        ]) {
            countOfFCBs.put((Id)ar.get('flmas__FundraisingCampaign__c'), (Integer)ar.get('cnt'));
        }

        for (Campaign cNew : newRecords) {
            Campaign cOld = oldRecordsMap.get(cNew.Id);

            if ((cOld == null || (cNew.IsFundraisingCampaign__c && !cOld.IsFundraisingCampaign__c))
                    && (countOfFCBs.get(cNew.Id) ?? 0) == 0
                    && cNew.StartDate != null
                    && cNew.EndDate != null) {
                // The Campaign is new or has been changed to a "Fundraising Campaign" AND has not existing budgets
                // Create some budgets

                // Create a bugdget for each fiscal or calendar year that overlaps with the campaign's start and end dates.
                Integer fiscalStart = DateTimeUtils.getFiscalYear(cNew.StartDate);
                Integer fiscalEnd   = DateTimeUtils.getFiscalYear(cNew.EndDate);

                for (Integer i=0,j=(fiscalEnd-fiscalStart); i<j; i++) {
                    Date budgetStart    = cNew.StartDate.addYears(i);
                    Date budgetEnd      = cNew.StartDate.addYears(i+1).addDays(-1);
                    if (budgetEnd > cNew.EndDate) {
                        budgetEnd = cNew.EndDate;
                    }

                    newFundraisingCampaignBudgets.add(
                            new FundraisingCampaignGoal__c(
                                    FundraisingCampaign__c = cNew.Id,
                                    StartDate__c = budgetStart,
                                    EndDate__c = budgetEnd,
                                    PipelineAmount__c = 0,
                                    CommittedAmount__c = 0,
                                    ReceivedAmount__c = 0,
                                    Name = cNew.Name + ' - ' + (fiscalStart + i)
                            )
                    );
                }
            }
        }

        DB.init(false, false, false).create(newFundraisingCampaignBudgets);
    }
}