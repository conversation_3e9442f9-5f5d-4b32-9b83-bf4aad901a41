/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 11/27/24.
 */

public without sharing class TRG_GDD_CampGoal extends Domain {

    public TRG_GDD_CampGoal(){}
    public TRG_GDD_CampGoal(List<GiftDefaultDesignation> records) {
        super(records);
    }

    public override void doBeforeInsert() {
        setBudget(this.triggerRecords);
    }

    public override void doBeforeUpdate(Map<Id, SObject> oldRecordsMap) {
        setBudget(this.triggerRecords);
    }

    /**
     * Sets the Fundraising Campaign Budget
     * Sets the Amount field
     *
     * @param recordsNew the new records to update
     */
    public static void setBudget(List<GiftDefaultDesignation> recordsNew) {
        System.debug('TRG_GDD_CampBudget.setBudget:enter');

        // Find qualified Fundraising Campaigns
        Map<Id,Campaign> campaignMap = new Map<Id,Campaign>();
        Map<Id,GiftCommitment> commitMap   = new Map<Id,GiftCommitment>();
        Map<Id,Opportunity> oppyMap   = new Map<Id,Opportunity>();
        for (GiftDefaultDesignation gtdNew : recordsNew) {
            System.debug('ParentRecord Type: ' + gtdNew.ParentRecordId?.getSobjectType());
            if (gtdNew.ParentRecordId?.getSobjectType() == GiftCommitment.SObjectType) {
                commitMap.put(gtdNew.ParentRecordId, null);
            } else if (gtdNew.ParentRecordId?.getSobjectType() == Opportunity.SObjectType) {
                oppyMap.put(gtdNew.ParentRecordId, null);
            }

            if (gtdNew.FundraisingCampaign__c != null && gtdNew.FundraisingCampaignGoal__c == null) {
                campaignMap.put(gtdNew.FundraisingCampaign__c, null);
            }
        }

        if (commitMap.size() > 0) {
            commitMap = new Map<Id,GiftCommitment>([
                    SELECT Id, EffectiveStartDate, ExpectedTotalCmtAmount
                    FROM GiftCommitment
                    WHERE Id IN :commitMap.keySet()
            ]);
        }

        if (oppyMap.size() > 0) {
            oppyMap = new Map<Id,Opportunity>([
                    SELECT Id, CloseDate, Amount
                    FROM Opportunity
                    WHERE Id IN :oppyMap.keySet()
            ]);
        }

        if (campaignMap.size() > 0) {
            campaignMap = new Map<Id, Campaign>([
                    SELECT Id, (
                            SELECT Id, StartDate__c, EndDate__c
                            FROM FundraisingCampaignGoals__r
                            ORDER BY StartDate__c DESC
                    )
                    FROM Campaign
                    WHERE Id IN :campaignMap.keySet()
            ]);
        }

        for (GiftDefaultDesignation gtdNew : recordsNew) {
            GiftCommitment gc;
            Opportunity oppy;

            // Set the Amount
            gtdNew.AllocatedAmount__c = 0.0;
            if (gtdNew.ParentRecordId?.getSobjectType() == GiftCommitment.SObjectType) {
                gc = commitMap.get(gtdNew.ParentRecordId);
            } else if (gtdNew.ParentRecordId?.getSobjectType() == Opportunity.SObjectType) {
                oppy = oppyMap.get(gtdNew.ParentRecordId);
            }
            System.debug('Found GiftCommitment: ' + gc);
            System.debug('Found Opportunity: ' + oppy);

            gtdNew.AllocatedAmount__c = (gtdNew.AllocatedPercentage/100) * (gc?.ExpectedTotalCmtAmount ?? oppy?.Amount ?? 0);

            // Set the Fundraising Campaign Budget
            if (gtdNew.FundraisingCampaign__c != null && gtdNew.FundraisingCampaignGoal__c == null) {
                Campaign c = campaignMap.get(gtdNew.FundraisingCampaign__c);

                if ((c?.FundraisingCampaignGoals__r?.size() ?? 0) > 0) {
                    Date evalDate = gc?.EffectiveStartDate ?? oppy?.CloseDate;

                    if (evalDate != null) {
                        // Find the first CampaignBudget with a start date that's less than the Oppy's
                        for (FundraisingCampaignGoal__c fcb : c.FundraisingCampaignGoals__r) {
                            if (fcb.StartDate__c <= evalDate && fcb.EndDate__c >= evalDate) {
                                gtdNew.FundraisingCampaignGoal__c = fcb.Id;
                                break;
                            }
                        }
                    }
                }
            }
        }
    }
}