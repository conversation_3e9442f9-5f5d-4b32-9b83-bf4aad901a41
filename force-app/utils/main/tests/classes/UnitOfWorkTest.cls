/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 7/22/22.
 */
@IsTest(IsParallel=true)
public class UnitOfWorkTest {
    private static Integer contactIdx = 0;

    /**
     * A helper method for other unit tests that need UOW to mock DML operations
     */
    public static void setMock() {
        UnitOfWork.m_DML = new MockDML();
    }

    public class MockDML implements UnitOfWork.IDML {

        public List<DB.DMLResult> doPublish (List<SObject> records) {
            List<DB.DMLResult> results = new List<DB.DMLResult>();
            for (SObject so : records) {
                Database.SaveResult dsr = null;
                DB.DMLResult r = new DB.DMLResult(true, so.Id, '', DB.Action.CREATE, so, String.valueOf(so.getSObjectType()), null, dsr);
                //r.isSuccess = true;
                results.add(r);
            }
            return results;
        }

        public List<DB.DMLResult> doCommit (DB.Action action, List<SObject> records, SObjectField keyField) {
            Database.SaveResult dsr     = (Database.SaveResult)JSON.deserialize('{"success":true}', Database.SaveResult.class);
            Database.UpsertResult dur   = (Database.UpsertResult)JSON.deserialize('{"success":true}', Database.UpsertResult.class);
            Database.DeleteResult ddr   = (Database.DeleteResult)JSON.deserialize('{"success":true}', Database.DeleteResult.class);
            List<DB.DMLResult> results     = new List<DB.DMLResult>();

            for (SObject so : records) {
                DB.DMLResult r;
                switch on action {
                    when CREATE,EDIT {
                        r = new DB.DMLResult(true, null, '', action, so, String.valueOf(so.getSObjectType()), null, dsr);
                        so.Id = IdUtilsTest.generateNextId(so.getSObjectType());
                    }
                    when CREATEOREDIT {
                        r = new DB.DMLResult(true, null, '', action, so, String.valueOf(so.getSObjectType()), null, dur);
                        if (so.Id == null) {
                            so.Id = IdUtilsTest.generateNextId(so.getSObjectType());
                        }
                    }
                    when DESTROY {
                        r = new DB.DMLResult(true, so.Id, '', action, so, String.valueOf(so.getSObjectType()), null, ddr);
                    }
                }

                //r.isSuccess = true;

                results.add(r);
            }
            return results;
        }
    }

    @IsTest
    static void constructor() {
        new UnitOfWork(DB.init());
    }

    @IsTest
    static void create_single() {

        UnitOfWork.INotifyWrapper tpe = createDummyPayload();

        Test.startTest();
        UnitOfWork uow = new UnitOfWork();
        uow.addInsert((Contact)tpe.obj, tpe);
        uow.checkpoint();
        Domain.suppressAll();
        uow.commitWork();

        System.assertEquals(1, Limits.getDmlStatements());
        System.assertEquals(1, Limits.getDmlRows());
        Test.stopTest();

        assertSuccess(tpe);
        System.assertNotEquals(null, ((Contact)tpe.obj).Id);
        Contact c = [
                SELECT Id, FirstName, LastName, Email
                FROM Contact
        ];
        System.assertEquals(((Contact)tpe.obj).Id, c.Id);
    }

    @IsTest
    static void create_multiple() {
        

        List<UnitOfWork.INotifyWrapper> tpes = new List<UnitOfWork.INotifyWrapper>();

        Test.startTest();
        UnitOfWork uow = new UnitOfWork();
        for (Integer i=0; i<50; i++) {
            UnitOfWork.INotifyWrapper tpe = createDummyPayload();
            tpes.add(tpe);
            uow.addInsert((Contact)tpe.obj, tpe);
            uow.checkpoint();
        }
        Domain.suppressAll();
        uow.commitWork();

        System.assertEquals(1, Limits.getDmlStatements());
        System.assertEquals(50, Limits.getDmlRows());
        Test.stopTest();

        for (UnitOfWork.INotifyWrapper tpe : tpes) {
            assertSuccess(tpe);
            System.assertNotEquals(null, ((Contact)tpe.obj).Id);
        }
    }

    /**
     * Create contacts with missing required fields and test that the event is notified appropriately.
     */
    @IsTest
    static void create_error() {
        List<UnitOfWork.INotifyWrapper> tpes = new List<UnitOfWork.INotifyWrapper>();

        Test.startTest();
        UnitOfWork uow = new UnitOfWork();
        for (Integer i=0; i<50; i++) {
            UnitOfWork.INotifyWrapper tpe = createDummyPayload();
            tpes.add(tpe);
            uow.addInsert((Contact)tpe.obj, tpe);
            uow.checkpoint();
        }
        ((Contact)tpes.get(24).obj).LastName = null; // Last name is required. This validates that all are successful but this one
        Domain.suppressAll();
        uow.commitWork();

        System.assertEquals(1, Limits.getDmlStatements());
        System.assertEquals(50, Limits.getDmlRows());
        Test.stopTest();

        for (Integer i=0; i<50; i++) {
            UnitOfWork.INotifyWrapper tpe = tpes.get(i);
            if (i == 24) {
                assertFail(tpe);
            } else {
                assertSuccess(tpe);
                System.assertNotEquals(null, ((Contact)tpe.obj).Id);
            }
        }
    }

    @IsTest
    static void update_single() {
        

        UnitOfWork.INotifyWrapper tpe = createDummyPayload();
        insert (Contact)tpe.obj;

        ((Contact)tpe.obj).FirstName = 'Trial';

        Test.startTest();
        UnitOfWork uow = new UnitOfWork();
        uow.addUpdate((Contact)tpe.obj, tpe);
        uow.checkpoint();
        Domain.suppressAll();
        uow.commitWork();

        System.assertEquals(1, Limits.getDmlStatements());
        System.assertEquals(1, Limits.getDmlRows());
        Test.stopTest();

        Contact c = [
                SELECT Id, FirstName, LastName, Email
                FROM Contact
        ];
        System.assertEquals(((Contact)tpe.obj).Id, c.Id);
        assertSuccess(tpe);
        System.assertEquals('Trial', c.FirstName);
    }

    @IsTest
    static void update_multiple() {
        

        List<UnitOfWork.INotifyWrapper> tpes      = new List<UnitOfWork.INotifyWrapper>();
        List<Contact> contactsToInsert  = new List<Contact>();
        for (Integer i=0; i<50; i++) {
            UnitOfWork.INotifyWrapper tpe = createDummyPayload();
            tpes.add(tpe);
            contactsToInsert.add((Contact)tpe.obj);
        }
        insert contactsToInsert;

        for (Integer i=0; i<50; i++) {
            contactsToInsert.get(i).FirstName = 'Trial' + i;
        }

        Test.startTest();
        UnitOfWork uow = new UnitOfWork();
        for (UnitOfWork.INotifyWrapper tpe : tpes) {
            uow.addUpdate((Contact)tpe.obj, tpe);
            uow.checkpoint();
        }
        Domain.suppressAll();
        uow.commitWork();

        System.assertEquals(1, Limits.getDmlStatements());
        System.assertEquals(50, Limits.getDmlRows());
        Test.stopTest();

        List<Contact> updatedContacts = [
                SELECT Id, FirstName, LastName
                FROM Contact
                ORDER BY Id ASC];
        System.assertEquals(50, updatedContacts.size());
        for (Integer i=0; i<50; i++) {
            UnitOfWork.INotifyWrapper tpe     = tpes.get(i);
            Contact updatedContact  = updatedContacts.get(i);
            assertSuccess(tpe);
            System.assertNotEquals(null, ((Contact)tpe.obj).Id);
            System.assertEquals('Trial'+i, updatedContact.FirstName);
        }
    }

    @IsTest
    static void update_error() {
        

        List<UnitOfWork.INotifyWrapper> tpes      = new List<UnitOfWork.INotifyWrapper>();
        List<Contact> contactsToInsert  = new List<Contact>();
        for (Integer i=0; i<50; i++) {
            UnitOfWork.INotifyWrapper tpe = createDummyPayload();
            tpes.add(tpe);
            contactsToInsert.add((Contact)tpe.obj);
        }
        insert contactsToInsert;

        for (Integer i=0; i<50; i++) {
            contactsToInsert.get(i).FirstName = 'Trial' + i;
        }
        ((Contact)tpes.get(24).obj).LastName = null; // Last name is required. This validates that all are successful but this one

        Test.startTest();
        UnitOfWork uow = new UnitOfWork();
        for (UnitOfWork.INotifyWrapper tpe : tpes) {
            uow.addUpdate((Contact)tpe.obj, tpe);
            uow.checkpoint();
        }
        uow.commitWork();

        System.assertEquals(1, Limits.getDmlStatements());
        System.assertEquals(50, Limits.getDmlRows());
        Test.stopTest();

        List<Contact> updatedContacts = [
                SELECT Id, FirstName, LastName
                FROM Contact
                ORDER BY Id ASC];
        System.assertEquals(50, updatedContacts.size());
        for (Integer i=0; i<50; i++) {
            UnitOfWork.INotifyWrapper tpe     = tpes.get(i);
            Contact updatedContact  = updatedContacts.get(i);
            if (i == 24) {
                assertFail(tpe);
                System.assertEquals('Tester25', updatedContact.FirstName);
            } else {
                assertSuccess(tpe);
                System.assertNotEquals(null, ((Contact)tpe.obj).Id);
                System.assertEquals('Trial'+i, updatedContact.FirstName);
            }
        }
    }

    @IsTest
    static void update_MultipleNotifiers() {
        

        UnitOfWork.INotifyWrapper tpe = createDummyPayload();
        insert (Contact)tpe.obj;

        ((Contact)tpe.obj).FirstName = 'Trial';

        UnitOfWork.INotifyWrapper n1 = new UnitOfWork.INotifyWrapper(null);
        UnitOfWork.INotifyWrapper n2 = new UnitOfWork.INotifyWrapper(null);

        Test.startTest();
        UnitOfWork uow = new UnitOfWork();
        uow.addUpdate((Contact)tpe.obj, tpe);
        uow.addUpdate((Contact)tpe.obj, n1);
        uow.addUpdate((Contact)tpe.obj, n2);
        uow.checkpoint();
        uow.commitWork();

        System.assertEquals(1, Limits.getDmlStatements());
        System.assertEquals(1, Limits.getDmlRows());
        Test.stopTest();

        Contact c = [
                SELECT Id, FirstName, LastName, Email
                FROM Contact
        ];
        System.assertEquals(((Contact)tpe.obj).Id, c.Id);
        assertSuccess(tpe);
        System.assertEquals('Trial', c.FirstName);
        System.assert(n1.wasNotified());
        System.assert(n1.isSuccess());
        System.assert(!n1.isFail());
        System.assert(n2.wasNotified());
        System.assert(n2.isSuccess());
        System.assert(!n2.isFail());
    }

    @IsTest
    static void upsert_single() {
        

        UnitOfWork.INotifyWrapper tpe = createDummyPayload();
        insert (Contact)tpe.obj;

        ((Contact)tpe.obj).FirstName   = 'Trial';
        ((Contact)tpe.obj).Id          = null;

        Test.startTest();
        UnitOfWork uow = new UnitOfWork();
        uow.addUpsert((Contact)tpe.obj, tpe, Contact.Email);
        uow.checkpoint();
        uow.commitWork();

        System.assertEquals(1, Limits.getDmlStatements());
        System.assertEquals(1, Limits.getDmlRows());
        Test.stopTest();

        Contact c = [
                SELECT Id, FirstName, LastName, Email
                FROM Contact
        ];
        System.assertEquals(((Contact)tpe.obj).Id, c.Id);
        assertSuccess(tpe);
        System.assertEquals('Trial', c.FirstName);
    }

    @IsTest
    static void upsert_multiple() {
        

        List<UnitOfWork.INotifyWrapper> tpes      = new List<UnitOfWork.INotifyWrapper>();
        List<Contact> contactsToInsert  = new List<Contact>();
        for (Integer i=0; i<50; i++) {
            UnitOfWork.INotifyWrapper tpe = createDummyPayload();
            tpes.add(tpe);
            contactsToInsert.add((Contact)tpe.obj);
        }
        insert contactsToInsert;

        for (Integer i=0; i<50; i++) {
            contactsToInsert.get(i).FirstName   = 'Trial' + i;
            contactsToInsert.get(i).Id          = null;
        }

        Test.startTest();
        UnitOfWork uow = new UnitOfWork();
        for (UnitOfWork.INotifyWrapper tpe : tpes) {
            uow.addUpsert((Contact)tpe.obj, tpe, Contact.Email);
            uow.checkpoint();
        }
        uow.commitWork();

        System.assertEquals(1, Limits.getDmlStatements());
        System.assertEquals(50, Limits.getDmlRows());
        Test.stopTest();

        List<Contact> updatedContacts = [
                SELECT Id, FirstName, LastName
                FROM Contact
                ORDER BY Id ASC];
        System.assertEquals(50, updatedContacts.size());
        for (Integer i=0; i<50; i++) {
            UnitOfWork.INotifyWrapper tpe     = tpes.get(i);
            Contact updatedContact  = updatedContacts.get(i);
            assertSuccess(tpe);
            System.assertNotEquals(null, ((Contact)tpe.obj).Id);
            System.assertEquals('Trial'+i, updatedContact.FirstName);
        }
    }

    @IsTest
    static void upsert_error() {
        

        List<UnitOfWork.INotifyWrapper> tpes      = new List<UnitOfWork.INotifyWrapper>();
        List<Contact> contactsToInsert  = new List<Contact>();
        for (Integer i=0; i<50; i++) {
            UnitOfWork.INotifyWrapper tpe = createDummyPayload();
            tpes.add(tpe);
            contactsToInsert.add((Contact)tpe.obj);
        }
        insert contactsToInsert;

        for (Integer i=0; i<50; i++) {
            contactsToInsert.get(i).FirstName   = 'Trial' + i;
            contactsToInsert.get(i).Id          = null;
        }
        ((Contact)tpes.get(24).obj).LastName = null; // Last name is required. This validates that all are successful but this one

        Test.startTest();
        UnitOfWork uow = new UnitOfWork();
        for (UnitOfWork.INotifyWrapper tpe : tpes) {
            uow.addUpsert((Contact)tpe.obj, tpe, Contact.Email);
            uow.checkpoint();
        }
        uow.commitWork();

        System.assertEquals(1, Limits.getDmlStatements());
        System.assertEquals(50, Limits.getDmlRows());
        Test.stopTest();

        List<Contact> updatedContacts = [
                SELECT Id, FirstName, LastName
                FROM Contact
                ORDER BY Id ASC];
        System.assertEquals(50, updatedContacts.size());
        for (Integer i=0; i<50; i++) {
            UnitOfWork.INotifyWrapper tpe     = tpes.get(i);
            Contact updatedContact  = updatedContacts.get(i);
            if (i == 24) {
                assertFail(tpe);
                System.assertEquals('Tester25', updatedContact.FirstName);
            } else {
                assertSuccess(tpe);
                System.assertNotEquals(null, ((Contact)tpe.obj).Id);
                System.assertEquals('Trial'+i, updatedContact.FirstName);
            }
        }
    }

    @IsTest
    static void upsert_nullKey() {
        

        UnitOfWork.INotifyWrapper tpe = createDummyPayload();
        insert (Contact)tpe.obj;

        ((Contact)tpe.obj).FirstName = 'Trial';

        Test.startTest();
        UnitOfWork uow = new UnitOfWork();
        try {
            uow.addUpsert((Contact)tpe.obj, tpe, null);
            System.assert(false);
        } catch (UnitOfWork.UoWException e) {
            System.assert(true);
        }
        uow.checkpoint();
        uow.commitWork();
        Test.stopTest();
    }

    @IsTest
    static void upsert_nullKeyValue() {
        

        UnitOfWork.INotifyWrapper tpe = createDummyPayload();
        ((Contact)tpe.obj).Email   = null;
        insert (Contact)tpe.obj;

        ((Contact)tpe.obj).FirstName = 'Trial';

        Test.startTest();
        UnitOfWork uow = new UnitOfWork();
        try {
            uow.addUpsert((Contact)tpe.obj, tpe, Contact.Email);
            System.assert(false);
        } catch (UnitOfWork.UoWException e) {
            System.assert(true);
        }
        uow.checkpoint();
        uow.commitWork();
        Test.stopTest();
    }

    @IsTest
    static void upsert_Id_notnull() {
        

        UnitOfWork.INotifyWrapper tpe = createDummyPayload();
        ((Contact)tpe.obj).Email   = null;
        ((Contact)tpe.obj).FirstName = 'Trial';
        insert ((Contact)tpe.obj);

        Test.startTest();
        UnitOfWork uow = new UnitOfWork();
        uow.addUpsert((Contact)tpe.obj, tpe, Contact.Id);
        uow.checkpoint();
        uow.commitWork();
        Test.stopTest();

        System.assertNotEquals(null, ((Contact)tpe.obj).Id);
    }

    @IsTest
    static void upsert_Id_null() {
        

        UnitOfWork.INotifyWrapper tpe = createDummyPayload();
        ((Contact)tpe.obj).Email   = null;
        ((Contact)tpe.obj).FirstName = 'Trial';

        Test.startTest();
        UnitOfWork uow = new UnitOfWork();
        uow.addUpsert((Contact)tpe.obj, tpe, Contact.Id);
        uow.checkpoint();
        uow.commitWork();
        Test.stopTest();

        System.assertNotEquals(null, ((Contact)tpe.obj).Id);
    }

    @IsTest
    static void delete_single() {
        ContactDomain.run = false;
        

        UnitOfWork.INotifyWrapper tpe = createDummyPayload();
        insert (Contact)tpe.obj;

        ((Contact)tpe.obj).FirstName = 'Trial';

        Test.startTest();
        UnitOfWork uow = new UnitOfWork();
        uow.addDelete((Contact)tpe.obj, tpe);
        uow.checkpoint();
        uow.commitWork();

        System.assertEquals(1, Limits.getDmlStatements());
        System.assertEquals(1, Limits.getDmlRows());
        Test.stopTest();

        List<Contact> c = [
                SELECT Id, FirstName, LastName, Email
                FROM Contact
        ];
        System.assertEquals(0, c.size());
        assertSuccess(tpe);
    }

    @IsTest
    static void delete_multiple() {
        ContactDomain.run = false;

        List<UnitOfWork.INotifyWrapper> tpes      = new List<UnitOfWork.INotifyWrapper>();
        List<Contact> contactsToInsert  = new List<Contact>();
        for (Integer i=0; i<50; i++) {
            UnitOfWork.INotifyWrapper tpe = createDummyPayload();
            tpes.add(tpe);
            contactsToInsert.add((Contact)tpe.obj);
        }
        insert contactsToInsert;

        for (Integer i=0; i<50; i++) {
            contactsToInsert.get(i).FirstName = 'Trial' + i;
        }

        Test.startTest();
        UnitOfWork uow = new UnitOfWork();
        for (UnitOfWork.INotifyWrapper tpe : tpes) {
            uow.addDelete((Contact)tpe.obj, tpe);
            uow.checkpoint();
        }
        uow.commitWork();

        System.assertEquals(1, Limits.getDmlStatements());
        System.assertEquals(50, Limits.getDmlRows());
        Test.stopTest();

        List<Contact> deletedContacts = [
                SELECT Id, FirstName, LastName
                FROM Contact
                ORDER BY Id ASC];
        System.assertEquals(0, deletedContacts.size());
        for (Integer i=0; i<50; i++) {
            UnitOfWork.INotifyWrapper tpe     = tpes.get(i);
            assertSuccess(tpe);
        }
    }

    /*@IsTest
    static void delete_error() {
        // need to think about how to test this...
    }*/

    @IsTest
    static void commitNothing() {
        UnitOfWork uow = new UnitOfWork();
        uow.checkpoint();
        uow.commitWork();
    }

    @IsTest
    static void nullRecord() {
        Contact c;
        UnitOfWork uow = new UnitOfWork();
        try {
            uow.addInsert(c, new UnitOfWork.INotifyWrapper(null));
            System.assert(false);
        } catch (UnitOfWork.UoWException e) {
            System.assert(true);
        }
    }

    @IsTest
    static void nullNotifier() {
        

        Contact c = createDummyContact();
        UnitOfWork.INotify notifier = null;

        Test.startTest();
        UnitOfWork uow = new UnitOfWork();
        uow.addInsert(c, notifier);
        uow.checkpoint();
        uow.commitWork();
        Test.stopTest();

        System.assert(true);


        Contact c1 = [
                SELECT Id, FirstName, LastName, Email
                FROM Contact
        ];// null or >1 will throw error
        System.assertNotEquals(null, c1);
    }

    /**
     * add items, reset: assert the UoW is empty
     */
    @IsTest
    static void checkpoint_1() {
        UnitOfWork.INotifyWrapper inw = createDummyPayload();
        Contact c = (Contact)inw.obj;
        UnitOfWork uow = new UnitOfWork();
        uow.addInsert(c, inw);
        System.assertEquals(0, uow.getCheckpointSize());
        System.assertEquals(1, uow.eventsByOperation.get(DB.Action.CREATE).size());
        uow.reset();
        System.assertEquals(0, uow.getCheckpointSize());
        System.assertEquals(0, uow.eventsByOperation.get(DB.Action.CREATE).size());
    }

    /**
     * add items, checkpoint, add new sobjects, commit: assert only the first items are committed
     */
    @IsTest
    static void checkpoint_2() {
        

        UnitOfWork uow = new UnitOfWork();

        UnitOfWork.INotifyWrapper inw1 = createDummyPayload();
        Contact c1 = (Contact)inw1.obj;
        uow.addInsert(c1, inw1);
        uow.checkpoint();
        System.assertEquals(1, uow.getRecordSize());
        System.assertEquals(1, uow.getNotifierSize());
        System.assertEquals(1, uow.getCheckpointSize());

        UnitOfWork.INotifyWrapper inw2 = createDummyPayload();
        Contact c2 = (Contact)inw2.obj;
        uow.addInsert(c2, inw2);
        System.assertEquals(2, uow.getRecordSize());
        System.assertEquals(2, uow.getNotifierSize());
        System.assertEquals(1, uow.getCheckpointSize());

        uow.commitWork();
        assertSuccess(inw1);

        List<Contact> contact = [
                SELECT Id
                FROM Contact
        ];
        System.assertEquals(1, contact.size());
        System.assertEquals(contact.get(0).Id, c1.Id);
        System.assert(!inw2.wasNotified());
        System.assertEquals(null, c2.Id);
    }

    /**
     * - add items, checkpoint, add new sobjects, reset: assert there are no non-checkpointed items
     */
    @IsTest
    static void checkpoint_3() {
        

        UnitOfWork uow = new UnitOfWork();

        UnitOfWork.INotifyWrapper inw1 = createDummyPayload();
        Contact c1 = (Contact)inw1.obj;
        uow.addInsert(c1, inw1);
        uow.checkpoint();
        System.assertEquals(1, uow.getCheckpointSize());

        UnitOfWork.INotifyWrapper inw2 = createDummyPayload();
        Contact c2 = (Contact)inw2.obj;
        uow.addInsert(c2, inw2);
        System.assertEquals(2, uow.getNotifierSize());
        System.assertEquals(2, uow.getRecordSize());
        System.assertEquals(1, uow.getCheckpointSize());

        uow.reset();
        System.assertEquals(1, uow.getCheckpointSize());
        System.assertEquals(1, uow.getNotifierSize());
        System.assertEquals(1, uow.getRecordSize());
    }

    /**
     * - add items, checkpoint, add new INotify to existing items, commit: assert all items committed and ALL notifiers notified
     */
    @IsTest
    static void checkpoint_4() {
        

        UnitOfWork uow = new UnitOfWork();

        UnitOfWork.INotifyWrapper inw1 = createDummyPayload();
        Contact c1 = (Contact)inw1.obj;
        uow.addUpsert(c1, inw1, Contact.Email);
        uow.checkpoint();
        System.assertEquals(1, uow.getCheckpointSize());
        System.assertEquals(1, uow.getNotifierSize());
        System.assertEquals(1, uow.getRecordSize());

        UnitOfWork.INotifyWrapper inw2 = new UnitOfWork.INotifyWrapper(c1);
        uow.addUpsert(c1, inw2, Contact.Email);
        System.assertEquals(2, uow.getNotifierSize());
        System.assertEquals(1, uow.getRecordSize());
        System.assertEquals(1, uow.getCheckpointSize());

        uow.commitWork();
        assertSuccess(inw1);
        assertSuccess(inw2);
    }

    /**
     * add items, checkpoint, add new INotify to existing items, reset, commit: assert all items are committed and only first INotifiers are notified
     */
    @IsTest
    static void checkpoint_5() {
        

        UnitOfWork uow = new UnitOfWork();

        UnitOfWork.INotifyWrapper inw1 = createDummyPayload();
        Contact c1 = (Contact)inw1.obj;
        uow.addUpsert(c1, inw1, Contact.Email);
        uow.checkpoint();
        System.assertEquals(1, uow.getCheckpointSize());
        System.assertEquals(1, uow.getNotifierSize());
        System.assertEquals(1, uow.getRecordSize());

        UnitOfWork.INotifyWrapper inw2 = new UnitOfWork.INotifyWrapper(c1);
        uow.addUpsert(c1, inw2, Contact.Email);
        System.assertEquals(2, uow.getNotifierSize());
        System.assertEquals(1, uow.getRecordSize());
        System.assertEquals(1, uow.getCheckpointSize());

        uow.reset();
        System.assertEquals(1, uow.getNotifierSize());
        System.assertEquals(1, uow.getRecordSize());
        System.assertEquals(1, uow.getCheckpointSize());

        uow.commitWork();

        assertSuccess(inw1);
        System.assert(!inw2.wasNotified());
        System.assert(!inw2.isSuccess());
        System.assert(!inw2.isFail());

    }

    /*@IsTest
    static void mixedExternalIdFields() {
        

        UnitOfWork uow = new UnitOfWork();

        UnitOfWork.INotifyWrapper inw1 = createDummyPayload();
        UnitOfWork.INotifyWrapper inw2 = createDummyPayload();
        uow.addUpsert((Contact)inw1.obj, inw1, Contact.Email);
        uow.addUpsert((Contact)inw1.obj, inw1, Contact.Email);
        uow.addUpsert((Contact)inw2.obj, inw2, Contact.TicketureIdentityId__c);
        uow.addUpsert((Contact)inw2.obj, inw2, Contact.TicketureIdentityId__c);

        NPSPUtility.disableAllNPSPTriggers();
        uow.checkpoint();
        uow.commitWork();

        List<Contact> contacts = [
                SELECT Id, Email, TicketureIdentityId__c
                FROM Contact
        ];
        System.debug(contacts);

        System.assertEquals(2, contacts.size());
        System.assertEquals(2, Limits.getDmlStatements());
        System.assertEquals(2, Limits.getDmlRows());

        assertSuccess(inw1);
        assertSuccess(inw2);
    }*/

    @IsTest
    static void mixedSObjectTypes() {
        

        UnitOfWork uow = new UnitOfWork();

        Account a = new Account();
        a.Name = 'Test';
        UnitOfWork.INotifyWrapper inw1 = new UnitOfWork.INotifyWrapper(a);
        uow.addInsert(a, inw1);

        Product2 p = new Product2();
        p.Name  = 'Test';
        UnitOfWork.INotifyWrapper inw2 = new UnitOfWork.INotifyWrapper(p);
        uow.addInsert(p, inw2);

        Test.startTest();
        uow.checkpoint();
        uow.commitWork();
        Test.stopTest();

        assertSuccess(inw1);
        assertSuccess(inw2);
    }

    @IsTest
    static void mixedDML(){
        ContactDomain.run = false;
        


        UnitOfWork.INotifyWrapper contactToUpdate = createDummyPayload();
        UnitOfWork.INotifyWrapper contactToDelete = createDummyPayload();
        insert new List<Contact>{(Contact)contactToUpdate.obj, (Contact)contactToDelete.obj};

        UnitOfWork.INotifyWrapper contactToInsert = createDummyPayload();
        UnitOfWork.INotifyWrapper contactToUpsert = createDummyPayload();

        Test.startTest();
        UnitOfWork uow = new UnitOfWork();
        uow.addInsert((Contact)contactToInsert.obj, contactToInsert);
        uow.addUpsert((Contact)contactToUpsert.obj, contactToUpsert, Contact.Email);
        uow.addUpdate((Contact)contactToUpdate.obj, contactToUpdate);
        uow.addDelete((Contact)contactToDelete.obj, contactToDelete);
        uow.checkpoint();
        uow.commitWork();
        Test.stopTest();

        assertSuccess(contactToInsert);
        assertSuccess(contactToUpsert);
        assertSuccess(contactToUpdate);
        assertSuccess(contactToDelete);

        List<Contact> contacts = [
                SELECT Id
                FROM Contact
        ];
        System.assertEquals(3, contacts.size());
    }


    private static UnitOfWork.INotifyWrapper createDummyPayload() {
        return new UnitOfWork.INotifyWrapper(createDummyContact());
    }
    private static Contact createDummyContact() {
        contactIdx++;

        Contact c1                  = new Contact();
        c1.FirstName                = 'Tester' + contactIdx;
        c1.LastName                 = 'McTestFace';
        c1.Email                    = '<EMAIL>' + contactIdx;

        return c1;
    }

    private static void assertSuccess(UnitOfWork.INotifyWrapper nw) {
        System.assert(nw.wasNotified());
        System.assert(nw.isSuccess());
        System.assert(!nw.isFail());
    }

    private static void assertFail(UnitOfWork.INotifyWrapper nw) {
        System.assert(nw.wasNotified());
        System.assert(!nw.isSuccess());
        System.assert(nw.isFail());
    }
}