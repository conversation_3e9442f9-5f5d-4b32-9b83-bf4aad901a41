/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Parser unit tests.
 *
 * These test the parsing of whole SOQL statements.
 * These will also test CRUDFLS
 */
@IsTest(IsParallel=false)
private class SOQLTest {

    static {
        //LicenseManager.setLicense(LicenseManagerTest.TestLicenseKey_orgId.class);
    }

    /*
     * FULL SOQL TESTS
     */

    @IsTest
    static void testParser_SOQL_00 () {
        System.runAs(TestUtils.createStandardUser()) {
            SOQL.parse(null,
                    'SELECT a.Id, a.Name, a.Owner.Manager.Username, MAX(Website), ' +
                            'TYPEOF Owner\n' +
                            'WHEN User THEN Id\n' +
                            'WHEN Group THEN CreatedById\n' +
                            'ELSE Name\n' +
                            'END, ' +
                            '(SELECT Id, Account.Id FROM Contacts) ' +
                            'FROM Account a, a.Owner u, u.Manager m' +
                            'WHERE a.Name LIKE \'Burlington%\'');

            System.assert(true);
        }
    }

    @IsTest
    static void testParser_SOQL_01 () {
        SOQL.parse(null, 'SELECT Id, Name, COUNT(), TYPEOF What\n' +
                'WHEN Account THEN Phone\n' +
                'WHEN Contact THEN Email, Mobile\n' +
                'WHEN User THEN Username,FederationId\n' +
                'ELSE Name, Bla\n' +
                'END,\n' +
                'MAX(Value) MyFuncAlias,  \t \r \n' +
                '(SELECT Id FROM Children c, User u WHERE 1=1)\n' +
                'FROM Account a\n' +
                'USING SCOPE TEAM\n' +
                'WHERE Id = \'123\'');

        System.assert(true);
    }

    @IsTest
    static void testParser_SOQL_02 () {
        try {
            SOQL.Query qry = SOQL.parse(null, 'SELECT Id, Name FROM NonExistantObject__c');
            qry.validate(true, true);
            System.assert(false);
        } catch (Exception e) {
            System.assert(true);
        }
    }

    @IsTest
    static void testParser_SOQL_03 () {
        SOQL.Query qry = SOQL.parse(null, 'SELECT Id FROM Contact, Contact.Account');
        qry.validate(true, true);
        System.assert(true);
    }

    // Sub-query
    @IsTest
    static void testParser_SOQL_04 () {
        SOQL.Query qry = SOQL.parse(null, 'SELECT Id, (SELECT Id FROM Contacts) FROM Account');
        qry.validate(true, true);

        System.assertEquals(1, qry.select_stmt.subQueries.size());
        System.assertEquals(qry.from_stmt, qry.select_stmt.subQueries.get(0).from_stmt.parentFromStmt);
        System.assertEquals('Contacts', qry.select_stmt.subQueries.get(0).from_stmt.froms.get(0).parts.get(0).tk.lexeme);
        System.assertEquals(Contact.SObjectType, qry.select_stmt.subQueries.get(0).from_stmt.froms.get(0).parts.get(0).getSObjectType());

    }

    @IsTest
    static void testParser_SOQL_05 () {
        try {
            SOQL.Query qry = SOQL.parse(null, 'SELECT What.Name FROM Event');
            qry.validate(true, true);
            System.assert(false);
        } catch (ParserException pe) {
            System.assert(pe.getMessage().startsWith('Polymorphic relationship joins are not supported'));
        }
    }


    /*
     * Tests that the SOQL Parser throws exceptions
     * for improper SOQL syntax
     */


    @IsTest
    static void testParser_UnexpectedTokens () {
        try {
            SOQL.Query qry = SOQL.parse(null, 'NOTSELECT * FROM BLA'); // SELECT should be the first keyword
            System.assert(false);
        } catch (ParserException spe) {
            System.assert(true);
        }

        try {
            SOQL.Query qry = SOQL.parse(null, 'SELECT Id WHERE'); // All SOQL requires a "FROM"
            System.assert(false);
        } catch (ParserException spe) {
            System.assert(true);
        }

        try {
            SOQL.Query qry = SOQL.parse(null, 'SELECT Id '); // All SOQL requires a "FROM"
            System.assert(false);
        } catch (ParserException spe) {
            System.assert(true);
        }

        try {
            SOQL.Query qry = SOQL.parse(null, 'SELECT Id, (SELECT Id FROM Contacts FROM Account'); // Subqueries require closing )
            System.assert(false);
        } catch (ParserException spe) {
            System.assert(true);
        }

        try {
            SOQL.Query qry = SOQL.parse(null, 'SELECT Id, FROM Account'); // Comma should not follow last SELECT element
            System.assert(false);
        } catch (ParserException spe) {
            System.assert(true);
        }

        try {
            SOQL.Query qry = SOQL.parse(null, 'SELECT Id Name FROM Account'); // Comma should separate identifiers
            System.assert(false);
        } catch (ParserException spe) {
            System.assert(true);
        }

        // Functions not allowed in sub-queries
        try {
            SOQL.Query qry = SOQL.parse(null, 'SELECT Id, Name, (SELECT COUNT() FROM Contacts) FROM Account');
            System.assert(false);
        } catch (Exception e) {
            System.assert(true);
        }

        // Sub-queries not allowed in sub-queries
        try {
            SOQL.Query qry = SOQL.parse(null, 'SELECT Id, Name, (SELECT Id, (SELECT Id FROM Accounts) FROM Contacts) FROM Account');
            System.assert(false);
        } catch (Exception e) {
            System.assert(true);
        }

        // Bla is an invalid field on Account.
        try {
            SOQL.Query qry = SOQL.parse(null, 'SELECT Id, Account.Bla FROM Contact');
            qry.validate(true, true);
            System.assert(false);
        } catch (Exception e) {
            System.assert(true);
        }
    }


    /*
     * TOKEN TESTS
     */


    @IsTest
    static void testParser_FUNCTION () {
        SOQL.Scanner parser = new SOQL.ScannerImpl('COUNT(Id) MyCount'); // Validate field, function, alias

        SOQL.FunctionStmt fe = new SOQL.FunctionStmt(parser.next(), null);
        fe.parse(parser);
        System.assertEquals('COUNT', fe.tk.lexeme);
        System.assertEquals('Id', fe.fieldPart.parts.get(0).tk.lexeme);
        System.assertEquals('MyCount', fe.alias.lexeme);

        parser = new SOQL.ScannerImpl('COUNT() MyCount'); // COUNT doesn't require a field, also validates alias
        fe = new SOQL.FunctionStmt(parser.next(), null);
        fe.parse(parser);

        System.assertEquals('COUNT', fe.tk.lexeme);
        System.assertEquals(null, fe.fieldPart);
        System.assertEquals('MyCount', fe.alias.lexeme);

        parser = new SOQL.ScannerImpl('COUNT(Id)'); // Alias not required
        fe = new SOQL.FunctionStmt(parser.next(), null);
        fe.parse(parser);
        System.assertEquals('COUNT', fe.tk.lexeme);
        System.assertEquals('Id', fe.fieldPart.parts.get(0).tk.lexeme);
        System.assertEquals(null, fe.alias);

        parser = new SOQL.ScannerImpl('COUNT(Real.Name__c)'); // Field.Notation
        fe = new SOQL.FunctionStmt(parser.next(), null);
        fe.parse(parser);
        System.assertEquals('COUNT', fe.tk.lexeme);
        System.assertEquals(2, fe.fieldPart.parts.size());
        System.assertEquals('Real', fe.fieldPart.parts.get(0).tk.lexeme);
        System.assertEquals('Name__c', fe.fieldPart.parts.get(1).tk.lexeme);
        System.assertEquals(null, fe.alias);

        parser = new SOQL.ScannerImpl('COUNT(Id),'); // Comma's allowed
        fe = new SOQL.FunctionStmt(parser.next(), null);
        fe.parse(parser);


        //testParser_FUNCTION_fail('COUNT(Id) as MyCount'); // Functions don't support 'AS' //
        testParser_FUNCTION_fail('COUNT ID MyCount'); // Functions require an opening (
        testParser_FUNCTION_fail('COUNT(FROM)'); // Functions require a field, not a keyword token
        testParser_FUNCTION_fail('COUNT(Id'); // Functions require a closing )
        testParser_FUNCTION_fail('AVG()'); // Only the COUNT() function can be empty
        //testParser_FUNCTION_fail('COUNT(Id) FROM'); // Alias can't be keyword
    }

    static void testParser_FUNCTION_fail (String str) {
        try {
            SOQL.Scanner parser = new SOQL.ScannerImpl(str); // Validate field, function, alias

            SOQL.FunctionStmt fe = new SOQL.FunctionStmt(parser.next(), null);
            fe.parse(parser);
            System.assert(false);
        } catch (ParserException spe) {
            System.assert(true);
        }
    }

    @IsTest
    static void testParser_TYPEOF () {
        String stringToLex1 = 'TYPEOF What\n' +
                'WHEN Account THEN Phone\n' +
                'WHEN Contact THEN Email, Mobile\n' +
                'WHEN User THEN Username,FederationId\n' +
                'ELSE Name, Bla\n' +
                'END';
        SOQL.Scanner lexer = new SOQL.ScannerImpl(stringToLex1);
        SOQL.TypeOfStmt ft = new SOQL.TypeOfStmt(lexer.next(), null);
        ft.parse(lexer);

        System.assertEquals('What', ft.field.parts.get(0).tk.lexeme);

        System.assertEquals(3, ft.whenThens.size());


        System.assertEquals('Account', ft.whenThens.get(0).whenObject.froms.get(0).parts.get(0).tk.lexeme);
        System.assertEquals(1, ft.whenThens.get(0).thenFields.size());
        System.assertEquals('Phone', ft.whenThens.get(0).thenFields.get(0).parts.get(0).tk.lexeme);

        System.assertEquals('Contact', ft.whenThens.get(1).whenObject.froms.get(0).parts.get(0).tk.lexeme);
        System.assertEquals(2, ft.whenThens.get(1).thenFields.size());
        System.assertEquals('Email', ft.whenThens.get(1).thenFields.get(0).parts.get(0).tk.lexeme);
        System.assertEquals('Mobile', ft.whenThens.get(1).thenFields.get(1).parts.get(0).tk.lexeme);

        System.assertEquals('User', ft.whenThens.get(2).whenObject.froms.get(0).parts.get(0).tk.lexeme);
        System.assertEquals(2, ft.whenThens.get(2).thenFields.size());
        System.assertEquals('Username', ft.whenThens.get(2).thenFields.get(0).parts.get(0).tk.lexeme);
        System.assertEquals('FederationId', ft.whenThens.get(2).thenFields.get(1).parts.get(0).tk.lexeme);

        System.assertEquals(2, ft.elseFields.size());
        System.assertEquals('Name', ft.elseFields.get(0).parts.get(0).tk.lexeme);
        System.assertEquals('Bla', ft.elseFields.get(1).parts.get(0).tk.lexeme);

        testParser_TYPEOF_fail('TYPEOF SELECT'); // Should be a field
        testParser_TYPEOF_fail('TYPEOF Field1, SELECT'); // Should be a field
        testParser_TYPEOF_fail('TYPEOF Field1 SELECT'); // Should be a WHEN
        testParser_TYPEOF_fail('TYPEOF Field1 WHEN SELECT'); // Should be an object
        testParser_TYPEOF_fail('TYPEOF Field1 WHEN Object1 SELECT'); // Should be a THEN
        testParser_TYPEOF_fail('TYPEOF Field1 WHEN Object1 THEN SELECT'); // Should be a Field
        testParser_TYPEOF_fail('TYPEOF Field1 WHEN Object1 THEN Field2,SELECT'); // Should be a Field
        testParser_TYPEOF_fail('TYPEOF Field1 WHEN Object1 THEN Field2 ELSE SELECT'); // Should be a Field
        testParser_TYPEOF_fail('TYPEOF Field1 WHEN Object1 THEN Field2 ELSE Field3 SELECT'); // Should be a Field
    }
    static void testParser_TYPEOF_fail (String str) {
        try {
            SOQL.Scanner lexer = new SOQL.ScannerImpl(str);
            SOQL.TypeOfStmt ft = new SOQL.TypeOfStmt(lexer.next(), null);
            ft.parse(lexer);
            System.assert(false);
        } catch (ParserException spe) {
            System.assert(true);
        }
    }

    @IsTest
    static void testParser_FROM () {
        SOQL.Scanner lexer = new SOQL.ScannerImpl('FROM object1');
        SOQL.FromStmt ft = new SOQL.FromStmt(lexer.next(), null);
        ft.parse(lexer);
        System.assertEquals(1, ft.froms.size());
        System.assertEquals('object1', ft.froms.get(0).parts.get(0).tk.lexeme);
        System.assertEquals(null, ft.froms.get(0).alias);

        lexer = new SOQL.ScannerImpl('FROM object1 o');
        ft = new SOQL.FromStmt(lexer.next(), null);
        ft.parse(lexer);
        System.assertEquals(1, ft.froms.size());
        System.assertEquals('object1', ft.froms.get(0).parts.get(0).tk.lexeme);
        System.assertEquals('o', ft.froms.get(0).alias.lexeme);


        lexer = new SOQL.ScannerImpl('FROM object1 o, object2');
        ft = new SOQL.FromStmt(lexer.next(), null);
        ft.parse(lexer);
        System.assertEquals(2, ft.froms.size());
        System.assertEquals('object1', ft.froms.get(0).parts.get(0).tk.lexeme);
        System.assertEquals('o', ft.froms.get(0).alias.lexeme);
        System.assertEquals('object2', ft.froms.get(1).parts.get(0).tk.lexeme);
        System.assertEquals(null, ft.froms.get(1).alias);

        lexer = new SOQL.ScannerImpl('FROM object1 o1, object2 o2');
        ft = new SOQL.FromStmt(lexer.next(), null);
        ft.parse(lexer);
        System.assertEquals(2, ft.froms.size());
        System.assertEquals('object1', ft.froms.get(0).parts.get(0).tk.lexeme);
        System.assertEquals('o1', ft.froms.get(0).alias.lexeme);
        System.assertEquals('object2', ft.froms.get(1).parts.get(0).tk.lexeme);
        System.assertEquals('o2', ft.froms.get(1).alias.lexeme);

        lexer = new SOQL.ScannerImpl('FROM object1 o1, o1.object2 o2');
        ft = new SOQL.FromStmt(lexer.next(), null);
        ft.parse(lexer);
        System.assertEquals(2, ft.froms.size());
        System.assertEquals('object1', ft.froms.get(0).parts.get(0).tk.lexeme);
        System.assertEquals('o1', ft.froms.get(0).alias.lexeme);
        System.assertEquals('o1', ft.froms.get(1).parts.get(0).tk.lexeme);
        System.assertEquals('object2', ft.froms.get(1).parts.get(1).tk.lexeme);
        System.assertEquals('o2', ft.froms.get(1).alias.lexeme);

        testParser_FROM_fail('FROM');
        testParser_FROM_fail('FROM SELECT');
        testParser_FROM_fail('FRM');
        testParser_FROM_fail('FROM COUNT()');
    }

    static void testParser_FROM_fail (String str) {
        SOQL.Scanner lexer = new SOQL.ScannerImpl(str);

        try {
            SOQL.FromStmt ft = new SOQL.FromStmt(lexer.next(), null);
            ft.parse(lexer);
            System.assert(false);
        } catch (ParserException spe) {
            System.assert(true);
        } catch (System.TypeException spe) {
            System.assert(true);
        }
    }

    @IsTest
    static void testParser_USING () {
        testParser_USING_pass('DELEGATED');
        testParser_USING_pass('EVERYTHING');
        testParser_USING_pass('MINE');
        testParser_USING_pass('MY_TERRITORY');
        testParser_USING_pass('MY_TEAM_TERRITORY');
        testParser_USING_pass('TEAM');

        testPaser_USING_fail('USING');
        testPaser_USING_fail('USING BLA');
        testPaser_USING_fail('USING SCOPE BLA');
    }

    static void testParser_USING_pass (String scope) {
        SOQL.Scanner lexer = new SOQL.ScannerImpl('USING SCOPE ' + scope);
        SOQL.UsingStmt ft = new SOQL.UsingStmt(lexer.next(), null);
        ft.parse(lexer);
        System.assertNotEquals(null, ft.scope_token);
        System.assertEquals(scope, ft.scope_token.lexeme);
    }
    static void testPaser_USING_fail (String scope) {
        SOQL.Scanner lexer = new SOQL.ScannerImpl('USING SCOPE ' + scope);

        try {
            SOQL.UsingStmt ft = new SOQL.UsingStmt(lexer.next(), null);
            ft.parse(lexer);
            System.assert(false);
        } catch (ParserException spe) {
            System.assert(true);
        } catch (System.TypeException spe) {
            System.assert(true);
        }
    }


    /*
     * CRUD/FLS TESTS
     */


    @IsTest
    static void testCRUDFLS () {
        System.runAs(TestUtils.createStandardUser()) {
            SOQL.Query qry = SOQL.parse('crudfls', 'SELECT c.Id, c.Subject, ' +
                    'TYPEOF c.Owner\n' +
                    'WHEN User THEN Id\n' +
                    'WHEN Group THEN CreatedById\n' +
                    'ELSE Name\n' +
                    'END, ' +
                    '(SELECT Id, ' +
                    '       TYPEOF What\n' +
                    'WHEN Case THEN Id\n' +
                    'WHEN Account THEN Name\n' +
                    'ELSE Email\n' +
                    'END ' +
                    'FROM Events) ' +
                    'FROM Case c, c.Account a, a.Owner.Manager m ' +
                    'WHERE a.Name LIKE \'Burlington%\'');
            qry.validate(true, true);
            System.assert(true);
        }
    }

    @IsTest
    static void testCRUDFLS_TYPEOF () {
        System.runAs(TestUtils.createStandardUser()) {
            SOQL.Query qry = SOQL.parse(null,
                    'SELECT TYPEOF Owner\n' +
                            'WHEN User THEN Id\n' +
                            'WHEN Group THEN CreatedBy.Manager.Username\n' +
                            'ELSE Name\n' +
                            'END ' +
                            'FROM Case');
            qry.validate(true, true);
            System.assert(true);
        }
    }

    @IsTest
    static void testCRUDFLS_FUNCTION () {
        System.runAs(TestUtils.createStandardUser()) {
            SOQL.Query qry = SOQL.parse(null, 'SELECT COUNT(Id) FROM Account');
            qry.validate(true, true);
            System.assert(true);
        }
    }

    @IsTest
    static void testCRUDFLS_ERROR () {
        System.runAs(TestUtils.createStandardUser()) {
            try {
                SOQL.Query qry = SOQL.parse(null, 'SELECT Hooga FROM Account');
                qry.validate(true, true);
                System.assert(false);
            } catch (ParserException spe) {
                System.assert(true);
            }
        }
    }

    @IsTest
    static void testCRUD_badAlias () {
        System.runAs(TestUtils.createStandardUser()) {
            try {
                SOQL.Query qry = SOQL.parse(null, 'SELECT u.Name FROM Account');
                qry.validate(true, true);
                System.assert(false);
            } catch (ParserException spe) {
                System.assert(true);
            }
        }
    }

    @IsTest
    static void testCRUD_badObjName () {
        System.runAs(TestUtils.createStandardUser()) {
            try {
                SOQL.Query qry = SOQL.parse(null, 'SELECT a.Name, h.Name FROM Account a, a.Hooga h');
                qry.validate(true, true);
                System.assert(false);
            } catch (ParserException spe) {
                System.assert(true);
            }
        }
    }

    @IsTest
    static void test_BigQueryParse () {
        String soqlString = 'SELECT ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, ParentId, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Parent.Parent.Parent.Parent.Parent.Id,  Parent.Parent.Parent.Parent.Parent.Name,  Parent.Parent.Parent.Parent.Parent.OwnerId,  Parent.Parent.Parent.Parent.Parent.Site, Parent.Parent.Parent.Parent.Parent.AnnualRevenue, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' Id, Name, AccountNumber, OwnerId, Site, AccountSource, AnnualRevenue, BillingStreet, BillingCity, BillingState, BillingPostalCode, CreatedBy, Description, NumberOfEmployees, Fax, Industry, LastModifiedBy, Ownership, Parent, Phone, Rating, ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, TickerSymbol, Type, Website, ' +
                ' (SELECT Id, AssistantName, AssistantPhone, Birthdate, OwnerId, CreatedBy, Department, Description, Email, Fax, HomePhone, LastModifiedBy, LeadSource, MailingStreet, MailingCity, MailingState, MailingPostalCode, MobilePhone, FirstName, LastName, OtherStreet, OtherCity, OtherState, OtherPostalCode, OtherPhone, Phone, Title, Account.Parent.Parent.Parent.Parent.Id, Account.Parent.Parent.Parent.Parent.Name FROM Contacts), ' +
                ' (SELECT Id, Name, CreatedBy, CreatedDate, LastModifiedBy, LastModifiedDate, OwnerId, Amount, CloseDate, Description, ExpectedRevenue, ForecastCategoryName, LeadSource, NextStep, Campaign, IsPrivate, Probability, TotalOpportunityQuantity, StageName, Type FROM Opportunities), ' +
                ' (SELECT Id, CreatedBy, CreatedDate, LastModifiedBy, LastModifiedDate, OwnerId, Asset, CaseNumber, Origin, Reason, ContactId, ClosedDate, Description, IsEscalated, ParentId, Priority, Status, Subject, Type, SuppliedCompany, SuppliedEmail, SuppliedName, SuppliedPhone FROM Cases) ' +
                ' FROM Account';

        Long timeA = Limits.getCpuTime();
        SOQL.Query q = SOQL.parse(null, soqlString);
        Long timeB = Limits.getCpuTime();
        q.validate(true, true);
        Long timeC = Limits.getCpuTime();
        System.debug('Total Parse Time: ' + (timeB - timeA));
        System.debug('Total Validate Time: ' + (timeC - timeB));


        System.assert(true);
    }

    /*private static void scanParse(SOQL.Scanner s) {
        Long start                  = Limits.getCpuTime();
        List<SOQL.Token> t1 = s.scanTokens();
        Long timeA                  = Limits.getCpuTime();


        SOQL.Query qry1 = SOQL.parse(null, s);
        Long timeB                  = Limits.getCpuTime();



    }*/


    /*
        SCANNER TESTS
     */

    @IsTest
    static void tokenCount () {
        SOQL.Scanner scanner = new SOQL.ScannerImpl('hooga   SELECT field.parent');
        List<SOQL.Token> tokens = scanner.scanTokens();

        System.assertEquals(6, tokens.size());

        scanner = new SOQL.ScannerImpl('SELECT Id, (SELECT Id FROM Contacts FROM Account');
        tokens = scanner.scanTokens();

        System.assertEquals(11, tokens.size());
    }

    /**
     * peek() then next() should have different tokens.
     */
    @IsTest
    static void peekNext () {
        SOQL.Scanner scanner = new SOQL.ScannerImpl('   hooga   SELECT field.parent   ');

        SOQL.Token token1 = scanner.peek();

        System.assertEquals(SOQL.TokenType.IDENTIFIER, token1.type);
        System.assertEquals('hooga', token1.lexeme);

        SOQL.Token token2 = scanner.next();

        System.assertEquals(SOQL.TokenType.IDENTIFIER, token2.type);
        System.assertEquals('hooga', token2.lexeme);

        SOQL.Token token3 = scanner.peek();

        System.assertEquals(SOQL.TokenType.TOKEN_SELECT, token3.type);
        System.assertEquals('SELECT', token3.lexeme);

        SOQL.Token token4 = scanner.peek();

        System.assertEquals(SOQL.TokenType.TOKEN_SELECT, token4.type);
        System.assertEquals('SELECT', token4.lexeme);

        SOQL.Token token5 = scanner.next();

        System.assertEquals(SOQL.TokenType.TOKEN_SELECT, token5.type);
        System.assertEquals('SELECT', token5.lexeme);

        SOQL.Token token6 = scanner.peek();

        System.assertEquals(SOQL.TokenType.IDENTIFIER, token6.type);
        System.assertEquals('field', token6.lexeme);
        scanner.next();

        SOQL.Token token7 = scanner.next();

        System.assertEquals(SOQL.TokenType.DOT, token7.type);
        System.assertEquals('.', token7.lexeme);

        SOQL.Token token8 = scanner.next();

        System.assertEquals(SOQL.TokenType.IDENTIFIER, token8.type);
        System.assertEquals('parent', token8.lexeme);
    }

    /**
     * Test string literals
     */
    @IsTest
    static void literals () {
        String stringToLex1 = '\'Hello >=<(): \\\'World\\\'\'';
        SOQL.Scanner scanner = new SOQL.ScannerImpl(stringToLex1);
        SOQL.Token t = scanner.next();
        System.assertEquals(SOQL.TokenType.TOKEN_STRING, t.type);
        System.assertEquals('Hello >=<(): \\\'World\\\'', t.literal);
        System.assertEquals(stringToLex1.trim(), t.lexeme);
    }

    /**
     * Test string literals
     */
    @IsTest
    static void numbers () {
        String stringToLex1 = '123 123.0';
        SOQL.Scanner scanner = new SOQL.ScannerImpl(stringToLex1);

        SOQL.Token t1 = scanner.next();
        System.assertEquals(SOQL.TokenType.TOKEN_NUMBER, t1.type);
        System.assertEquals('123', t1.lexeme);

        SOQL.Token t2 = scanner.next();
        System.assertEquals(SOQL.TokenType.TOKEN_NUMBER, t2.type);
        System.assertEquals('123.0', t2.lexeme);
    }

    /**
     * consume
     */
    @IsTest
    static void consume () {
        SOQL.ScannerImpl si = new SOQL.ScannerImpl('SELECT');
        si.consume(SOQL.TokenType.TOKEN_SELECT, 'Should be select token');
        System.assert(true);
    }

    @IsTest
    static void symbols () {
        SOQL.ScannerImpl si = new SOQL.ScannerImpl('!= ( ) < > <= >= = : ,');

        SOQL.Token t = si.next();
        System.assertEquals('!=', t.lexeme);
        System.assertEquals(SOQL.TokenType.BANG_EQUAL, t.type);

        t = si.next();
        System.assertEquals('(', t.lexeme);
        System.assertEquals(SOQL.TokenType.LEFT_PAREN, t.type);

        t = si.next();
        System.assertEquals(')', t.lexeme);
        System.assertEquals(SOQL.TokenType.RIGHT_PAREN, t.type);

        t = si.next();
        System.assertEquals('<', t.lexeme);
        System.assertEquals(SOQL.TokenType.LESS, t.type);

        t = si.next();
        System.assertEquals('>', t.lexeme);
        System.assertEquals(SOQL.TokenType.GREATER, t.type);

        t = si.next();
        System.assertEquals('<=', t.lexeme);
        System.assertEquals(SOQL.TokenType.LESS_EQUAL, t.type);

        /*t = si.next();
        System.assertEquals('=<', t.lexeme);
        System.assertEquals(SOQL.TokenType.LESS_EQUAL, t.type);*/

        t = si.next();
        System.assertEquals('>=', t.lexeme);
        System.assertEquals(SOQL.TokenType.GREATER_EQUAL, t.type);

        /* t = si.next();
         System.assertEquals('=>', t.lexeme);
         System.assertEquals(SOQL.TokenType.GREATER_EQUAL, t.type);*/

        t = si.next();
        System.assertEquals('=', t.lexeme);
        System.assertEquals(SOQL.TokenType.EQUAL, t.type);

        t = si.next();
        System.assertEquals(':', t.lexeme);
        System.assertEquals(SOQL.TokenType.COLON, t.type);

        t = si.next();
        System.assertEquals(',', t.lexeme);
        System.assertEquals(SOQL.TokenType.COMMA, t.type);

        t = si.next();
        System.assertEquals('', t.lexeme);
        System.assertEquals(SOQL.TokenType.EOF, t.type);
    }

    @IsTest
    static void otherSymbols() {
        SOQL.ScannerImpl si = new SOQL.ScannerImpl('an_identifier%');

        SOQL.Token t = si.next();
        System.assertEquals('an_identifier', t.lexeme);
        System.assertEquals(SOQL.TokenType.IDENTIFIER, t.type);

        t = si.next();
        System.assertEquals('%', t.lexeme);
        System.assertEquals(SOQL.TokenType.SYMBOL, t.type);
    }

    @IsTest
    static void badtoken () {
        try {
            SOQL.ScannerImpl si = new SOQL.ScannerImpl('!<');
            si.next();
            System.assert(false);
        } catch (ParserException spe) {
            System.assert(true);
        }
        try {
            SOQL.ScannerImpl si = new SOQL.ScannerImpl('\\');
            si.next();
            System.assert(false);
        } catch (ParserException spe) {
            System.assert(true);
        }
    }

    /*
     * Lexer-specific unit tests.
     *
     * These test specific tokens individually and not SOQL statements as a whole.
     * That's left to the parser tests.
     */

    @IsTest
    static void test_StringLiteralsEscape () {
        String stringToLex2 = '\'Hello \\\' World\'';
        SOQL.Scanner lexer2 = new SOQL.ScannerImpl(stringToLex2);
        SOQL.Token t2 = lexer2.next();
        System.assertEquals('\'Hello \\\' World\'', t2.lexeme);
    }

    @IsTest
    static void test_Escape_fail () {
        String stringToLex2 = '\\r';
        SOQL.Scanner lexer2 = new SOQL.ScannerImpl(stringToLex2);
        try {
            lexer2.next();
            System.assert(false);
        } catch (Exception e) {
            System.assert(e instanceof ParserException);
        }
    }

    @IsTest
    static void test_tokenBoundary () {
        String stringToLex2 = 'field=\'literal\' field=:var';
        SOQL.Scanner lexer2 = new SOQL.ScannerImpl(stringToLex2);

        System.assertEquals(SOQL.TokenType.IDENTIFIER, lexer2.next().type);
        System.assertEquals(SOQL.TokenType.EQUAL, lexer2.next().type);
        System.assertEquals(SOQL.TokenType.TOKEN_STRING, lexer2.next().type);
        System.assertEquals(SOQL.TokenType.IDENTIFIER, lexer2.next().type);
        System.assertEquals(SOQL.TokenType.EQUAL, lexer2.next().type);
        System.assertEquals(SOQL.TokenType.COLON, lexer2.next().type);
        System.assertEquals(SOQL.TokenType.IDENTIFIER, lexer2.next().type);
    }

    @IsTest
    static void test_Performance01 () {
        String stringToLex2 = '\'Hello World\'';

        Long timerMS = 1000;
        Long counter = 0;
        while (Limits.getCpuTime() < timerMS) {
            SOQL.Scanner lexer2 = new SOQL.ScannerImpl(stringToLex2);
            SOQL.Token t2;
            do {
                t2 = lexer2.next();
            } while (!(t2.type == SOQL.TokenType.EOF));
            counter++;

            if (Math.mod(counter, 1000) == 0) {
                //
            }
        }


        System.assert(true);
    }

    /*
     * Performance test of string-based stream reading vs array of integers (characters)
     *
     * nextToken() Count: 811560,590342,780015
     * nextToken2() Count: 791420,512544,781913
     *
     * nextToken() Count: 750238,830278,587770
     * nextToken2() Count: 763637,830888,832246
     *
     * The bigger the string, the slower nextToken() is.
     *
    @isTest
    static void test_Performance_02() {
        /*SOQL.SOQLLexer lexer1 = new SOQL.SOQLLexer(soql);
        Long timeA      = System.currentTimeMillis();
        Long timeB      = timeA + timer;
        Long counterA   = 0;
        while (System.currentTimeMillis() < timeB) {
            String token;
            while ((token = lexer1.nextToken()) != null) {
                //
            }
            counterA++;
        }

        SOQL.SOQLLexer2 lexer2 = new SOQL.SOQLLexer2(soql);
        timeA       = System.currentTimeMillis();
        timeB       = timeA + timer;
        Long counterB   = 0;
        while (System.currentTimeMillis() < timeB) {
            String token;
            while ((token = lexer2.nextToken2()) != null) {
                //
            }
            counterB++;
        }



    }

    *@isTest
    static void test_tokens() {
        String stringToLex = '';
        for (String s : SOQL.TOKEN_TYPES.keySet()) {
            stringToLex += s;
            stringToLex += ' ';
        }

        SOQL.SOQLLexer lexer = new SOQL.SOQLLexer(null, stringToLex);
        SOQL.Token t2;
        do {
            t2= lexer.nextToken();
            t2.typeCode();
        } while (!(t2 instanceof SOQL.EOFToken));
    }*/

    @IsTest
    static void expr () {
        TestExpr te = new TestExpr(null);
        te.validate(true, true);
        System.assert(true);

        new SOQL.FieldPartExpr(null, null, null).parse(null); // code coverage
    }
    public class TestExpr extends SOQL.Expr {
        public TestExpr (SOQL.Token t) {
            super(t, null);
        }
        public override void parse (SOQL.Scanner p) {

        }
        public override SObjectType getSObjectType () {
            return null;
        }

    }

    @IsTest
    static void stringutil_format () {
        System.assertEquals('Hello World', StringUtils.format('Hello {param}').set(null, null).set('{param}', 'World').toString());
    }

    @IsTest
    static void where_compares () {
        Opportunity o = new Opportunity(
                AccountId = '001000000000001AAA'
                , Amount = 10
                , CloseDate = Date.newInstance(2023,6,15)
                , IsPrivate = true
                , Name = 'Test'
        );

        // null
        testWhere('AccountId != NULL', o, true);
        testWhere('AccountId = NULL', o, false);

        // String
        testWhere('AccountId = \'001000000000001AAA\'', o, true);
        testWhere('AccountId = \'001000000000001AAB\'', o, false);

        // Number
        testWhere('Amount != 10', o, false);
        testWhere('Amount != 9', o, true);
        testWhere('Amount = 10', o, true);
        testWhere('Amount = 9', o, false);
        testWhere('Amount >= 10', o, true);
        testWhere('Amount <= 10', o, true);
        testWhere('Amount > 10', o, false);
        testWhere('Amount < 10', o, false);
        testWhere('Amount > 9', o, true);
        testWhere('Amount < 11', o, true);

        // Date
        testWhere('CloseDate != 2023-06-15', o, false);
        testWhere('CloseDate != 2023-06-14', o, true);
        testWhere('CloseDate = 2023-06-15', o, true);
        testWhere('CloseDate = 2023-06-14', o, false);
        testWhere('CloseDate >= 2023-06-15', o, true);
        testWhere('CloseDate <= 2023-06-15', o, true);
        testWhere('CloseDate > 2023-06-15', o, false);
        testWhere('CloseDate < 2023-06-15', o, false);
        testWhere('CloseDate > 2023-06-14', o, true);
        testWhere('CloseDate < 2023-06-16', o, true);

        // Datetime
        Event e = new Event(
                StartDateTime = Datetime.newInstanceGmt(2023, 6, 15, 12,5,32)
        );
        testWhere('StartDateTime != 2023-06-15T12:05:32.000Z', e, false);
        testWhere('StartDateTime != 2023-06-15T12:05:33.000Z', e, true);
        testWhere('StartDateTime = 2023-06-15T12:05:32.000Z', e, true);
        testWhere('StartDateTime = 2023-06-15T12:05:33.000Z', e, false);
        testWhere('StartDateTime >= 2023-06-15T12:05:32.000Z', e, true);
        testWhere('StartDateTime <= 2023-06-15T12:05:32.000Z', e, true);
        testWhere('StartDateTime > 2023-06-15T12:05:32.000Z', e, false);
        testWhere('StartDateTime < 2023-06-15T12:05:32.000Z', e, false);
        testWhere('StartDateTime > 2023-06-15T12:05:31.000Z', e, true);
        testWhere('StartDateTime < 2023-06-15T12:05:33.000Z', e, true);

        // Boolean
        testWhere('IsPrivate = true', o, true);
        testWhere('IsPrivate != true', o, false);
        testWhere('IsPrivate = false', o, false);
        testWhere('IsPrivate != false', o, true);
        o.IsPrivate = false;
        testWhere('IsPrivate = false', o, true);
        testWhere('IsPrivate = true', o, false);
        testWhere('IsPrivate != false', o, false);
        testWhere('IsPrivate != true', o, true);

        testWhere('AccountId != NULL AND NOT IsPrivate = false', o, false); // true !true
        testWhere('AccountId != NULL AND (IsPrivate = false OR Amount = 11)', o, true); // true (true false)

        // IN / List<String>
        testWhere('Name IN (\'Foo\',\'Bar\',\'Test\')', o, true);
        testWhere('Name IN (\'Foo\',\'Bar\',\'Hello World\')', o, false);

        // NOT IN
        testWhere('Name NOT IN (\'Foo\',\'Bar\')', o, true);
        testWhere('Name NOT IN (\'Foo\',\'Bar\',\'TEST\')', o, false);

        // LIKE
        testWhere('Name LIKE \'Test\'', o, true);
        testWhere('Name LIKE \'Te%\'', o, true);
        testWhere('Name LIKE \'%st\'', o, true);
        testWhere('Name LIKE \'%es%\'', o, true);
        testWhere('Name LIKE \'%foo%\'', o, false);

        // Bind var
        testWhere('Name = :oppyName', o, true, new Map<String,Object>{
                'oppyName' => 'Test'
        });
        testWhere('Name != :oppyName', o, true, new Map<String,Object>{
                'oppyName' => 'FooBar'
        });
        testWhere('Name != :oppyName', o, false, new Map<String,Object>{
                'oppyName' => 'Test'
        });
        testWhere('Name = :oppyName', o, false, new Map<String,Object>{
                'oppyName' => 'FooBar'
        });

        // Logic
        testWhere('AccountId != NULL AND IsPrivate = false', o, true);  // true true
        testWhere('AccountId != NULL OR IsPrivate = true', o, true);    // true false
        testWhere('AccountId = NULL OR IsPrivate = true', o, false);    // false false
        testWhere('AccountId = NULL OR IsPrivate = false', o, true);    // false true

        /* Only Strings and semi/anti-joins are supported for IN
        // List<Decimal>
        testWhere('Amount IN (1,2,3,4.5,0.6,10)', o, true);
        testWhere('Amount IN (1,2)', o, false);
        // List<Date>
        testWhere('CloseDate IN (2023-06-16,2023-06-15)', o, true);
        testWhere('CloseDate IN (2023-06-16,2023-06-17)', o, false);
        // List<Datetime>
        testWhere('StartDateTime IN (2023-06-15T12:05:33.000Z,2023-06-15T12:05:32.000Z)', e, true);
        testWhere('StartDateTime IN (2023-06-15T12:05:33.000Z)', e, false);*/
    }

    @IsTest
    static void updateStmt_setRecord() {
        Contact c = new Contact();
        c.Account = new Account(
                Description = 'Tony'
        );

        SOQL.UpdateDML us = (SOQL.UpdateDML)SOQL.parseDML(null, 'UPDATE Contact ' +
                'SET FirstName = Account.Description,' +
                'LastName = \'Stark\'');
        us.execute(c, null);

        System.Assert.areEqual('Tony', c.FirstName);
        System.Assert.areEqual('Stark', c.LastName);
    }

    @IsTest
    static void updateStmt_matchRecord() {
        Contact c1 = new Contact(
                Phone = '(*************',
                Email = '<EMAIL>',
                Account = new Account(
                        Phone = '(*************'
                ),
                LastName = 'Test'
        );
        Contact c2 = new Contact(
                Phone = '(*************',
                Email = '<EMAIL>',
                Account = new Account(
                        Phone = '(*************3'
                )
        );
        Account c3 = new Account(
                Phone = '(*************'
        );

        SOQL.UpdateDML us = (SOQL.UpdateDML)SOQL.parseStmt(null,
                'UPDATE Contact ' +
                        'SET FirstName = Account.Description ' +
                        'WHERE Phone = Account.Phone AND Email = \'<EMAIL>\' AND LastName = :lastName');
        Map<String,Object> bindMap = new Map<String,Object>{
                'lastName' => 'Test'
        };
        System.assert(us.matches(c1, bindMap));
        System.assert(!us.matches(c2, bindMap));
        System.assert(!us.matches(c3, bindMap));
        System.assert(!us.matches(c3));
    }

    @IsTest
    static void updateStmt_toDataSOQL() {

        SOQL.UpdateDML us = (SOQL.UpdateDML)SOQL.parseStmt(null,
                'UPDATE Contact ' +
                        'SET FirstName = Account.Description ' +
                        'WHERE Phone = Account.Phone AND (Email = \'<EMAIL>\' OR Email IN (\'a\',\'b\')) AND (NOT FirstName = \'Teddy\') AND CALENDAR_MONTH(CreatedDate) = \'January\'');
        us.getDataSOQL();
        System.Assert.areEqual('WHERE (((Phone = Account.Phone AND (Email = \'<EMAIL>\' OR Email IN (\'a\',\'b\'))) AND (NOT (FirstName = \'Teddy\'))) AND CALENDAR_MONTH(CreatedDate) = \'January\')', us.where_stmt.toSOQL());
    }

    @IsTest
    static void updateStmt_count() {
        Account a = new Account(
                Name = 'Test',
                BillingStreet = '123 N Main St'
        );
        insert a;
        insert new Contact(
                FirstName = 'Test',
                LastName = 'Test',
                AccountId = a.Id
        );

        SOQL.UpdateDML us = (SOQL.UpdateDML)SOQL.parseStmt(null,
                'UPDATE Contact ' +
                        'SET FirstName = Account.Description ' +
                        'WHERE FirstName = \'Test\'');
        Integer i = us.getRecordCount();
        System.Assert.areEqual(1, i);
        us.getQueryLocator();
    }

    @IsTest
    static void updateStmt_getRecords() {
        Account a = new Account(
                Name = 'Test',
                BillingStreet = '123 N Main St'
        );
        insert a;
        insert new Contact(
                FirstName = 'Test',
                LastName = 'Test',
                AccountId = a.Id
        );

        SOQL.UpdateDML us = (SOQL.UpdateDML)SOQL.parseStmt(null,
                'UPDATE Contact ' +
                        'SET FirstName = Account.Description ' +
                        'WHERE FirstName = \'Test\'');
        List<SObject> recs = us.getRecords();
        System.Assert.areEqual(1, recs.size());
    }

    @IsTest
    static void updateDML_execute() {
        Account a = new Account(
                Name = 'Test',
                BillingStreet = '123 N Main St'
        );
        insert a;
        insert new Contact(
                FirstName = 'Test',
                LastName = 'Test',
                AccountId = a.Id
        );

        Test.startTest();
        DMLBatch.go('UPDATE Contact SET MailingStreet = Account.BillingStreet WHERE MailingStreet != Account.BillingStreet');
        Test.stopTest();
    }

    @IsTest
    static void deleteDML_execute() {
        Account a = new Account(
                Name = 'Test',
                BillingStreet = '123 N Main St'
        );
        insert a;
        insert new Contact(
                FirstName = 'Test',
                LastName = 'Test',
                AccountId = a.Id
        );

        Test.startTest();
        DMLBatch.go('DELETE FROM Contact');
        Test.stopTest();

        System.Assert.areEqual(0, [SELECT COUNT() FROM Contact]);
    }

    @IsTest
    static void deleteteStmt_execute() {
        Account a = new Account(
                Name = 'Test',
                BillingStreet = '123 N Main St'
        );
        insert a;
        Contact c = new Contact(
                FirstName = 'Test',
                LastName = 'Test',
                AccountId = a.Id
        );
        insert c;

        SOQL.DeleteDML us = (SOQL.DeleteDML)SOQL.parseStmt(null,
                'DELETE FROM Contact');
        us.execute(c);
    }

    @IsTest
    static void misc_Stmt() {
        SOQL.Stmt slct = SOQL.parseStmt(null,'SELECT Id FROM Contact');
        SOQL.Stmt udml = SOQL.parseStmt(null,'UPDATE Contact SET FirstName = Account.Description');
        SOQL.Stmt ddml = SOQL.parseStmt(null,'DELETE FROM Contact');

        System.Assert.isTrue(slct instanceof SOQL.Query);
        System.Assert.isTrue(udml instanceof SOQL.UpdateDML);
        System.Assert.isTrue(ddml instanceof SOQL.DeleteDML);

        System.Assert.areEqual(DB.Action.EDIT, ((SOQL.UpdateDML)udml).getAction());
        System.Assert.areEqual(DB.Action.DESTROY, ((SOQL.DeleteDML)ddml).getAction());
    }

    @IsTest
    static void update_syntax() {
        testUpdateDML('UPDATE Contact SET bla = bla', true);
        testUpdateDML('UPDATE Contact Account', false); // SET should follow the first identifier
        testUpdateDML('DELETE Contact', false); // UPDATE should be first token
        testUpdateDML('UPDATE Contact SET bla = foo WHERE SELECT', false); // The filter must have identifier
        testUpdateDML('UPDATE Contact SET bla = foo WHERE foo ', false); // The filter specifies no comparison operator
        testUpdateDML('UPDATE Contact SET bla = foo WHERE foo =', false); // The filter specifies no comparison value
        testUpdateDML('UPDATE Contact SET bla = foo WHERE foo = %', false); // The filter has a bad no comparison value
        testUpdateDML('UPDATE Contact SET bla = foo WHERE foo NOT =', false); // Bad NOT usage
        testUpdateDML('UPDATE Contact SET bla = foo WHERE foo IN (\'bar\'&', false); // Bad IN
        testUpdateDML('UPDATE Contact SET bla = foo WHERE foo INCLUDES ', false); // Next token must be (
        testUpdateDML('UPDATE Contact SET SELECT', false); // Set must have identifier
        testUpdateDML('UPDATE Contact SET bla ', false); // Set must have assignment operator
        testUpdateDML('UPDATE Contact SET bla =', false); // assignment must have value (literal or identifier)
        testUpdateDML('UPDATE Contact SET bla = %', false); // assignment must have value (literal or identifier)
        testSetStmt('bla = bla', true);
        testSetStmt('bla = bla, foo = bar, hello = world', true);
        testSetStmt('WHERE', false); // identifier should follow SET
        testSetStmt('bla = bla WHERE', true);
        testSetStmt('bla = bla, WHERE', false);
        testSetStmt('foo = bar+', false);
    }

    @IsTest
    static void delete_syntax() {
        testDeleteDML('DELETE FROM Contact', true);
        testDeleteDML('DELETE FROM Contact WHERE FirstName = \'Test\'', true);

        testDeleteDML('DELETE Contact', false); // FROM should follow DELETE
        testDeleteDML('DELETE FROM', false); // Identifier should follow from
        testDeleteDML('DELETE FROM WHERE', false); // Identifier should follow from
        testDeleteDML('DELETE FROM Contact WHERE', false); // Identifier should follow WHERE
    }

    @IsTest
    static void misc_coverage() {
        SOQL.ScannerImpl si = new SOQL.ScannerImpl('SELECT');
        SOQL.SetFieldExpr sfe = new SOQL.SetFieldExpr(null,null);
        sfe.getSObjectType();
        try {
            sfe.parse(si);
            System.Assert.isFalse(true);
        } catch (Exception e) {
            System.Assert.isTrue(true);
        }

        SOQL.ConditionFieldExpr cfe = new SOQL.ConditionFieldExpr(null);
        cfe.getSObjectType();
        try {
            si.reset();
            cfe.parse(si);
            System.Assert.isFalse(true);
        } catch (Exception e) {
            System.Assert.isTrue(true);
        }

        cfe = new SOQL.ConditionFieldExpr(null);
        cfe.parse(new SOQL.ScannerImpl('CALENDAR_MONTH(Foo) = 1'));
        try {
            cfe.interpret(null, null);
            System.Assert.isFalse(true);
        } catch (Exception e) { // Functions not supported
            System.Assert.isTrue(true);
        }

        cfe = new SOQL.ConditionFieldExpr(null);
        try {
            cfe.interpret(null, null);
            System.Assert.isFalse(true);
        } catch (Exception e) { // Functions not supported
            System.Assert.areEqual('The filter specifies no comparison.', e.getMessage());
        }

        SOQL.Token t = new SOQL.Token(SOQL.TokenType.COLON, ':', ':', 1, 1);
        SOQL.ComparisonValueExpr cve = new SOQL.ComparisonValueExpr(t);
        cve.getSObjectType();
        try {
            si.reset();
            cve.parse(si);
            System.Assert.isFalse(true);
        } catch (Exception e) {
            System.Assert.isTrue(true);
        }
        try {
            cve.getValueForToken(new SOQL.Token(null, null, null, null, null));
            System.Assert.isFalse(true);
        } catch (Exception e) {
            System.Assert.isTrue(true);
        }
        cve = new SOQL.ComparisonValueExpr(null);
        cve.getValueForToken(null); // returns null

        try {
            cve.compare(null, SOQL.TokenType.COLON, null);
            System.Assert.isFalse(true);
        } catch (Exception e) {
            System.Assert.isTrue(true);
        }

        try {
            cve.compare(SOQL.TokenType.INCLUDES, null);
            System.Assert.isFalse(true);
        } catch (Exception e) {
            System.Assert.isTrue(true);
        }

        try {
            cve.compare(SOQL.TokenType.EXCLUDES, null);
            System.Assert.isFalse(true);
        } catch (Exception e) {
            System.Assert.isTrue(true);
        }

        new SOQL.Token(null, null, null, null, null).toSOQL();

        try {
            SOQL.CNot cn = new SOQL.CNot();
            cn.setRight(null);
            System.Assert.isFalse(true);
        } catch (Exception e) {
            System.Assert.isTrue(true);
        }

        SOQL.FieldExpr fe = new SOQL.FieldExpr(new SOQL.Token(SOQL.TokenType.IDENTIFIER, 'Foo', 'Foo', 1, 1), null);
        fe.parts.remove(0);
        System.Assert.areEqual(null, fe.getValue(new Contact()));
    }


    static void testWhere(String whereExpr, SObject record, Boolean expected) {
        testWhere(whereExpr, record, expected, new Map<String,Object>());
    }
    static void testWhere(String whereExpr, SObject record, Boolean expected, Map<String,Object> bindMap) {
        SOQL.ScannerImpl si = new SOQL.ScannerImpl(whereExpr);
        SOQL.WhereStmt stmt = new SOQL.WhereStmt(null);
        stmt.parse(si);
        System.assertEquals(expected, stmt.interpret(record, bindMap));
    }
    static void testUpdateDML(String followingStmtTxt, Boolean expected) {
        testStmt(new SOQL.UpdateDML(null, null), followingStmtTxt, expected);
    }
    static void testDeleteDML(String followingStmtTxt, Boolean expected) {
        testStmt(new SOQL.DeleteDML(null, null), followingStmtTxt, expected);
    }
    static void testSetStmt(String followingStmtTxt, Boolean expected) {
        testStmt(new SOQL.SetStmt(null, null), followingStmtTxt, expected);
    }
    static void testStmt(SOQL.Stmt stmt, String followingStmtTxt, Boolean expected) {
        SOQL.ScannerImpl si = new SOQL.ScannerImpl(followingStmtTxt);
        try {
            stmt.parse(si);
            System.Assert.isTrue(expected);
        } catch (Exception e) {
            System.debug(e.getStackTraceString());
            System.Assert.isFalse(expected);
        }
    }
}