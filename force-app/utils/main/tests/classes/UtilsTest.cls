/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 11/20/23.
 */

@IsTest
private class UtilsTest {
    @IsTest
    static void nullValue() {
        System.Assert.areEqual(Date.today(), Utils.nullValue(Date.today(), Date.today().addDays(-1)));
        System.Assert.areEqual(Date.today().addDays(-1), Utils.nullValue(null, Date.today().addDays(-1)));

        Datetime dt1 = Datetime.now();
        Datetime dt2 = Datetime.now().addDays(1);
        System.Assert.areEqual(dt1, Utils.nullValue(dt1, dt2));
        System.Assert.areEqual(dt2, Utils.nullValue(null, dt2));

        Decimal d1 = 1.0;
        Decimal d2 = 2.0;
        System.Assert.areEqual(d1, Utils.nullValue(d1, d2));
        System.Assert.areEqual(d2, Utils.nullValue(null, d2));

        Integer i1 = 1;
        Integer i2 = 2;
        System.Assert.areEqual(i1, Utils.nullValue(i1, i2));
        System.Assert.areEqual(i2, Utils.nullValue(null, i2));

        String s1 = '1';
        String s2 = '2';
        System.Assert.areEqual(s1, Utils.nullValue(s1, s2));
        System.Assert.areEqual(s2, Utils.nullValue(null, s2));

        Boolean b1 = true;
        Boolean b2 = false;
        System.Assert.areEqual(b1, Utils.nullValue(b1, b2));
        System.Assert.areEqual(b2, Utils.nullValue(null, b2));

        Object o1 = new UtilsTest();
        Object o2 = new UtilsTest();
        System.Assert.areEqual(o1, Utils.nullValue(o1, o2));
        System.Assert.areEqual(o2, Utils.nullValue(null, o2));
    }

    @IsTest
    static void getStackTraceString() {
        Utils.getStackTrace();
    }
}