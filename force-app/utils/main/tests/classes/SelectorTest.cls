/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 6/21/23.
 */

@IsTest
private class SelectorTest {
    @IsTest
    static void testBehavior() {
        Account a = new Account(Name = 'Test');
        insert a;

        TestSelector aslctr = new TestSelector('Account');
        //System.assertEquals(1, aslctr.getAllRecordsAllFields().size());
        //System.assertNotEquals(null, aslctr.getRecordByIdAllFields(a.Id));
        System.assertEquals(1, aslctr.addField('Name')
                .addField('BillingStreet')
                .addFilter('Name', Selector.Comparator.EQUALS, 'Test')
                .addOrderBy('Name', true, true)
                .setUseLocking(true)
                .runQuery().size());

        aslctr.setFields(new Set<String>{'Name','ShippingStreet'});
        aslctr.setQueryString('SELECT Id, Name FROM Account WHERE :Name = \'bla\'');
        aslctr.setFilterValues(new Map<String,Object>{
                'Name' => 'Test'
        });
    }

    private class TestSelector extends Selector {
        private TestSelector(String obj) {
            super(obj);
        }
    }
}