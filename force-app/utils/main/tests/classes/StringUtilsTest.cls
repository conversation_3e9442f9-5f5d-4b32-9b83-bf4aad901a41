/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 3/23/23.
 */
@IsTest
public class StringUtilsTest {

    @IsTest
    static void base62Coding() {
        Long l1     = 9223372036854775807L;
        String s1   = StringUtils.base62encode(l1);
        Long l2     = StringUtils.base62decode(s1);
        System.assertEquals(l1, l2);
        
        System.assertEquals('0', StringUtils.base62encode(0));
        System.assertEquals('1', StringUtils.base62encode(1));
        System.assertEquals('2', StringUtils.base62encode(2));
        System.assertEquals('3', StringUtils.base62encode(3));
        System.assertEquals('4', StringUtils.base62encode(4));
        System.assertEquals('5', StringUtils.base62encode(5));
        System.assertEquals('6', StringUtils.base62encode(6));
        System.assertEquals('7', StringUtils.base62encode(7));
        System.assertEquals('8', StringUtils.base62encode(8));
        System.assertEquals('9', StringUtils.base62encode(9));
        System.assertEquals('A', StringUtils.base62encode(10));
        System.assertEquals('B', StringUtils.base62encode(11));
        System.assertEquals('C', StringUtils.base62encode(12));
        System.assertEquals('D', StringUtils.base62encode(13));
        System.assertEquals('E', StringUtils.base62encode(14));
        System.assertEquals('F', StringUtils.base62encode(15));
        System.assertEquals('G', StringUtils.base62encode(16));
        System.assertEquals('H', StringUtils.base62encode(17));
        System.assertEquals('I', StringUtils.base62encode(18));
        System.assertEquals('J', StringUtils.base62encode(19));
        System.assertEquals('K', StringUtils.base62encode(20));
        System.assertEquals('L', StringUtils.base62encode(21));
        System.assertEquals('M', StringUtils.base62encode(22));
        System.assertEquals('N', StringUtils.base62encode(23));
        System.assertEquals('O', StringUtils.base62encode(24));
        System.assertEquals('P', StringUtils.base62encode(25));
        System.assertEquals('Q', StringUtils.base62encode(26));
        System.assertEquals('R', StringUtils.base62encode(27));
        System.assertEquals('S', StringUtils.base62encode(28));
        System.assertEquals('T', StringUtils.base62encode(29));
        System.assertEquals('U', StringUtils.base62encode(30));
        System.assertEquals('V', StringUtils.base62encode(31));
        System.assertEquals('W', StringUtils.base62encode(32));
        System.assertEquals('X', StringUtils.base62encode(33));
        System.assertEquals('Y', StringUtils.base62encode(34));
        System.assertEquals('Z', StringUtils.base62encode(35));
        System.assertEquals('a', StringUtils.base62encode(36));
        System.assertEquals('b', StringUtils.base62encode(37));
        System.assertEquals('c', StringUtils.base62encode(38));
        System.assertEquals('d', StringUtils.base62encode(39));
        System.assertEquals('e', StringUtils.base62encode(40));
        System.assertEquals('f', StringUtils.base62encode(41));
        System.assertEquals('g', StringUtils.base62encode(42));
        System.assertEquals('h', StringUtils.base62encode(43));
        System.assertEquals('i', StringUtils.base62encode(44));
        System.assertEquals('j', StringUtils.base62encode(45));
        System.assertEquals('k', StringUtils.base62encode(46));
        System.assertEquals('l', StringUtils.base62encode(47));
        System.assertEquals('m', StringUtils.base62encode(48));
        System.assertEquals('n', StringUtils.base62encode(49));
        System.assertEquals('o', StringUtils.base62encode(50));
        System.assertEquals('p', StringUtils.base62encode(51));
        System.assertEquals('q', StringUtils.base62encode(52));
        System.assertEquals('r', StringUtils.base62encode(53));
        System.assertEquals('s', StringUtils.base62encode(54));
        System.assertEquals('t', StringUtils.base62encode(55));
        System.assertEquals('u', StringUtils.base62encode(56));
        System.assertEquals('v', StringUtils.base62encode(57));
        System.assertEquals('w', StringUtils.base62encode(58));
        System.assertEquals('x', StringUtils.base62encode(59));
        System.assertEquals('y', StringUtils.base62encode(60));
        System.assertEquals('z', StringUtils.base62encode(61));

        System.assertEquals('10', StringUtils.base62encode(62));
        System.assertEquals('20', StringUtils.base62encode(124));
    }

    @IsTest
    static void format() {
        String formatted = StringUtils.format('Hello {0}. I am pleased to meet :p2 %3')
                .set('{0}', 'World')
                .set(':p2', 'you')
                .set('%3', 'today')
                .toString();
        System.assertEquals('Hello World. I am pleased to meet you today', formatted);

        System.Assert.areEqual('Hello, foo, Bar, 1 World', StringUtils.format('Hello, {param} World').set('{param}', new List<String>{'foo','Bar','1'}, null).toString());
    }

    @IsTest
    static void capitalizeWords() {
        System.assertEquals('Hello World! Capitalization Is Fun!', StringUtils.capitalizeWords('HELLO World! caPitaLIZATION is fun!'));
        StringUtils.capitalizeWords('');
    }

    @IsTest
    static void blankValue() {
        System.assertEquals('', StringUtils.blankValue(null));
        System.assertEquals('', StringUtils.blankValue(''));
        System.assertEquals('', StringUtils.blankValue(' '));
        System.assertEquals('', StringUtils.blankValue('\n'));
        System.assertEquals('', StringUtils.blankValue('\t'));
        System.assertEquals('Hello World', StringUtils.blankValue(null, 'Hello World'));
        System.assertEquals('Hello World', StringUtils.blankValue('', 'Hello World'));
        System.assertEquals('Hello World', StringUtils.blankValue(' ', 'Hello World'));
        System.assertEquals('Hello World', StringUtils.blankValue('\n', 'Hello World'));
        System.assertEquals('Hello World', StringUtils.blankValue('\t', 'Hello World'));
    }

    @IsTest
    static void equals() {
        System.assert(StringUtils.equals('',''));
        System.assert(StringUtils.equals('',null));
        System.assert(StringUtils.equals(null,null));
        System.assert(StringUtils.equals(null,''));
        System.assert(StringUtils.equals('1','1'));
        System.assert(!StringUtils.equals('1','2'));
    }

    @IsTest
    static void equalsIgnoreCase() {
        System.assert(StringUtils.equalsIgnoreCase('abc','abc'));
        System.assert(StringUtils.equalsIgnoreCase('ABC','abc'));
        System.assert(StringUtils.equalsIgnoreCase('abc','ABC'));
        System.assert(StringUtils.equalsIgnoreCase('',''));
        System.assert(StringUtils.equalsIgnoreCase('',null));
        System.assert(StringUtils.equalsIgnoreCase(null,''));
        System.assert(StringUtils.equalsIgnoreCase(null,null));
    }
}