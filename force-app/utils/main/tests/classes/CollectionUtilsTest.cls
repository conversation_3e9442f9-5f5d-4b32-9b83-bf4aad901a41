/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 1/2/24.
 */

@IsTest
private class CollectionUtilsTest {
    @IsTest
    static void removeAllAtAndAfter() {
        List<Integer> lst1 = new List<Integer>{0,1,2,3,4,5,6,7,8,9,10,11,12};
        CollectionUtils.removeAllAtAndAfter(lst1, 5);

        System.Assert.areEqual(5, lst1.size());
        System.Assert.areEqual(4, lst1.get(lst1.size()-1));

        CollectionUtils.removeAllAtAndAfter(lst1, 10);
        System.Assert.areEqual(5, lst1.size());
    }

    @IsTest
    static void test() {
        List<Integer> originalList = new List<Integer>{0,1,2,3,4,5};
        CollectionUtils.Iterator ci = new CollectionUtils.Iterator(originalList);

        List<Integer> newList   = new List<Integer>();
        while (ci.hasNext()) {
            Integer i = (Integer)ci.next();
            System.debug(i);
            newList.add(i);
            ci.remove();
        }

        System.assertEquals(0, originalList.size());
        System.assertEquals(6, newList.size());
        System.assertEquals(0, newList.get(0));
        System.assertEquals(1, newList.get(1));
        System.assertEquals(2, newList.get(2));
        System.assertEquals(3, newList.get(3));
        System.assertEquals(4, newList.get(4));
        System.assertEquals(5, newList.get(5));
    }
}