/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 11/20/23.
 */

@IsTest
private class LookupSearchResultTest {
    @IsTest
    static void testBehavior() {
        new LookupSearchResult();
        LookupSearchResult lsr = new LookupSearchResult(
                IdUtilsTest.generateNextId(Account.SObjectType),
                'Account',
                'standard:account',
                'Account',
                'Account'
        );

        lsr.getId();
        lsr.getSObjectType();
        lsr.getIcon();
        lsr.getTitle();
        lsr.getSubtitle();
        lsr.getRecord();
    }
}