/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

@IsTest(IsParallel=false)
public class DBTest {

    static {
        //LicenseManager.setLicense(LicenseManagerTest.TestLicenseKey_orgId.class);
    }

    @IsTest
    static void read () {

        DB.init().read('SELECT Id ' +
                'FROM Account ' +
                'WHERE Name = :name0 ' +
                'OR Name = :name1 ' +
                'OR Name = :name2 ' +
                'OR Name = :name3 ' +
                'OR Name = :name4 ' +
                'OR Name = :name5 ' +
                'OR Name = :name6 ' +
                'OR Name = :name7 ' +
                'OR Name = :name8 ' +
                'OR Name = :name9 ' +
                'OR Name = :name10 ' +
                'OR Name = :name11 ' +
                'OR Name = :name12 ' +
                'OR Name = :name13 ' +
                'OR Name = :name14 ' +
                'OR Name = :name15 ' +
                'OR Name = :name16 ' +
                'OR Name = :name17 ' +
                'OR Name = :name18 ' +
                'OR Name = :name19 ')
                .setParam(':name0', 'Burlington')
                .setParam(':name1', 'Burlington')
                .setParam(':name2', 'Burlington')
                .setParam(':name3', 'Burlington')
                .setParam(':name4', 'Burlington')
                .setParam(':name5', 'Burlington')
                .setParam(':name6', 'Burlington')
                .setParam(':name7', 'Burlington')
                .setParam(':name8', 'Burlington')
                .setParam(':name9', 'Burlington')
                .setParam(':name10', 'Burlington')
                .setParam(':name11', 'Burlington')
                .setParam(':name12', 'Burlington')
                .setParam(':name13', 'Burlington')
                .setParam(':name14', 'Burlington')
                .setParam(':name15', 'Burlington')
                .setParam(':name16', 'Burlington')
                .setParam(':name17', 'Burlington')
                .setParam(':name18', 'Burlington')
                .setParam('name19', 'Burlington') // Deliberately testing that code injects the ":"
                .go();
        System.assert(true);
    }

    @IsTest
    static void read_localNamespaceObject () {

        DB.init().setNamespace('crudfls').read('SELECT Id ' +
                'FROM Account ' +
                'WHERE Name = :name0 ' +
                'OR Name = :name1 ' +
                'OR Name = :name2 ' +
                'OR Name = :name3 ' +
                'OR Name = :name4 ' +
                'OR Name = :name5 ' +
                'OR Name = :name6 ' +
                'OR Name = :name7 ' +
                'OR Name = :name8 ' +
                'OR Name = :name9 ')
                .setParam(':name0', 'Burlington')
                .setParam(':name1', 'Burlington')
                .setParam(':name2', 'Burlington')
                .setParam(':name3', 'Burlington')
                .setParam(':name4', 'Burlington')
                .setParam(':name5', 'Burlington')
                .setParam(':name6', 'Burlington')
                .setParam(':name7', 'Burlington')
                .setParam(':name8', 'Burlington')
                .setParam(':name9', 'Burlington')
                .go();
        System.assert(true);
    }

    @IsTest
    static void read_parameters () {

        String queryStr = 'SELECT Id FROM Contact WHERE Id IN :list';

        DB.init().read(queryStr).setParam(null, '');
        /*

        SOQLParser.Query qry = SOQLParser.parse(queryStr);

        String postParseQryStr = JSON.serializePretty(qry);
        */

        //System.assert(postParseQryStr.contains('::'));

        DB.init().read(queryStr).setParam(':list', new List<Id>{
        }).go();
        System.assert(true);

    }

    @IsTest
    static void read_noShare () {

        DB.init(false, false, false).read('SELECT Id ' +
                'FROM Account ' +
                'WHERE Name = :name0 ' +
                'OR Name = :name1 ' +
                'OR Name = :name2 ' +
                'OR Name = :name3 ' +
                'OR Name = :name4 ' +
                'OR Name = :name5 ' +
                'OR Name = :name6 ' +
                'OR Name = :name7 ' +
                'OR Name = :name8 ' +
                'OR Name = :name9 ')
                .setParam(':name0', 'Burlington')
                .setParam(':name1', 'Burlington')
                .setParam(':name2', 'Burlington')
                .setParam(':name3', 'Burlington')
                .setParam(':name4', 'Burlington')
                .setParam(':name5', 'Burlington')
                .setParam(':name6', 'Burlington')
                .setParam(':name7', 'Burlington')
                .setParam(':name8', 'Burlington')
                .setParam(':name9', 'Burlington')
                .go();
        System.assert(true);
    }

    @IsTest
    static void read_fail_crud () {

        System.runAs(TestUtils.createPlatformUser()) {
            // Platform users don't have access to Leads
            try {
                Test.startTest();
                DB.init().read('SELECT DoNotCall FROM Lead').go();
                System.assert(false);
            } catch (Exception e) {
                System.assert(e instanceof SecurityException.CRUDException);
            } finally {
                Test.stopTest();
            }
        }
    }

    @IsTest
    static void read_fail_fls () {

        System.runAs(TestUtils.createStandardUser()) {
            // Platform users don't have access to Leads
            try {
                Test.startTest();
                DB.init().read('SELECT DoNotCall FROM Lead').go();
                System.assert(false);
            } catch (Exception e) {
                System.assert(e instanceof SecurityException.FLSException);
            } finally {
                Test.stopTest();
            }
        }
    }

    @IsTest
    static void create_fail_crud () {

        Lead so = new Lead(LastName = 'Test', Company = 'Test', DoNotCall = true);

        System.runAs(TestUtils.createPlatformUser()) {
            // Platform users don't have access to Leads
            try {
                Test.startTest();
                DB.init(true, false, true).create(so);
                System.assert(false);
            } catch (Exception e) {

                System.assert(e instanceof SecurityException.CRUDException);
            } finally {
                Test.stopTest();
            }
        }
    }

    @IsTest
    static void create_fail_fls () {

        Lead so = new Lead(DoNotCall = true);

        System.runAs(TestUtils.createStandardUser()) {
            // For some weird reason, standard users don't have access to the DNC field by default
            try {
                Test.startTest();
                DB.init(false, true, true).create(so);
                System.assert(false);
            } catch (Exception e) {
                System.assert(e instanceof SecurityException.FLSException);
            } finally {
                Test.stopTest();
            }
        }
    }

    /**
     * This test validates that FLS checks are NOT performed on AutoNumber fields, which aren't createable/editable on
     * purpose because they're managed by Salesforce automatically.
     */
    @IsTest
    static void edit_fail_fls_autonumber () {

        Account a = new Account(Name = 'Test Account');
        insert a;

        Contract ctrct = new Contract(AccountId = a.Id);
        insert ctrct;

        ctrct = [
                SELECT Id,
                        ContractNumber
                FROM Contract
                WHERE Id = :ctrct.Id
        ]; // Get the contract and it's number

        System.runAs(TestUtils.createStandardUser()) {
            // For some weird reason, standard users don't have access to the DNC field by default
            try {
                Test.startTest();
                DB.init(false, true, true).edit(ctrct);
                System.assert(true);
            } catch (Exception e) {

                System.assert(false);
            } finally {
                Test.stopTest();
            }
        }
    }

    @IsTest
    static void edit_fail_crud () {

        Lead so = new Lead(LastName = 'Test', Company = 'Test', DoNotCall = true);

        System.runAs(TestUtils.createPlatformUser()) {
            // Platform users don't have access to Leads
            try {
                Test.startTest();
                DB.init(true, false, true).edit(so);
                System.assert(false);
            } catch (Exception e) {
                System.assert(e instanceof SecurityException.CRUDException);
            } finally {
                Test.stopTest();
            }
        }
    }

    @IsTest
    static void edit_fail_fls () {

        Lead so = new Lead(DoNotCall = true);

        System.runAs(TestUtils.createStandardUser()) {
            // For some weird reason, standard users don't have access to the DNC field by default
            try {
                Test.startTest();
                DB.init(false, true, true).edit(so);
                System.assert(false);
            } catch (Exception e) {
                System.assert(e instanceof SecurityException.FLSException);
            } finally {
                Test.stopTest();
            }
        }
    }

    @IsTest
    static void destroy_fail_crud () {

        Lead so = new Lead(LastName = 'Test', Company = 'Test', DoNotCall = true);

        System.runAs(TestUtils.createPlatformUser()) {
            // Platform users don't have access to Leads
            try {
                Test.startTest();
                DB.init(true, false, true).destroy(so);
                System.assert(false);
            } catch (Exception e) {
                System.assert(e instanceof SecurityException.CRUDException);
            } finally {
                Test.stopTest();
            }
        }
    }

    @IsTest
    static void undestroy_fail_crud () {

        Lead so = new Lead(LastName = 'Test', Company = 'Test', DoNotCall = true);

        System.runAs(TestUtils.createPlatformUser()) {
            // Platform users don't have access to Leads
            try {
                Test.startTest();
                DB.init(true, false, true).undestroy(so);
                System.assert(false);
            } catch (Exception e) {
                System.assert(e instanceof SecurityException.CRUDException);
            } finally {
                Test.stopTest();
            }
        }
    }

    // All of the tests above used their respective single sObject methods
    // The tests below test the the bulk capabilties

    @IsTest
    static void dml_bulk_share () {

        dml_bulk(true);
    }
    @IsTest
    static void dml_bulk_noshare () {

        dml_bulk(false);
    }
    static void dml_bulk (Boolean share) {
        Lead so = new Lead(LastName = 'Test', Company = 'Test', DoNotCall = true);

        System.runAs(TestUtils.createPlatformUser()) {
            Test.startTest();
            DB.init(false, false, share).create(new List<Lead>{
                    so
            });
            System.assert(true);

            so.LastName = 'Test2';
            DB.init(false, false, share).edit(new List<Lead>{
                    so
            });

            DB.init(false, false, share).destroy(new List<Lead>{
                    so
            });

            DB.init(false, false, share).undestroy(new List<Lead>{
                    so
            });

            Test.stopTest();
        }
    }

    @IsTest
    static void dml_bulk_crudfls () {

        dml_bulk_cf(true);
    }
    @IsTest
    static void dml_bulk_nocrudfls () {

        dml_bulk_cf(false);
    }
    static void dml_bulk_cf (Boolean share) {
        Lead so = new Lead(LastName = 'Test', Company = 'Test');

        System.runAs(TestUtils.createStandardUser()) {
            Test.startTest();
            DB.init(share, share, false).create(new List<Lead>{
                    so
            });
            System.assert(true);

            so.LastName = 'Test2';
            DB.init(share, share, false).edit(new List<Lead>{
                    so
            });

            DB.init(share, share, false).destroy(new List<Lead>{
                    so
            });

            DB.init(share, share, false).undestroy(new List<Lead>{
                    so
            });

            Test.stopTest();
        }
    }

    @IsTest
    static void dml_mixed () {

        Lead l = new Lead(LastName = 'Test', Company = 'Test');
        Account a = new Account(Name = 'Test');
        List<SObject> sos = new List<SObject>{
                l, a
        };

        System.runAs(TestUtils.createStandardUser()) {
            Test.startTest();
            DB.init().create(sos);
            System.assert(true);
            Test.stopTest();
        }
    }

    @IsTest
    static void dml_single () {

        Lead so = new Lead(LastName = 'Test', Company = 'Test');

        System.runAs(TestUtils.createStandardUser()) {
            Test.startTest();
            DB.init().create(so);
            System.assert(true);

            so.LastName = 'Test2';
            DB.init().edit(so);

            DB.init().destroy(so);

            DB.init().undestroy(so);

            Test.stopTest();
        }
    }

    @IsTest
    static void misc_cvg () {

        Lead so = new Lead(LastName = 'Test', Company = 'Test');

        DB myDb = DB.init();
        myDb = DB.init(false, false, false);
        myDb.doDML(new List<Lead>{
                so
        }, null);

        try {
            myDb.doDML(null, DB.Action.READ);
            System.assert(false);
        } catch (DB.DBException dbe) {
            System.assert(true);
        }

        try {
            myDb.doDML(new List<Lead>{
                    null
            }, DB.Action.READ);
            System.assert(false);
        } catch (DB.DBException dbe) {
            System.assert(true);
        }

        DB.init(new Database.DMLOptions());

        // Test > 20 params
        try {
            DB.Query q = DB.init().read('SELECT Id FROM Account');
            for (Integer i = 0; i < 25; i++) {
                q.setParam('param' + i, null);
            }
            System.assert(false);
        } catch (DB.DBException dbe) {
            System.assert(true);
        }

        // Test updating same paramName > 20 times won't fail
        DB.Query q = DB.init().read('SELECT Id FROM Account');
        for (Integer i = 0; i < 25; i++) {
            q.setParam(':param0', null);
        }
        System.assert(true);

        DB.getFieldToken(null, '', '');
    }

    // C R U D   F L S   T E S T S   H E R E

    @IsTest
    static void test_CRUD_create_success () {

        System.runAs(TestUtils.createStandardUser()) {
            Test.startTest();
            SObjectType sot = Schema.Lead.SObjectType;
            DB.validateCRUD(DB.Action.CREATE, sot);
            System.assert(true);
            Test.stopTest();
        }
    }

    @IsTest
    static void test_CRUD_create_fail () {

        System.runAs(TestUtils.createPlatformUser()) {
            try {
                Test.startTest();
                SObjectType sot = Schema.Lead.SObjectType;
                DB.validateCRUD(DB.Action.CREATE, sot);
                System.assert(false);
            } catch (Exception e) {
                System.assert(e instanceof SecurityException.CRUDException);
            } finally {
                Test.stopTest();
            }
        }
    }

    @IsTest
    static void test_CRUD_createoredit_fail () {

        System.runAs(TestUtils.createPlatformUser()) {
            try {
                Test.startTest();
                SObjectType sot = Schema.Lead.SObjectType;
                DB.validateCRUD(DB.Action.CREATEOREDIT, sot);
                System.assert(false);
            } catch (Exception e) {
                System.assert(e instanceof SecurityException.CRUDException);
            } finally {
                Test.stopTest();
            }
        }
    }

    @IsTest
    static void test_CRUD_read_success () {

        System.runAs(TestUtils.createStandardUser()) {
            Test.startTest();
            SObjectType sot = Schema.Lead.SObjectType;
            DB.validateCRUD(DB.Action.READ, sot);
            System.assert(true);
            Test.stopTest();
        }
    }

    @IsTest
    static void test_CRUD_read_fail () {


        System.runAs(TestUtils.createPlatformUser()) {
            try {
                Test.startTest();
                SObjectType sot = Schema.Lead.SObjectType;
                DB.validateCRUD(DB.Action.READ, sot);
                System.assert(false);
            } catch (Exception e) {
                System.assert(e instanceof SecurityException.CRUDException);
            } finally {
                Test.stopTest();
            }
        }
    }

    @IsTest
    static void test_CRUD_edit_success () {

        System.runAs(TestUtils.createStandardUser()) {
            Test.startTest();
            SObjectType sot = Schema.Lead.SObjectType;
            DB.validateCRUD(DB.Action.EDIT, sot);
            System.assert(true);
            Test.stopTest();
        }
    }

    @IsTest
    static void test_CRUD_edit_fail () {

        System.runAs(TestUtils.createPlatformUser()) {
            try {
                Test.startTest();
                SObjectType sot = Schema.Lead.SObjectType;
                DB.validateCRUD(DB.Action.EDIT, sot);
                System.assert(false);
            } catch (Exception e) {
                System.assert(e instanceof SecurityException.CRUDException);
            } finally {
                Test.stopTest();
            }
        }
    }

    @IsTest
    static void test_CRUD_destroy () {

        System.runAs(TestUtils.createStandardUser()) {
            Test.startTest();
            SObjectType sot = Schema.Lead.SObjectType;
            DB.validateCRUD(DB.Action.DESTROY, sot);
            System.assert(true);
            Test.stopTest();
        }
    }

    @IsTest
    static void test_CRUD_undestroy () {

        System.runAs(TestUtils.createStandardUser()) {
            Test.startTest();
            SObjectType sot = Schema.Lead.SObjectType;
            DB.validateCRUD(DB.Action.UNDESTROY, sot);
            System.assert(true);
            Test.stopTest();
        }
    }

    @IsTest
    static void test_FLS_create_success () {

        System.runAs(TestUtils.createStandardUser()) {
            Test.startTest();
            DescribeFieldResult dfr = DB.getFieldToken(DB.getSObjectType(null, 'Lead'), null, 'FirstName').getDescribe();

            DB.validateFLS(DB.Action.CREATE, Lead.SObjectType, dfr);
            System.assert(true);
            Test.stopTest();
        }
    }

    @IsTest
    static void test_FLS_create_fail () {

        System.runAs(TestUtils.createStandardUser()) {
            try {
                Test.startTest();
                DescribeFieldResult dfr = DB.getFieldToken(DB.getSObjectType(null, 'Lead'), null, 'DoNotCall').getDescribe();

                DB.validateFLS(DB.Action.CREATE, Lead.SObjectType, dfr);
                System.assert(false);
            } catch (Exception e) {
                System.assert(e instanceof SecurityException.FLSException);
            } finally {
                Test.stopTest();
            }
        }
    }

    @IsTest
    static void test_FLS_createoredit_fail () {

        System.runAs(TestUtils.createStandardUser()) {
            try {
                Test.startTest();
                DescribeFieldResult dfr = DB.getFieldToken(DB.getSObjectType(null, 'Lead'), null, 'DoNotCall').getDescribe();

                DB.validateFLS(DB.Action.CREATEOREDIT, Lead.SObjectType, dfr);
                System.assert(false);
            } catch (Exception e) {
                System.assert(e instanceof SecurityException.FLSException);
            } finally {
                Test.stopTest();
            }
        }
    }

    @IsTest
    static void test_FLS_createoredit_success () {

        System.runAs(TestUtils.createStandardUser()) {
            Test.startTest();
            Account a = new Account(Name = 'Upsert Test');
            Schema.SObjectField sof = Account.Id.getDescribe().getSobjectField();
            DB.init().createOrEdit(a, sof);
            DB.init(false, false, false).createOrEdit(a, sof);
            System.assert(true);
            Test.stopTest();
        }
    }

    @IsTest
    static void test_FLS_read_success () {

        System.runAs(TestUtils.createStandardUser()) {
            Test.startTest();
            DescribeFieldResult dfr = DB.getFieldToken(DB.getSObjectType(null, 'Lead'), null, 'FirstName').getDescribe();

            DB.validateFLS(DB.Action.READ, Lead.SObjectType, dfr);
            System.assert(true);
            Test.stopTest();
        }
    }

    @IsTest
    static void test_FLS_read_fail () {

        System.runAs(TestUtils.createStandardUser()) {
            try {
                Test.startTest();
                DescribeFieldResult dfr = DB.getFieldToken(DB.getSObjectType(null, 'Lead'), null, 'DoNotCall').getDescribe();

                DB.validateFLS(DB.Action.READ, Lead.SObjectType, dfr);
                System.assert(false);
            } catch (Exception e) {
                System.assert(e instanceof SecurityException.FLSException);
            } finally {
                Test.stopTest();
            }
        }
    }

    @IsTest
    static void test_FLS_edit_success () {

        System.runAs(TestUtils.createStandardUser()) {
            Test.startTest();
            DescribeFieldResult dfr = DB.getFieldToken(DB.getSObjectType(null, 'Lead'), null, 'FirstName').getDescribe();

            DB.validateFLS(DB.Action.EDIT, Lead.SObjectType, dfr);
            System.assert(true);
            Test.stopTest();
        }
    }

    @IsTest
    static void test_FLS_edit_fail () {

        System.runAs(TestUtils.createStandardUser()) {
            try {
                Test.startTest();
                DescribeFieldResult dfr = DB.getFieldToken(DB.getSObjectType(null, 'Lead'), null, 'DoNotCall').getDescribe();

                DB.validateFLS(DB.Action.EDIT, Lead.SObjectType, dfr);
                System.assert(false);
            } catch (Exception e) {
                System.assert(e instanceof SecurityException.FLSException);
            } finally {
                Test.stopTest();
            }
        }
    }

    @IsTest
    static void test_FLS_destroy () {
        // FLS on a destroy call doesn't apply

        System.runAs(TestUtils.createStandardUser()) {
            Test.startTest();
            DescribeFieldResult dfr = DB.getFieldToken(DB.getSObjectType(null, 'Lead'), null, 'DoNotCall').getDescribe();

            DB.validateFLS(DB.Action.DESTROY, Lead.SObjectType, dfr);
            System.assert(true);
            Test.stopTest();
        }
    }

    @IsTest
    static void test_FLS_undestroy () {
        // FLS on a destroy call doesn't apply

        System.runAs(TestUtils.createStandardUser()) {
            Test.startTest();
            DescribeFieldResult dfr = DB.getFieldToken(DB.getSObjectType(null, 'Lead'), null, 'DoNotCall').getDescribe();

            DB.validateFLS(DB.Action.UNDESTROY, Lead.SObjectType, dfr);
            System.assert(true);
            Test.stopTest();
        }
    }

    @IsTest
    static void securityException () {
        SecurityException se = new SecurityException(DB.Action.READ, 'Test {0}', 'Object', 'Field');
        System.assertEquals(DB.Action.READ, se.getAction());
        System.assertEquals('Object', se.getObject());
        System.assertEquals('Field', se.getField());
    }

    @IsTest
    static void getSObjectType () {
        try {
            DB.getSObjectType('absl', 'Account');
            System.assert(true);
        } catch (Exception e) {
            System.assert(false);

        }
        try {
            DB.getSObjectType(null, 'Account');
            System.assert(true);
        } catch (Exception e) {
            System.assert(false);

        }
    }

    @IsTest
    static void getChildSObject () {
        try {
            SObjectType sot = DB.getChildSObject(Account.SObjectType, 'Contacts');
            System.assertEquals(Contact.SObjectType, sot);
        } catch (Exception e) {

        }
        try {
            SObjectType sot = DB.getChildSObject(Account.SObjectType, 'Contacts2'); // not found
            System.assertEquals(null, sot);
        } catch (Exception e) {

        }
    }

    @IsTest
    static void truncateUpsert() {
        Contact c1   = new Contact();
        c1.FirstName = '12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890';
        c1.LastName  = '12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890';
        c1.Email     = '<EMAIL>';
        System.assertEquals(200, c1.FirstName.length());
        System.assertEquals(200, c1.LastName.length());

        Database.DMLOptions opts    = new Database.DMLOptions();
        opts.allowFieldTruncation   = true;

        DB d = DB.init(false, false, false, opts, null);
        d.createOrEdit(c1, Contact.Email);

        Contact c2 = [SELECT Id, FirstName, LastName FROM Contact WHERE Id = :c1.Id];

        System.assertEquals(40, c2.FirstName.length());
        System.assertEquals(80, c2.LastName.length());
    }

    @IsTest
    static void testCaseInsensitiveQueryParam() {
        String soql = 'SELECT Id, Name, FROM Account WHERE RecordTypeId = \'0128Z000000txRcQAI\' AND (Name = :pArAM) AND Description = :Param AND BillingStreet = :paraM';
        DB.Query q = new DB.Query(null, soql);
        q.setParam(':param', 'bla');

        System.Assert.areEqual('SELECT Id, Name, FROM Account WHERE RecordTypeId = \'0128Z000000txRcQAI\' AND (Name = :p0) AND Description = :p0 AND BillingStreet = :p0', q.soqlQuery);
    }
}