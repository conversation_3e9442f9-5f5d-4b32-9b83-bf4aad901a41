/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 11/16/23.
 */

@IsTest
private class LinesEditorControllerTest {
    @IsTest
    static void getTableData() {
        LinesEditorController.getTableData('OpportunityLineItem','flmas__ManageLines','OpportunityId',IdUtilsTest.generateNextId(Opportunity.SObjectType), new List<String>{'Name'});
    }

    @IsTest
    static void getTableDataAddtlFields() {
        LinesEditorController.getTableData('OpportunityLineItem','flmas__ManageLines','OpportunityId',IdUtilsTest.generateNextId(Opportunity.SObjectType), new List<String>{'Name'}, new List<String>());
    }
}