/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 3/24/23.
 */
@IsTest
public class TypeUtilsTest {

    @IsTest
    static void getTypeForClassName() {
        TypeUtils.getTypeForClassName('absl.IdUtils');
    }

    @IsTest
    static void getTypeForObject() {
        System.assertEquals('String',TypeUtils.getTypeForObject('').getName());
        System.assertEquals('Datetime',TypeUtils.getTypeForObject(Datetime.now()).getName());
    }
}