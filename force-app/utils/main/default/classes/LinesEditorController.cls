/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 9/10/23.
 */

@NamespaceAccessible
public virtual with sharing class LinesEditorController {

    /**
     * @description Retrieves table data for the specified object and field set, filtering by a parent record.
     *               Uses helper method for consistent data handling.
     *
     * @param objectName            The name of the object to query.
     * @param fieldSetName          The name of the field set defining which fields to retrieve.
     * @param parentFieldToFilterBy The field on the child records that links to the parent record.
     * @param parentRecordId        The ID of the parent record to filter by.
     * @param parentObjFields       List of fields to query from the parent record.
     *
     * @return                      AA TableDataContainer containing data columns, child records, and parent record details.
     */
    @AuraEnabled
    public static TableDataContainer getTableData(String objectName, String fieldSetName, String parentFieldToFilterBy, String parentRecordId, List<String> parentObjFields) {
        return getTableDataHelper(objectName, fieldSetName, parentFieldToFilterBy, parentRecordId, parentObjFields, new List<String>());
    }

    /**
      * @description Retrieves table data for the specified object and field set with additional query fields.
      *               This method provides an alternative to the base getTableData() by adding a sixth parameter
      *
      * @param objectName            The name of the object to query.
      * @param fieldSetName          The name of the field set defining which fields to retrieve.
      * @param parentFieldToFilterBy The field on the child records that links to the parent record.
      * @param parentRecordId        The ID of the parent record to filter by.
      * @param parentObjFields       List of fields to query from the parent record.
      * @param additionalQueryFields Additional fields to include in the query beyond those defined in the field set.
      *
      * @return                      A TableDataContainer with the queried data and additional fields, ready for display.
      */
    public static TableDataContainer getTableData(String objectName, String fieldSetName, String parentFieldToFilterBy, String parentRecordId, List<String> parentObjFields,  List<String> additionalQueryFields) {
        return getTableDataHelper(objectName, fieldSetName, parentFieldToFilterBy, parentRecordId, parentObjFields, additionalQueryFields);
    }

    /**
     * @description Helper function to retrieve table data for a specified object, field set, and parent record.
     *              Supports additional query fields for flexibility.
     *
     * @param objectName            The name of the object to query.
     * @param fieldSetName          The name of the field set defining which fields to retrieve.
     * @param parentFieldToFilterBy The field on the child records that links to the parent record.
     * @param parentRecordId        The ID of the parent record to filter by.
     * @param parentObjFields       List of fields to query from the parent record.
     * @param additionalQueryFields Additional fields to include in the query beyond those defined in the field set.
     *
     * @return                      A TableDataContainer containing data columns, child records, and parent record details.
     */
    public static TableDataContainer getTableDataHelper(String objectName, String fieldSetName, String parentFieldToFilterBy, String parentRecordId, List<String> parentObjFields, List<String> additionalQueryFields) {
        DescribeSObjectResult primaryDSOR = ((SObject)Type.forName(objectName).newInstance()).getSObjectType().getDescribe();

        List<DatatableColumn> dtColumns = getDatatableColumnsForObject(primaryDSOR, fieldSetName);
        List<SObject> records = getRecordsForObject(primaryDSOR, parentFieldToFilterBy, fieldSetName, parentRecordId, additionalQueryFields);

        // Retrieve parent record details if parentObjFields are specified
        SObject parentObj = null;
        if (!parentObjFields.isEmpty()) {
            parentObj = getParentRecord(parentRecordId, parentObjFields);
        }

        return new TableDataContainer(dtColumns, records, parentObj);
    }

    /**
     * @description Retrieves the list of DatatableColumn objects for the specified object and field set.
     *
     * @param primaryDSOR   The DescribeSObjectResult for the target object.
     * @param fieldSetName  The name of the field set defining which fields to retrieve.
     *
     * @return              List of DatatableColumn objects for the specified field set.
     */
    public static List<DatatableColumn> getDatatableColumnsForObject(DescribeSObjectResult primaryDSOR, String fieldSetName) {
        List<Schema.FieldSetMember> fsmlColumns = FieldSetUtils.createService().getFieldSetMembers(fieldSetName, primaryDSOR);
        return getDatatableColumns(fsmlColumns);
    }

    /**
     * @description Retrieves the child records for the specified object and parent record filter.
     *
     * @param primaryDSOR             The DescribeSObjectResult for the target object.
     * @param parentFieldToFilterBy   The field on the child records that links to the parent record.
     * @param fieldSetName            The name of the field set defining which fields to retrieve.
     * @param parentRecordId          The ID of the parent record to filter by.
     * @param additionalQueryFields   Additional fields to include in the query beyond those defined in the field set.
     *
     * @return                        List of child SObject records matching the filter criteria.
     */
    public static List<SObject> getRecordsForObject(DescribeSObjectResult primaryDSOR, String parentFieldToFilterBy, String fieldSetName, String parentRecordId, List<String> additionalQueryFields) {
        List<Schema.FieldSetMember> fsmlColumns = FieldSetUtils.createService().getFieldSetMembers(fieldSetName, primaryDSOR);

        StringUtils.StringFormatter sf = StringUtils.format('SELECT {fields} FROM {obj} WHERE {parentFieldToFilterBy} = :recordId')
                .set('{fields}',getQueryFields(fsmlColumns, additionalQueryFields))
                .set('{obj}', primaryDSOR.getName())
                .set('{parentFieldToFilterBy}', parentFieldToFilterBy);
        String queryString = sf.toString();

        validateComponentSetup(queryString);

        List<SObject> records = new List<SObject>();
        try {
            records = DB.init()
                    .read(queryString)
                    .setParam('recordId', parentRecordId)
                    .go();

        } catch(Exception e) {
            System.debug(e.getMessage());
            System.debug(e.getLineNumber());
            System.debug(e.getStackTraceString());
            if (Test.isRunningTest()) {
                throw new LinesEditorControllerException(e.getMessage());
            }
            throw new AuraHandledException(e.getMessage());
        }
        return records;
    }

    /**
     * @description Retrieves the parent record details based on specified fields.
     *
     * @param parentRecordId  The ID of the parent record to retrieve.
     * @param parentObjFields List of fields to query from the parent record.
     *
     * @return                The parent SObject with the specified fields, or null if not found.
     */
    private static SObject getParentRecord(String parentRecordId, List<String> parentObjFields) {
        String parentSoql = StringUtils.format('SELECT {flds} FROM {obj} WHERE Id = :parentRecordId')
                .set('{flds}', String.join(parentObjFields, ', '))
                .set('{obj}', ((Id)parentRecordId).getSobjectType())
                .toString();

        List<SObject> parentRecords = Database.query(parentSoql);

        return parentRecords.isEmpty() ? null : parentRecords.get(0);
    }

    @AuraEnabled
    public static void applyUpdates(String objectName, List<String> recordIdsToDelete, List<Map<String,Object>> recordsToUpsert) {
        deleteTableData(recordIdsToDelete);
        upsertTableData(objectName, recordsToUpsert);
    }

    @NamespaceAccessible
    public static void deleteTableData(List<String> recordIds) {
        if (recordIds != null && recordIds.size() > 0) {
            List<SObject> recordsToDelete = new List<SObject>();
            for (Id rid : recordIds) {
                recordsToDelete.add(
                        rid.getSobjectType().newSObject(rid)
                );
            }
            DB.init().destroy(recordsToDelete);
        }
    }

    @NamespaceAccessible
    public static List<SObject> upsertTableData(String objectName, List<Map<String,Object>> records) {
        List<SObject> allRecords = SObjectUtils.getSObjectUtil(objectName).createSObjectsFromMapList(records);
        List<SObject> recordsToUpdate = new List<SObject>();
        List<SObject> recordsToCreate = new List<SObject>();

        for (SObject record : allRecords) {
            if (record.Id == null) {
                recordsToCreate.add(record);
            } else {
                recordsToUpdate.add(record);
            }
        }

        DB.init(false, false, false).edit(recordsToUpdate);
        DB.init(false, false, false).create(recordsToCreate);

        return allRecords;
    }

    private static void validateComponentSetup(String queryString) {
        Boolean isSetupError = false;
        List<String> message = new List<String> {};

        try {
            SOQL.Query qry = SOQL.parse(null, queryString);
            if (Test.isRunningTest()) {
                qry.validate(false, false);
            } else {
                qry.validate(true, true);
            }
        } catch (Exception e) {
            System.debug(e);
            isSetupError = true;
            message.add(e.getMessage());
        }

        if (isSetupError) {
            if (Test.isRunningTest()) {
                throw new LinesEditorControllerException(String.join(message, '\n'));
            }
            throw new AuraHandledException(String.join(message, '\n'));
        }
    }

    @NamespaceAccessible
    public static List<DatatableColumn> getDatatableColumns(List<Schema.FieldSetMember> fsmlColumns) {

        List<DatatableColumn> dcList = new List<DatatableColumn>();

        for(Schema.FieldSetMember fsm : fsmlColumns) {
            DatatableColumn dtc     = new DatatableColumn(fsm);
            TypeAttributes ta       = new TypeAttributes(fsm);
            dtc.typeAttributes      = ta;

            /*
                this blocks adding fields like "LineItemSchedule.Type" where it is
                not editable but it is required, and formula fields
                and from adding quantity and revenue field twice

                note: this used to check a Set<String> that had pre-defined fields.
                Check Git History around v0.33. Or look at Enhanced Product Schedules codebase.
             */
            if (!(dtc.required && !dtc.editable)) {
                dcList.add(dtc);
            }
        }

        return dcList;
    }

    private static String getQueryFields(List<Schema.FieldSetMember> fieldSetMembers, List<String> additionalFields) {
        String queryString = '';
        Set<String> allFields = new Set<String>{'Id'};

        if(fieldSetMembers != null) {
            for(Schema.FieldSetMember fsm : fieldSetMembers) {
                allFields.add(fsm.getFieldPath());

                // add lookup object Name field
                if(!Test.isRunningTest()) {
                    if(fsm.getSObjectField().getDescribe().getReferenceTo().size() > 0) {
                        allFields.add(fsm.getFieldPath().replace('__c', '__r').replace('Id', '') + '.Name');
                    }
                }
            }
        }

        if(additionalFields != null) {
            allFields.addAll(additionalFields);
        }

        if(!allFields.isEmpty()) {
            queryString = String.join(new List<String>(allFields), ', ');
        }
        return queryString;
    }


    @NamespaceAccessible
    public class TableDataContainer {
        @AuraEnabled
        public SObject parentObj;
        @AuraEnabled
        public final List<DatatableColumn> columns;
        @AuraEnabled
        public final List<SObject> records;

        public TableDataContainer(
                List<DatatableColumn> scheduleColumns,
                List<SObject> records,
                SObject parentObj
        ) {
            this.columns    = scheduleColumns;
            this.records    = records;
            this.parentObj  = parentObj;
        }
    }

    @NamespaceAccessible
    public class DatatableColumn {
        @AuraEnabled
        public String label;
        @AuraEnabled
        public String fieldName;
        @AuraEnabled
        public String type;
        @AuraEnabled
        public Boolean createable;
        @AuraEnabled
        public Boolean editable;
        @AuraEnabled
        public String formatter;
        @AuraEnabled
        public TypeAttributes typeAttributes;
        @AuraEnabled
        public Boolean required;
        @AuraEnabled
        public Boolean lookupField;
        @AuraEnabled
        public String lookupRelatedObjectAPIName;
        @AuraEnabled
        public String iconName;

        @NamespaceAccessible
        public DatatableColumn(Schema.FieldSetMember fsm) {
            this(fsm.getSObjectField().getDescribe());

            if(!Test.isRunningTest()) {
                this.fieldName                   = fsm.getFieldPath();
            }
            this.required                    = (fsm.getDbRequired() || fsm.getRequired()) ? true : false;
        }
        @NamespaceAccessible
        public DatatableColumn(DescribeFieldResult fsmDescribe) {
            this.fieldName                   = fsmDescribe.name;
            this.type                        = displayTypesToTypeString.get(fsmDescribe.getType());
            this.label                       = fsmDescribe.getLabel();
            this.createable                  = !Test.isRunningTest() ? (fsmDescribe.isCreateable()) : true;
            this.editable                    = !Test.isRunningTest() ? (fsmDescribe.isUpdateable() && !fsmDescribe.isCalculated()) : true;
            this.lookupField                 = !Test.isRunningTest() ? fsmDescribe.getReferenceTo().size() > 0 : false;
            this.iconName                    = !Test.isRunningTest() && this.lookupField ? LookupController.getIconName(fsmDescribe.getReferenceTo()[0].getDescribe().getName()) : null;
            this.lookupRelatedObjectAPIName  = !Test.isRunningTest() ? (fsmDescribe.getReferenceTo().size() > 0 ? fsmDescribe.getReferenceTo()[0].getDescribe().getName() : null) : null;

            switch on (fsmDescribe.getType()) {
                when CURRENCY {
                    this.formatter = 'currency';
                }
                when PERCENT {
                    this.formatter  = 'percent-fixed';
                }
                when else {
                    this.formatter  = null;
                }
            }
        }

        @NamespaceAccessible
        public DatatableColumn() {}
    }

    @NamespaceAccessible
    public class TypeAttributes {
        @AuraEnabled
        public String fieldAPIName;
        @AuraEnabled
        public String objectAPIName;
        @AuraEnabled
        public List<LWCUtils.SelectOption> picklistValues;
        @AuraEnabled
        public String maxLength;
        @AuraEnabled
        public Double step;

        @NamespaceAccessible
        public TypeAttributes(Schema.FieldSetMember fsm) {
            DescribeFieldResult dfr     = fsm.SObjectField.getDescribe();
            DescribeSObjectResult dsor  = dfr.SObjectType.getDescribe();

            this.objectAPIName          = dsor.getName();
            this.fieldAPIName           = fsm.getFieldPath();
            this.picklistValues         = new List<LWCUtils.SelectOption>();

            if(fsm.getType() === DisplayType.PICKLIST ||
                    fsm.getType() === DisplayType.MULTIPICKLIST ||
                    fsm.getType() === DisplayType.COMBOBOX)
            {
                this.picklistValues = LWCUtils.getPickListValues(dfr);//getPickListValues(this.objectAPIName, this.fieldAPIName);
            }

            //if(!Test.isRunningTest()) {
            Integer ml;
            Schema.DisplayType fieldType = fsm.getType();

            if(fieldType == Schema.DisplayType.CURRENCY ||
                    fieldType == Schema.DisplayType.DOUBLE)
            {
                ml = dfr.getPrecision();
                this.step = Math.pow(10,(-1*dfr.getScale()));

            } else if(fieldType == Schema.DisplayType.INTEGER ||
                    fieldType == Schema.DisplayType.LONG)
            {
                ml = dfr.getDigits();

            } else {
                ml = dfr.getLength();
            }
            this.maxLength = ml != null ? String.valueOf(ml) : null;

            //}
        }
    }


    @NamespaceAccessible
    public class LinesEditorControllerException extends Exception {}
    private static final Boolean quantitySchedulesEnabled;
    private static final Boolean revenueSchedulesEnabled;
    private static final String CAN_USE_REV_SCHEDULE_FIELD  = 'CanUseRevenueSchedule';
    private static final String CAN_USE_QTY_SCHEDULE_FIELD  = 'CanUseQuantitySchedule';
    static {
        Schema.DescribeSObjectResult sotfs  = Product2.SObjectType.getDescribe();
        Map<String, SObjectField> fields    = sotfs.fields.getMap();
        revenueSchedulesEnabled             = fields.get(CAN_USE_REV_SCHEDULE_FIELD)    != null ? true : false;
        quantitySchedulesEnabled            = fields.get(CAN_USE_QTY_SCHEDULE_FIELD)    != null ? true : false;
    }
    private static final Map<DisplayType, String> displayTypesToTypeString = new Map<DisplayType, String> {
            DisplayType.ADDRESS                     => 'text',
            DisplayType.ANYTYPE                     => '',
            DisplayType.BASE64                      => '',
            DisplayType.BOOLEAN                     => 'checkbox',
            DisplayType.COMBOBOX                    => '',
            DisplayType.COMPLEXVALUE                =>'',
            DisplayType.CURRENCY                    => 'number',
            DisplayType.DATACATEGORYGROUPREFERENCE  => '',
            DisplayType.DATE                        => 'date',
            DisplayType.DATETIME                    => 'datetime',
            DisplayType.DOUBLE                      => 'number',
            DisplayType.EMAIL                       => 'text',
            DisplayType.ENCRYPTEDSTRING             => '',
            DisplayType.ID                          => 'text',
            DisplayType.INTEGER                     => 'number',
            DisplayType.JSON                        => '',
            DisplayType.LOCATION                    => '',
            DisplayType.LONG                        => 'number',
            DisplayType.MULTIPICKLIST               => 'multipicklist',
            DisplayType.PERCENT                     => 'number',
            DisplayType.PHONE                       => 'tel',
            DisplayType.PICKLIST                    => 'picklist',
            DisplayType.REFERENCE                   => '',
            DisplayType.SOBJECT                     => '',
            DisplayType.STRING                      => 'text',
            DisplayType.TEXTAREA                    => 'textarea',
            DisplayType.TIME                        => 'time',
            DisplayType.URL                         => 'url'
    };
}