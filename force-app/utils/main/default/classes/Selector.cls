/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * The base Selector class
 *
 * <AUTHOR> LaPorte, Foglight Solutions, Inc.
 * @date 2018-04-16
 */

@NamespaceAccessible
public virtual class Selector { // todo make this generic when those are supported in 50 years

    @NamespaceAccessible
    public enum Comparator {
        EQUALS, NOT_EQUALS, IS_IN, IS_NOT_IN, GREATER_THAN, LESS_THAN, GREATER_THAN_EQUAL, LESS_THAN_EQUAL, IS_LIKE, IS_NOT_LIKE
    }

    // this map intentionally does not contain NOT LIKE, since it's so weird to write, we just have a condition for it
    private Map<Comparator, String> comparatorSignMap = new Map<Comparator, String>{
            Comparator.EQUALS => ' = '
            , Comparator.NOT_EQUALS => ' != '
            , Comparator.IS_IN => ' IN '
            , Comparator.IS_NOT_IN => ' NOT IN '
            , Comparator.GREATER_THAN => ' > '
            , Comparator.LESS_THAN => ' < '
            , Comparator.GREATER_THAN_EQUAL => ' >= '
            , Comparator.LESS_THAN_EQUAL => ' <= '
            , Comparator.IS_LIKE => ' LIKE '
    };

    private final DB dbInstance;
    private final String objectName;
    private final Map<String, Object> filterValues;
    private final List<OrderByContainer> orderBys;
    private final List<String> generatedWhereClauses;

    private Set<String> fieldsToQuery;
    private Integer queryLimit;
    private String queryString;
    private String orderByClause;
    private Boolean useLocking;


    public Selector(String objectName) {
        this(objectName, DB.init());
    }

    public Selector(SObjectType sObjectType) {
        this(String.valueOf(sObjectType));
    }

    public Selector(SObjectType sObjectType, DB dbInstance) {
        this(String.valueOf(sObjectType), dbInstance);
    }

    // initialize with some simple defaults
    @NamespaceAccessible
    public Selector(String objectName, DB dbInstance) {
        this.dbInstance = dbInstance;
        this.objectName = objectName;
        this.filterValues = new Map<String, Object>();
        this.generatedWhereClauses  = new List<String>();
        this.orderBys = new List<OrderByContainer>();

        this.fieldsToQuery = new Set<String>();
        this.queryLimit = -1; // Setting to -1 here, since it makes no sense.  runQuery method will enforce a value that makes sense here thus ensuring queries are limited.
        this.queryString = '';
        this.orderByClause = ' ORDER BY Id ASC';
        this.useLocking = false;
    }

    // These are generic helper methods, super simple

    @NamespaceAccessible
    public Map<Id, SObject> getAllRecordsAllFields() {
        return setLimit(1000).runQuery();
    }

    @NamespaceAccessible
    public SObject getRecordByIdAllFields(Id recordId) {
        /*Map <Id, SObject> returnedRecords = setLimit(1)
                .addFilter('Id', Comparator.EQUALS, recordId)
                .runQuery();

        return returnedRecords.get(recordId);*/
        List<SObject> sos = getRecordsByIdsAllFields(new Set<Id>{recordId});
        if (sos.size() > 0) {
            return sos.get(0);
        }
        return null;
    }

    @NamespaceAccessible
    public List<SObject> getRecordsByIdsAllFields(Set<Id> recordIds) {
        Map <Id, SObject> returnedRecords = setLimit(10000)
                .addFilter('Id', Comparator.IS_IN, recordIds)
                .runQuery();

        return returnedRecords.values();
    }

    // Below are query building and/or internal methods for supporting query building, or for users who just prefer to build queries with these tools
    // Example:  new AccountSelector().setLimit(1).addOrderBy('Name', true, true).setFields(new Set<String>{'Name'}).addFilter('Name', 'Barn%', Selector.Comparator.IS_NOT_LIKE).setUseLocking(false).runQuery());

    @NamespaceAccessible
    public Selector setUseLocking(Boolean useLocking) {
        this.useLocking = useLocking;

        return this;
    }

    @NamespaceAccessible
    public Selector setLimit(Integer queryLimit) {
        if (queryLimit == null || queryLimit <= 0) {
            throw new SelectorException(System.Label.exception_selector_limit_invalid);
        }
        this.queryLimit = queryLimit;

        return this;
    }

    @NamespaceAccessible
    public Selector addOrderBy(String fieldName, Boolean isAscending, Boolean isNullsFirst) {

        if (String.isEmpty(fieldName) || isAscending == null || isNullsFirst == null) {
            throw new SelectorException(System.Label.exception_selector_invalid_order_by);
        }

        OrderByContainer obc = new OrderByContainer(fieldName, isAscending, isNullsFirst);

        this.orderBys.add(obc);

        return this;
    }

    @NamespaceAccessible
    public Selector addOrderBy(SObjectField fieldName, Boolean isAscending, Boolean isNullsFirst) {
        return addOrderBy(String.valueOf(fieldName), isAscending, isNullsFirst);
    }

    // Override default fields
    @NamespaceAccessible
    public Selector setFields(Set<String> fieldsToQuery) {
        if (fieldsToQuery == null) {
            throw new SelectorException(System.Label.exception_selector_null_fields_to_query);
        }
        for (String incomingFieldName : fieldsToQuery) {
            if (String.isEmpty(incomingFieldName)) {
                throw new SelectorException(System.Label.exception_selector_null_fields_to_query);
            }
            this.fieldsToQuery.add(incomingFieldName.toLowerCase());
        }

        return this;
    }

    @NamespaceAccessible
    public Selector addField(String fieldName) {
        if (String.isEmpty(fieldName)) {
            throw new SelectorException(System.Label.exception_selector_null_fields_to_query);
        }
        this.fieldsToQuery.add(fieldName.toLowerCase());

        return this;
    }

    @NamespaceAccessible
    public Selector addField(SObjectField fieldName) {
        if (fieldName == null) {
            throw new SelectorException(System.Label.exception_selector_null_fields_to_query);
        }
        this.fieldsToQuery.add(String.valueOf(fieldName).toLowerCase());

        return this;
    }

    // this option trumps everything, using a querystring here will cause the System to use it and not attempt to limit or build anything
    @NamespaceAccessible
    public Selector setQueryString(String queryString) {
        this.queryString = queryString;

        return this;
    }

    @NamespaceAccessible
    public Selector setFilterValues(Map<String, Object> filterValues) {

        this.filterValues.clear();// Clear existing
        for (String key : filterValues.keySet()) {

            if (!this.queryString.contains(':' + key) && !this.queryString.contains(': ' + key)) {
                throw new SelectorException(StringUtils.format(System.Label.exception_selector_filter_not_found)
                        .set('{keyName}', key)
                        .toString()
                );
            } else {

                this.queryString = this.queryString.replace(':' + key, ':filterValue_' + this.filterValues.size());
                this.queryString = this.queryString.replace(': ' + key, ':filterValue_' + this.filterValues.size());

            }

            //this.filterValues.put(key, filters.get(key));

            addFilter(key, Comparator.IS_IN, filterValues.get(key)); // Comparator doesn't really matter here since we are using their query
        }

        return this;
    }

    // This will set all the filters using AND AND AND AND AND
    // Anything more complicated needs to be done with a custom query
    @NamespaceAccessible
    public Selector addFilter(String fieldName, Comparator comp, Object fieldValue) {
        return addFilter(LogicOperator.OP_AND, fieldName, comp, fieldValue);
    }
    @NamespaceAccessible
    public Selector addFilter(LogicOperator lo, String fieldName, Comparator comp, Object fieldValue) {
        if (this.filterValues.size() >= 10) {
            throw new SelectorException(System.Label.exception_selector_too_many_filters);
        }

        String varName = 'filterValue_' + this.filterValues.size();
        if (comp == Comparator.IS_NOT_LIKE) {
            this.addFilter(lo,'(NOT ' + fieldName + ' LIKE :' + varName + ') ');
        } else {
            this.addFilter(lo,fieldName + comparatorSignMap.get(comp) + ' :' + varName);
        }

        this.filterValues.put(varName, fieldValue);

        return this;
    }

    @NamespaceAccessible
    public Selector addFilter(String condition) {
        return addFilter(LogicOperator.OP_AND, condition);
    }

    @NamespaceAccessible
    public Selector addFilter(LogicOperator lo, String condition) {
        String cond = '';

        if (this.generatedWhereClauses.size() > 0) {
            cond = ' ' + lo?.name().replace('OP_','') + ' ';
        }
        cond += condition;

        this.generatedWhereClauses.add(cond);

        return this;
    }

    // this is the main worker, this is what will query the DB
    // returning a map here since it's easy enough to use the list if needed, but not as simple to convert the other way
    @NamespaceAccessible
    public Map<Id, SObject> runQuery() {
        return new Map<Id, SObject>(validateBuildQuery().go());
    }

    @NamespaceAccessible
    public Database.QueryLocator runQueryLocator() {
        return validateBuildQuery().queryLocator();
    }

    @NamespaceAccessible
    public List<DB.DMLResult> destroy() {
        return this.dbInstance.destroy(this);
    }

    // TODO:  Think about adding a SOSL version here

    private DB.Query validateBuildQuery() {
        // Now we can build our query and run it.
        if (String.isEmpty(this.queryString)) {

            // Let's do some validation up front, make sure our users are not tyring to break stuff
            if (String.isEmpty(this.objectName)) {
                throw new SelectorException(System.Label.exception_selector_no_object_specified);
            }
            /*if (this.queryLimit == null || this.queryLimit < 0) {
                throw new SelectorException(System.Label.exception_selector_no_limit_specified);
            }*/
            // No fields specified?  Let's make it easy to select all.
            if (this.fieldsToQuery.isEmpty()) {
                this.fieldsToQuery = getAllFields();
            }

            this.queryString = buildQuery();
        }

        if (String.isEmpty(this.queryString)) {
            throw new SelectorException(System.Label.exception_selector_querystringissue);
        }

        DB.Query qry = this.dbInstance.read(this.queryString);
        if (this.filterValues.size() > 0) {
            for (String name : this.filterValues.keySet()) {
                qry.setParam(name, this.filterValues.get(name));
            }
        }

        return qry;
    }

    private Set<String> getAllFields() {
        // TODO:  Someday this should use something like describeCache instead of raw describe calls
        Set<String> returnSet = new Set<String>();

        Map<String, Schema.SObjectType> globalDescribeMap = Schema.getGlobalDescribe();
        Schema.SObjectType sot = globalDescribeMap.get(this.objectName);
        if (sot == null) {
            throw new SelectorException(StringUtils.format(System.Label.exception_selector_object_not_found)
                    .set('{objName}',this.objectName)
                    .toString()
            );
        }

        for (Schema.SObjectField sof : sot.getDescribe().fields.getMap().values()) {
            returnSet.add(sof.getDescribe().getName().toLowerCase());
        }

        return returnSet;
    }

    public String buildQuery() {
        String returnQuery = 'SELECT ';

        if (this.fieldsToQuery == null || this.fieldsToQuery.isEmpty()) {
            throw new SelectorException(System.Label.exception_selector_no_fields_specified);
        }

        /*this.fieldsToQuery.remove('id'); // we always query for id above, remove the lowercase version if the user added it

        for (String fieldName : this.fieldsToQuery) {
            returnQuery += ', ' + fieldName;
        }*/
        returnQuery += String.join(new List<String>(this.fieldsToQuery), ', ');
        returnQuery += ' FROM ' + this.objectName;
        returnQuery += ' WHERE Id != \'000000000000000\'';  // this is a valid 15 char id that will never hit anything, but also not cause a tablescan

        if (generatedWhereClauses.size() > 0) {
            returnQuery += ' AND (';
            for (String whereSegment : generatedWhereClauses) {
                returnQuery += whereSegment;
            }
            returnQuery += ')';
        }

        if (this.useLocking == true) {
            returnQuery += buildLimitStatement();
            returnQuery += ' FOR UPDATE ';
        } else {
            // Order by is not allowed if locking is used, so only grab it if we can use it.
            returnQuery += buildOrderByStatement();
            returnQuery += buildLimitStatement();
        }

        return returnQuery;
    }

    private String buildLimitStatement() {
        String returnString = '';
        if (this.queryLimit > 0) {
            returnString = ' LIMIT ' + this.queryLimit;
        }
        return returnString;
    }

    private String buildOrderByStatement() {
        String returnString = '';

        if (!orderBys.isEmpty()) {
            returnString = ' ORDER BY';
            Boolean firstHit = false;
            for (OrderByContainer obc : orderBys) {
                // If we haven't hit our first row, don't add a comma.
                returnString += (firstHit) ? ', ' : '';
                firstHit = true;

                returnString += ' ' + obc.fieldName + ' ' + ((obc.isAscending) ? ' ASC' : ' DESC ') + ' ' + ((obc.isNullsFirst) ? ' NULLS FIRST ' : ' NULLS LAST ');
            }
        }

        return returnString;
    }

    private class SelectorException extends Exception {
    }

    private class OrderByContainer {

        public OrderByContainer(String fieldName, Boolean isAscending, Boolean isNullsFirst) {
            this.fieldName = fieldName;
            this.isAscending = isAscending;
            this.isNullsFirst = isNullsFirst;
        }

        public String fieldName;
        public Boolean isAscending;
        public Boolean isNullsFirst;
    }

    public enum LogicOperator {
        OP_AND, OP_OR
    }
}