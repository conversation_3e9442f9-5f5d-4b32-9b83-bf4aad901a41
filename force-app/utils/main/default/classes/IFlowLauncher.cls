/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * IFlowLauncher
 * @description: 
 * @author: <PERSON><PERSON>
 * @date: 11/7/24
 */

public interface IFlowLauncher {
    void execute(FlowLauncherController.FlowLauncherRequest request);
}