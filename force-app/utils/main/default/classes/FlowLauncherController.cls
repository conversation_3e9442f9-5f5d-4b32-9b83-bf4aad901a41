/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * FlowLauncherController
 * @description: 
 * @author: <PERSON><PERSON>
 * @date: 11/7/24
 */

public with sharing class FlowLauncherController {

    @AuraEnabled
    public static void launchFlow(String payload) {
        Map<String, Object> untypedRequest = (Map<String, Object>)JSON.deserializeUntyped(payload);

        Id recordId = (Id) untypedRequest.get('recordId');
        String handlerClass = (String) untypedRequest.get('handlerClass');
        Map<String, Object> params = (Map<String, Object>) untypedRequest.get('params');

        FlowLauncherRequest request = new FlowLauncherRequest(recordId, handlerClass, params);
        Type t = Type.forName(request.handlerClass);
        IFlowLauncher launcher = (IFlowLauncher)t.newInstance();
        launcher.execute(request);
    }

    public class FlowLauncherRequest {
        public Id recordId;
        public String handlerClass;
        public Map<String, Object> params;

        public FlowLauncherRequest(Id recordId, String handlerClass, Map<String, Object> params) {
            this.recordId = recordId;
            this.handlerClass = handlerClass;
            this.params = params;
        }
    }
}