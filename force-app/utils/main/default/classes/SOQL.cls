/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * An incomplete parser for SOQL
 *
 * This was built to aid with CRUD/FLS, so it naturally only focuses on the
 * SELECT and FROM parts of the query... for now.
 *
 * https://developer.salesforce.com/docs/atlas.en-us.soql_sosl.meta/soql_sosl/sforce_api_calls_soql_select.htm?search_text=COUNT(
 *
 *
 * You: "Whoa, wait, a lexer/parser? Why not just a regex? That's way simpler!"
 * Me: "Yea, but it's also really difficult to get a performant AND accurate regex."
 * Me: "also, a regex is itself... a parser/lexer. This one's static so in theory is faster more tuned for SOQL."
 * You: "cool, but it's more code to maintain."
 * Me: "Yep, that's my job."
 * You: "ok, awesome."
 */
@NamespaceAccessible
public with sharing class SOQL {
    private static String namespace;

    @NamespaceAccessible
    public static Query parse (String ns, String soqlString) {
        return (Query)parseStmt(ns, soqlString);
    }

    @NamespaceAccessible
    public static DMLStmt parseDML (String ns, String soqlString) {
        return (DMLStmt) parseStmt(ns, soqlString);
    }

    @NamespaceAccessible
    public static Stmt parseStmt (String ns, String soqlString) {
        return parse(ns, new ScannerImpl(soqlString));
    }

    @NamespaceAccessible
    public static Stmt parse (String ns, Scanner scanner) {
        namespace = ns;

        Stmt s;
        Token t = scanner.peek();
        switch on (t.type) {
            when TOKEN_SELECT {
                s = new Query(null, null);
            }
            when TOKEN_UPDATE {
                s = new UpdateDML(null, null);
            }
            when TOKEN_DELETE {
                s = new DeleteDML(null, null);
            }
            // Eventually INSERT INTO tbl SELECT ...?
            when else {
                t.throwUnexpectedTokenException();
            }
        }
        s.parse(scanner);

        return s;
    }

    @NamespaceAccessible
    public abstract class Stmt {
        public Token tk { get; private set; }
        public FromStmt parentFromStmt { get; private set; }

        @NamespaceAccessible
        public Stmt (Token t, FromStmt parentFromStmt) {
            this.tk = t;
            this.parentFromStmt = parentFromStmt;
        }

        @NamespaceAccessible
        public abstract void parse (Scanner p);

        @NamespaceAccessible
        public virtual void validate (Boolean crud, Boolean fls) {
            // do nothing by default. The Stmt/Expr should override this if CRUD/FLS rules apply
            // e.g. ORDER BY [fieldName] doesn't need to do a CRUDFLS check
        }

        ParserException getInvalidFieldException () {
            return new ParserException(StringUtils.format('Invalid field: "{fieldName}".')
                    .set('{fieldName}',this.tk.lexeme).toString());
        }
    }

    @NamespaceAccessible
    public abstract class DMLStmt extends Stmt {
        public WhereStmt where_stmt { get; private set; } // this is only used for
        private Boolean hasRelativeCondition;

        @NamespaceAccessible
        public DMLStmt (Token t, FromStmt parentFromStmt) {
            super(t, parentFromStmt);
        }

        @NamespaceAccessible
        public List<DB.DMLResult> execute(SObject record) {
            return execute(new List<SObject>{record});
        }
        @NamespaceAccessible
        public List<DB.DMLResult> execute(List<SObject> records) {
            return execute(records, null);
        }
        @NamespaceAccessible
        public List<DB.DMLResult> execute(List<SObject> records, Map<String,Object> bindMap) {
            return execute(records, bindMap, true, true, true);
        }
        @NamespaceAccessible
        public List<DB.DMLResult> execute(List<SObject> records, Map<String,Object> bindMap, Boolean crud, Boolean fls, Boolean share) {
            List<SObject> qualifiedRecords = new List<SObject>();
            for (SObject so : records) {
                if (execute(so, bindMap)) {
                    qualifiedRecords.add(so);
                }
            }

            if (qualifiedRecords.size() > 0) {
                return DB.init(crud, fls, share).doDML(qualifiedRecords, getAction(), null);
            }
            return new List<DB.DMLResult>();
        }

        @NamespaceAccessible
        public Boolean hasRelativeCondition() {
            if (this.hasRelativeCondition == null) {
                this.hasRelativeCondition = this.where_stmt?.hasRelativeCondition() == true;
            }
            return this.hasRelativeCondition;
        }

        @NamespaceAccessible
        public Integer getRecordCount() {
            return getRecordCount(null);
        }
        @NamespaceAccessible
        public Integer getRecordCount(Map<String,Object> bindMap) {
            StringUtils.StringFormatter sf = StringUtils.format('SELECT COUNT() FROM {object} {WHERE}')
                    .set('{object}', this.getSObjectType());
            if (!hasRelativeCondition() && this.where_stmt != null) {
                sf.set('{WHERE}', this.where_stmt.toSOQL());
            } else {
                sf.set('{WHERE}', '');
            }
            bindMap = bindMap == null ? new Map<String,Object>() : bindMap;
            System.debug(sf.toString());
            return Database.countQueryWithBinds(sf.toString(), bindMap, AccessLevel.SYSTEM_MODE);
        }

        @NamespaceAccessible
        public Database.QueryLocator getQueryLocator() {
            return getQueryLocator(null);
        }
        @NamespaceAccessible
        public Database.QueryLocator getQueryLocator(Map<String,Object> bindMap) {
            bindMap = bindMap == null ? new Map<String,Object>() : bindMap;
            return Database.getQueryLocatorWithBinds(getDataSOQL(), bindMap, AccessLevel.SYSTEM_MODE);
        }

        @NamespaceAccessible
        public List<SObject> getRecords() {
            return getRecords(null);
        }
        @NamespaceAccessible
        public List<SObject> getRecords(Map<String,Object> bindMap) {
            bindMap = bindMap == null ? new Map<String,Object>() : bindMap;
            return Database.queryWithBinds(getDataSOQL(), bindMap, AccessLevel.SYSTEM_MODE);
        }

        public Boolean matches(SObject record) {
            return matches(record, null);
        }
        public Boolean matches(SObject record, Map<String,Object> bindMap) {
            return record.getSObjectType() == this.getSObjectType() && (this.where_stmt == null || this.where_stmt.interpret(record, bindMap));
        }

        public String getDataSOQL() {
            StringUtils.StringFormatter sf = StringUtils.format('SELECT {fields} FROM {object} {WHERE}')
                    .set('{fields}', String.join(getDataFields(), ','))
                    .set('{object}', getSObjectType());
            if (!hasRelativeCondition() && this.where_stmt != null) {
                sf.set('{WHERE}', this.where_stmt.toSOQL());
            } else {
                sf.set('{WHERE}', '');
            }

            String soql = sf.toString();
            System.debug(soql);
            return soql;
        }
        @NamespaceAccessible
        public abstract List<String> getDataFields();
        @NamespaceAccessible
        public abstract Boolean execute(SObject record, Map<String,Object> bindMap);
        @NamespaceAccessible
        public abstract DB.Action getAction();
        @NamespaceAccessible
        public abstract SObjectType getSObjectType();
    }

    public abstract class Expr extends Stmt {
        public Expr (Token t, FromStmt parentFromStmt) {
            super(t, parentFromStmt);
        }
        public abstract SObjectType getSObjectType ();
    }

    /**
     * SELECT fieldList [subquery][...]
     * [TYPEOF typeOfField whenExpression[...] elseExpression END][...]
     * FROM objectType[,...]
     * [USING SCOPE filterScope]
     * [WHERE conditionExpression]
     * [WITH [DATA CATEGORY] filteringExpression]
     * [GROUP BY {fieldGroupByList|ROLLUP (fieldSubtotalGroupByList)|CUBE (fieldSubtotalGroupByList)}
     *      [HAVING havingConditionExpression] ]
     * [ORDER BY fieldOrderByList {ASC|DESC} [NULLS {FIRST|LAST}] ]
     * [LIMIT numberOfRowsToReturn]
     * [OFFSET numberOfRowsToSkip]
     * [FOR {VIEW  | REFERENCE}[,...] ]
     * [ UPDATE {TRACKING|VIEWSTAT}[,...] ]
     *
     */
    @NamespaceAccessible
    public class Query extends Stmt {
        public SelectStmt select_stmt { get; private set; }
        public FromStmt from_stmt { get; private set; }
        public UsingStmt using_stmt { get; private set; }
        /*
        public WhereToken where_token   { get; private set; }
        public WithToken with_token     { get; private set; }
        public GroupToken group_token   { get; private set; }
        public OrderToken order_token   { get; private set; }
        public LimitToken limit_token   { get; private set; }
        public OffsetToken offset_token { get; private set; }
        public ForToken for_token       { get; private set; }
        public UpdateToken update_token { get; private set; }*/

        public Query (Token t, FromStmt parentFromStmt) {
            super(t, parentFromStmt);
        }

        public override void parse (Scanner p) {
            from_stmt = new FromStmt(null, this.parentFromStmt);

            select_stmt = new SelectStmt(p.consume(TokenType.TOKEN_SELECT, 'Must start with SELECT'), from_stmt);
            select_stmt.parse(p);

            from_stmt.tk = p.consume(TokenType.TOKEN_FROM, 'Expect FROM after SELECT');
            from_stmt.parse(p);

            if (p.check(TokenType.TOKEN_USING)) {
                using_stmt = new UsingStmt(p.next(), from_stmt);
                using_stmt.parse(p);
            }

            if (this.parentFromStmt != null) {
                // Sub-query
                Token t = p.next();
                // skip all tokens until end of sub-query. @todo eventually fill this out
                while (t.type != TokenType.RIGHT_PAREN && !p.isAtEnd()) {

                    t = p.next();
                }
                if (t.type != TokenType.RIGHT_PAREN) {
                    t.throwUnexpectedTokenException();
                }
            }
        }

        public override void validate (Boolean crud, Boolean fls) {
            if (from_stmt != null) {
                from_stmt.validate(crud, fls);
                select_stmt.validate(crud, fls);
            }
        }
    }

    public class SelectStmt extends Stmt {
        public final List<FieldExpr> fields { get; private set; }
        public final List<FunctionStmt> functions { get; private set; }
        public final List<TypeOfStmt> typeOfs { get; private set; }
        public final List<Query> subQueries { get; private set; }

        public SelectStmt (Token t, FromStmt parentFromStmt) {
            super(t, parentFromStmt);
            fields = new List<FieldExpr>();
            functions = new List<FunctionStmt>();
            typeOfs = new List<TypeOfStmt>();
            subQueries = new List<Query>();
        }

        public override void parse (Scanner p) {
            while (true) {
                if (p.check(TokenType.IDENTIFIER)) {
                    FieldExpr f = new FieldExpr(p.next(), this.parentFromStmt);
                    f.parse(p);
                    fields.add(f);
                } else if (p.check(TokenType.FUNCTION)) {
                    if (this.parentFromStmt.parentFromStmt != null) {
                        // subquery
                        p.next().throwUnexpectedTokenException();
                    }
                    FunctionStmt f = new FunctionStmt(p.next(), this.parentFromStmt);
                    f.parse(p);
                    functions.add(f);
                } else if (p.check(TokenType.TYPEOF)) {
                    TypeOfStmt t = new TypeOfStmt(p.next(), this.parentFromStmt);
                    t.parse(p);
                    typeOfs.add(t);
                } else if (p.check(TokenType.LEFT_PAREN)) {
                    if (this.parentFromStmt.parentFromStmt != null) {
                        p.next().throwUnexpectedTokenException();
                    }
                    Query s = new Query(p.next(), this.parentFromStmt);
                    s.parse(p);
                    subQueries.add(s);
                }

                if (p.check(TokenType.COMMA)) {
                    p.next();
                    if (p.matches(new List<TokenType>{
                            TokenType.IDENTIFIER,
                            TokenType.FUNCTION,
                            TokenType.TYPEOF,
                            TokenType.LEFT_PAREN
                    })) {
                        continue;
                    } else {
                        p.next().throwUnexpectedTokenException();
                    }
                } else if (p.check(TokenType.TOKEN_FROM)) {
                    break;
                } else {
                    p.next().throwUnexpectedTokenException();
                }
            }
        }

        public override void validate (Boolean crud, Boolean fls) {
            for (FieldExpr token : fields) {
                token.validate(crud, fls);
            }
            for (FunctionStmt token : functions) {
                token.validate(crud, fls);
            }
            for (TypeOfStmt token : typeOfs) {
                token.validate(crud, fls);
            }
            for (Query token : subQueries) {
                token.validate(crud, fls);
            }
        }
    }

    public virtual class FieldExpr extends Expr {
        public final List<FieldPartExpr> parts { get; private set; }
        private FieldPartExpr lastPart;

        public FieldExpr (Token t, FromStmt parentFromStmt) {
            super(null, parentFromStmt);
            this.parts = new List<FieldPartExpr>();
            addPart(t);
        }

        public virtual override void parse (Scanner p) {
            while (p.check(TokenType.DOT)) {
                p.next();
                System.debug(p.peek());
                addPart(p.consume(TokenType.IDENTIFIER, 'Expected an identifier after "."'));
            }
        }

        private void addPart (Token t) {
            this.lastPart = newFieldPart(t, this.parentFromStmt, lastPart);
            this.parts.add(this.lastPart);
        }
        protected virtual FieldPartExpr newFieldPart (Token t, FromStmt fs, FieldPartExpr fpe) {
            return new FieldPartExpr(t, fs, fpe);
        }

        public virtual override void validate (Boolean crud, Boolean fls) {
            if (crud || fls) {
                SObjectType sot = getSObjectType();
                if (crud) {
                    DB.validateCRUD(DB.Action.READ, sot);
                }

                if (fls) {
                    FieldPartExpr fpe = getLastPart();
                    String field = fpe.tk.lexeme;
                    SObjectField sof = DB.getFieldToken(sot, namespace, field);
                    if (sof == null) {
                        throw fpe.getInvalidFieldException();
                    }
                    DescribeFieldResult dfr = sof.getDescribe();

                    DB.validateFLS(DB.Action.READ, sot, dfr);
                }
            }
        }

        public override SObjectType getSObjectType () {
            return this.lastPart.getSObjectType();
        }

        public FieldPartExpr getLastPart () {
            return this.lastPart;
        }

        public Object getValue(SObject rec) {
            for (Integer i=0, j=parts.size()-1; i<=j; i++) {
                FieldPartExpr  fpe = parts.get(i);
                if (i == j) {
                    return rec?.get(fpe.tk.lexeme);
                } else {
                    rec = rec?.getSObject(fpe.tk.lexeme);
                }
            }
            return null;
        }

        public String getFieldPath() {
            List<String> partStrings = new List<String>();
            for (FieldPartExpr fpe : parts) {
                partStrings.add(fpe.tk.lexeme);
            }
            return String.join(partStrings, '.');
        }

        public String toSOQL() {
            return getFieldPath();
        }
    }

    public virtual class FieldPartExpr extends Expr {
        private final FieldPartExpr parentPart;
        private SObjectType sot; // The SObjectType that this field part lives on
        private SObjectType referenceTo; // The SObjectType that this field part refers to. e.g. Contact.Account refers to the Account object

        public FieldPartExpr (Token t, FromStmt parentFromStmt, FieldPartExpr parentPart) {
            super(t, parentFromStmt);
            this.parentPart = parentPart;
        }

        public override void parse (Scanner p) {
        }

        /**
         * Determine what object this FieldPart's token lives on.
         *
         * For example, if the full field expression is "Contact.Account.Name" in the query
         * "SELECT Contact.Account.Name FROM Case", then:
         * - the "Contact" field part lives on the "Case" object
         * - the "Account" field part lives on the "Contact"
         * - the "Name" field part lives on the "Account" object
         *
         * and this method will return the SObjectType for the object the fieldpart is defined on.
         *
         * @return The SObjectType that this field part refers to
         */
        public virtual override SObjectType getSObjectType () {
            if (this.sot == null) {
                if (parentPart != null) {
                    this.sot = this.parentPart.getReferenceTo();
                } else {
                    // else, it's a field on the root
                    this.sot = this.parentFromStmt.froms.get(0).getSObjectType();
                }
            }
            return this.sot;
        }

        protected virtual SObjectType getReferenceTo () {
            if (this.referenceTo == null) {
                SObjectType baseType;
                if (this.parentPart != null) {
                    baseType = this.parentPart.getReferenceTo();
                } else {
                    FromExpr aliasFE = this.parentFromStmt.getFromForAlias(this.tk.lexeme);
                    // determine if this part is an alias to an object
                    if (aliasFE != null) {
                        // The first part references an alias. Get the SOT for the alias
                        return this.referenceTo = aliasFE.getSObjectType();
                    } else {
                        // else, it's a field on the root
                        baseType = this.parentFromStmt.froms.get(0).getSObjectType();
                    }
                }

                SObjectField sof = DB.getFieldToken(baseType, namespace, this.tk.lexeme);
                if (sof == null) {
                    throw getInvalidFieldException();
                }
                DescribeFieldResult dfr = sof.getDescribe();
                if (dfr.getReferenceTo().size() == 1) {
                    this.referenceTo = dfr.getReferenceTo()[0];
                } else {
                    throw new ParserException('Polymorphic relationship joins are not supported. ' + String.valueOf(baseType) + '.' + this.tk.lexeme);
                }
            }
            return this.referenceTo;
        }
    }

    public class FunctionStmt extends Stmt {
        public FieldExpr fieldPart { get; private set; }
        public Token alias { get; private set; }

        public FunctionStmt (Token t, FromStmt parentFromStmt) {
            super(t, parentFromStmt);
        }

        public override void parse (Scanner p) {
            p.consume(TokenType.LEFT_PAREN, 'Expect "(" after function.');

            if (p.check(TokenType.RIGHT_PAREN) && !this.tk.lexeme.equalsIgnoreCase('COUNT')) {
                p.peek().throwUnexpectedTokenException();
            } else if (p.check(TokenType.IDENTIFIER)) {
                fieldPart = new FieldExpr(p.next(), this.parentFromStmt);
                fieldPart.parse(p);
            }

            p.consume(TokenType.RIGHT_PAREN, 'Expect ")" after field expression.');

            if (p.check(TokenType.IDENTIFIER)) {
                alias = p.next();
            }
        }

        public override void validate (Boolean crud, Boolean fls) {
            if (fieldPart != null) {
                fieldPart.validate(crud, fls);
            }
        }

        public String toSOQL() {
            return tk.lexeme + '(' + this.fieldPart.getFieldPath() + ')' + (this.alias != null ? ' ' + this.alias.lexeme : '');
        }
    }

    public class TypeOfStmt extends Stmt {
        public FieldExpr field { get; private set; }
        public List<WhenThenStmt> whenThens { get; private set; }
        public List<FieldExpr> elseFields { get; private set; }

        public TypeOfStmt (Token t, FromStmt parentFromStmt) {
            super(t, parentFromStmt);
            this.whenThens = new List<WhenThenStmt>();
            this.elseFields = new List<FieldExpr>();
        }

        public override void parse (Scanner p) {
            this.field = new FieldExpr(p.consume(TokenType.IDENTIFIER, 'Expect field expression after TYPEOF.'), this.parentFromStmt);
            this.field.parse(p);

            Token t = p.consume(TokenType.TOKEN_WHEN, 'Expect WHEN after TYPEOF field expression');
            WhenThenStmt wts = new WhenThenStmt(t, this.parentFromStmt);
            wts.parse(p);
            whenThens.add(wts);

            while (p.check(TokenType.TOKEN_WHEN)) {
                WhenThenStmt wts2 = new WhenThenStmt(p.next(), this.parentFromStmt);
                wts2.parse(p);
                whenThens.add(wts2);
            }

            if (p.check(TokenType.TOKEN_ELSE)) {
                p.next();
                while (!p.isAtEnd()) {
                    FieldExpr efe = new FieldExpr(p.consume(TokenType.IDENTIFIER, 'Expect field expression after ELSE.'), this.parentFromStmt);
                    efe.parse(p);
                    efe.getLastPart().sot = Name.SObjectType; // Override the SObjectType for else fields. They can only ever be Name
                    elseFields.add(efe);
                    if (!p.check(TokenType.COMMA)) {
                        break;
                    }
                    p.consume(TokenType.COMMA, 'Expected a comma');
                }
            }

            p.consume(TokenType.TOKEN_END, 'Expect END to conclude TYPE OF.');
        }

        public override void validate (Boolean crud, Boolean fls) {
            field.validate(crud, fls);

            for (WhenThenStmt whenThen : whenThens) {
                whenThen.validate(crud, fls);
            }

            if (fls) {
                for (FieldExpr elseField : elseFields) {
                    elseField.validate(crud, fls);
                }
            }
        }
    }

    public class WhenThenStmt extends Stmt {
        public FromStmt whenObject { get; private set; }
        public List<FieldExpr> thenFields { get; private set; }

        public WhenThenStmt (Token t, FromStmt parentFromStmt) {
            super(t, parentFromStmt);
            thenFields = new List<FieldExpr>();
        }

        public override void parse (Scanner p) {
            Token t = p.next();
            if (t.type == TokenType.IDENTIFIER || t.type == TokenType.TOKEN_GROUP) {
                this.whenObject = new FromStmt(null, null);
                this.whenObject.froms.add(new FromExpr(t, this.whenObject));
            } else {
                t.throwUnexpectedTokenException();
            }

            p.consume(TokenType.TOKEN_THEN, 'Expect THEN after WHEN object expression.');

            while (!p.isAtEnd()) {
                FieldExpr tfe = new FieldExpr(p.consume(TokenType.IDENTIFIER, 'Expect field expression after Then.'), this.whenObject);
                tfe.parse(p);
                thenFields.add(tfe);
                if (!p.check(TokenType.COMMA)) {
                    break;
                }
                p.consume(TokenType.COMMA, 'Expected a comma');
            }
        }

        public override void validate (Boolean crud, Boolean fls) {
            whenObject.validate(crud, fls);

            for (FieldExpr field : thenFields) {
                field.validate(crud, fls);
            }
        }
    }

    @NamespaceAccessible
    public virtual class FromStmt extends Stmt {
        public List<FromExpr> froms { get; private set; }
        public Map<String, FromExpr> aliases { get; private set; }

        @NamespaceAccessible
        public FromStmt (Token t, FromStmt parentFromStmt) {
            super(t, parentFromStmt);
            froms = new List<FromExpr>();
            aliases = new Map<String, FromExpr>();
        }

        public virtual override void parse (Scanner p) {
            do {
                FromExpr fe = new FromExpr(p.consume(TokenType.IDENTIFIER, 'Expect from expression.'), this);
                fe.parse(p);
                froms.add(fe);
                if (fe.alias != null) {
                    this.aliases.put(fe.alias.lexeme.toLowerCase(), fe);
                }
                if (!p.check(TokenType.COMMA)) {
                    break;
                }
                p.next();
            } while (!p.isAtEnd());
        }

        public FromExpr getFromForAlias (String alias) {
            return this.aliases.get(alias.toLowerCase());
        }

        public override void validate (Boolean crud, Boolean fls) {
            for (FromExpr fot : froms) {
                fot.validate(crud, fls);
            }
        }

        public Boolean isSubquery () {
            return this.parentFromStmt != null;
        }

        public SObjectType getSObjectType() {
            return this.froms.get(0)?.getSObjectType();
        }
    }

    /**
     * FROM Account
     * FROM Account a
     * FROM Account accountAlias, accountAlias.ParentAccount pa
     * ( ... FROM ChildObjects ...) <-- A sub Query!
     */
    public class FromExpr extends FieldExpr {
        public Token alias { get; private set; }

        public FromExpr (Token t, FromStmt parentFromStmt) {
            super(t, parentFromStmt);
        }

        public override void parse (Scanner p) {
            super.parse(p);
            if (p.check(TokenType.IDENTIFIER)) {
                this.alias = p.next();
            }
        }

        protected override FieldPartExpr newFieldPart (Token t, FromStmt fs, FieldPartExpr fpe) {
            return new FromPartExpr(t, fs, fpe);
        }

        public override void validate (Boolean crud, Boolean fls) {
            if (!getLastPart().tk.lexeme.endsWith('__mdt')) {
                super.validate(crud, false);
            }
        }
    }

    public class FromPartExpr extends FieldPartExpr {
        public FromPartExpr (Token t, FromStmt parentFromStmt, FieldPartExpr parentPart) {
            super(t, parentFromStmt, parentPart);
        }
        public override SObjectType getSObjectType () {
            if (this.sot == null) {
                if (this.parentFromStmt != null && this.parentFromStmt.isSubquery()) {
                    SObjectType baseSot = this.parentFromStmt.parentFromStmt.froms.get(0).getSObjectType();
                    sot = DB.getChildSObject(baseSot, this.tk.lexeme);
                } else if (parentPart != null) {
                    SObjectType baseType = this.parentPart.getSObjectType();

                    SObjectField sof = DB.getFieldToken(baseType, namespace, this.tk.lexeme);
                    if (sof == null) {
                        throw getInvalidFieldException();
                    }
                    DescribeFieldResult dfr = sof.getDescribe();
                    if (dfr.getReferenceTo().size() > 0) {
                        this.sot = dfr.getReferenceTo()[0];
                    }
                } else {
                    FromExpr aliasFE = this.parentFromStmt.getFromForAlias(this.tk.lexeme);
                    // determine if this part is an alias to an object
                    if (aliasFE != null) {
                        // The first part references an alias. Get the SOT for the alias
                        this.sot = aliasFE.getSObjectType();
                    } else {
                        // else, it's an Object
                        this.sot = DB.getSObjectType(namespace, this.tk.lexeme);
                    }
                }
            }
            return this.sot;
        }
    }

    public class UsingStmt extends Stmt {
        public Token scope_token { get; private set; }

        public UsingStmt (Token t, FromStmt parentFromStmt) {
            super(t, parentFromStmt);
        }

        public override void parse (Scanner p) {
            p.consume(TokenType.SCOPE, 'Expect SCOPE after USING.');

            this.scope_token = p.consume(TokenType.USING_SCOPE, 'Expect filter scope after USING SCOPE.');
        }
    }

    /**
     * UPDATE objectType
     * SET setFieldExpression
     * [WHERE conditionExpression]
     *
     * --
     * setFieldExpression: fieldExpression = {fieldExpression|:mergeField|literal}[...]
     * conditionExpression: fieldExpression [logicalOperator fieldExpression2][...]
     *
     * Todo:
     *   ORDER BY & LIMIT statements so that the update can avoid row-locks by ordering by parent fields and can support
     *   PKChunking
     */
    @NamespaceAccessible
    public class UpdateDML extends DMLStmt {
        public UpdateStmt update_stmt { get; private set; }
        public SetStmt set_stmt { get; private set; }
        private String remainingSOQL; // this is only used when the WHERE has a semi- or anti-join subquery
        private Integer where_pos; // todo use this to reference the original WHERE if we determine the WHERE has semi-/anti-joins

        public UpdateDML (Token t, FromStmt parentFromStmt) {
            super(t, parentFromStmt);
        }

        public override void parse(Scanner p) {
            update_stmt = new UpdateStmt(p.consume(TokenType.TOKEN_UPDATE, 'Must start with UPDATE'), null);
            update_stmt.parse(p);

            set_stmt = new SetStmt(p.consume(TokenType.TOKEN_SET, 'Expected SET after UPDATE'), update_stmt);
            set_stmt.parse(p);

            remainingSOQL = '';
            if (!p.check(TokenType.EOF)) {
                where_pos = p.peek().position;
                remainingSOQL = ((ScannerImpl)p).source.substring(where_pos);
            }

            if (p.check(TokenType.TOKEN_WHERE)) {
                where_stmt = new WhereStmt(p.next());
                where_stmt.parse(p);
            }
        }

        public override Boolean execute(SObject record, Map<String,Object> bindMap) {
            //if (record.getSObjectType() == this.update_stmt.update_expr.getSObjectType() && ((this.hasRelativeCondition() && this.where_stmt.interpret(record)) || !this.hasRelativeCondition())) {
            if (matches(record, bindMap)) {
                this.set_stmt.updateSObject(record);
                return true;
            }
            return false;
        }

        public override DB.Action getAction() {
            return DB.Action.EDIT;
        }

        public override SObjectType getSObjectType() {
            return this.update_stmt.update_expr.getSObjectType();
        }

        public override List<String> getDataFields() {
            Set<String> fields = new Set<String>();
            fields.addAll(this.set_stmt.getAllFieldPaths());
            if (this.where_stmt != null) {
                fields.addAll(this.where_stmt.getAllFieldPaths());
            }
            fields.remove(null);
            return new List<String>(fields);
        }
    }
    public class UpdateStmt extends Stmt {
        public UpdateExpr update_expr { get; private set; }

        public UpdateStmt (Token t, FromStmt parentFromStmt) {
            super(t, parentFromStmt);
        }

        public override void parse(Scanner p) {
            update_expr = new UpdateExpr(p.consume(TokenType.IDENTIFIER, 'Expected object identifier.'));
            update_expr.parse(p);
        }
    }
    public class UpdateExpr extends Expr {
        public SObjectType sot { get; private set; }

        public UpdateExpr (Token t) {
            super(t, null);
        }

        public override void parse(Scanner p) {
        }

        public override SObjectType getSObjectType() {
            if (this.sot == null) {
                this.sot = DB.getSObjectType(namespace, this.tk.lexeme);
            }
            return this.sot;
        }
    }
    public class SetStmt extends Stmt {
        public final List<SetFieldExpr> set_fields { get; private set; }
        private final UpdateStmt update_stmt;

        public SetStmt (Token t, UpdateStmt update_stmt) {
            super(t, null);
            this.set_fields = new List<SetFieldExpr>();
            this.update_stmt = update_stmt;
        }

        public override void parse(Scanner p) {
            if (p.check(TokenType.IDENTIFIER)) {
                System.debug(p.peek().toString());
                SetFieldExpr sfe = new SetFieldExpr(null, this.update_stmt);
                sfe.parse(p);
                this.set_fields.add(sfe);

                System.debug(p.peek().toString());
                switch on (p.peek().type) {
                    when COMMA {
                        p.next();
                        if (p.check(TokenType.IDENTIFIER)) {
                            parse(p); // recursive call, supports up to less than 1000 fields
                        } else {
                            p.next().throwUnexpectedTokenException();
                        }
                    }
                    when TOKEN_WHERE, EOF {
                        return;
                    }
                    when else {
                        p.next().throwUnexpectedTokenException();
                    }
                }
            } else {
                p.next().throwUnexpectedTokenException();
            }
        }

        public Set<String> getAllFieldPaths() {
            Set<String> parts = new Set<String>();
            for (SetFieldExpr sfe : this.set_fields) {
                parts.addAll(sfe.getAllFieldPaths());
            }
            return parts;
        }

        public void updateSObject(SObject record) {
            for (SetFieldExpr sfe : this.set_fields) {
                sfe.updateSObject(record);
            }
        }
    }
    public class SetFieldExpr extends Expr {
        private final UpdateStmt update_stmt { get; private set; }
        public FieldPartExpr field;
        public Token operator;
        public Token assignmentValue;
        public FieldExpr assignmentField;

        public SetFieldExpr (Token t, UpdateStmt update_stmt) {
            super(t, null);
            this.update_stmt = update_stmt;
        }

        public override SObjectType getSObjectType() {
            return null;
        }

        public override void parse(Scanner p) {
            // Must start with an identifier or function
            System.debug(p.peek());
            if (p.check(TokenType.IDENTIFIER)) {
                field = new FieldPartExpr(p.next(), this.parentFromStmt, null);
                field.parse(p);
            } else {
                p.next().throwUnexpectedTokenException();
            }

            // must have assignment operator =
            if (p.check(TokenType.EQUAL)) {
                operator    = p.next();
            } else {
                p.next().throwUnexpectedTokenException();
            }

            System.debug(p.peek());
            switch on (p.peek().type) {
                when TOKEN_STRING, TOKEN_NUMBER, TOKEN_DATE, TOKEN_DATETIME, TOKEN_TRUE, TOKEN_FALSE, TOKEN_NULL {
                    assignmentValue = p.next();
                }
                when IDENTIFIER {
                    assignmentField = new FieldExpr(p.next(), this.parentFromStmt);
                    assignmentField.parse(p);
                }
                when else {
                    System.debug(p.peek().type);
                    p.next().throwUnexpectedTokenException();
                }
            }
        }

        public Set<String> getAllFieldPaths() {
            Set<String> parts = new Set<String>();
            parts.add(String.valueOf(field.tk.lexeme));
            parts.add(assignmentField?.getFieldPath());
            return parts;
        }

        public void updateSObject(SObject record) {
            Object value;
            if (this.assignmentValue != null) {
                value = this.assignmentValue.literal;
            } else if (this.assignmentField != null) {
                value = this.assignmentField.getValue(record);
            }
            record.put(this.field.tk.lexeme, value);
        }
    }

    /**
     * DELETE
     * FROM objectType
     * [WHERE conditionExpression]
     *
     * --
     * conditionExpression: fieldExpression [logicalOperator fieldExpression2][...]
     *
     * Todo:
     *   ORDER BY & LIMIT statements so that the update can avoid row-locks by ordering by parent fields and can support
     *   PKChunking
     */
    @NamespaceAccessible
    public class DeleteDML extends DMLStmt {
        public FromStmt from_stmt { get; private set; }
        public WhereStmt where_stmt { get; private set; }

        public DeleteDML (Token t, FromStmt parentFromStmt) {
            super(t, parentFromStmt);
        }

        public override void parse(Scanner p) {
            p.consume(TokenType.TOKEN_DELETE, 'Must start with DELETE');// throw away, the next token is what matters.

            from_stmt = new FromStmt(p.consume(TokenType.TOKEN_FROM, 'Expected FROM after DELETE'), null);
            from_stmt.parse(p);

            if (p.check(TokenType.TOKEN_WHERE)) {
                where_stmt = new WhereStmt(p.next());
                where_stmt.parse(p);
            }
        }

        public override Boolean execute(SObject record, Map<String,Object> bindMap) {
            return matches(record, bindMap);
        }

        public override DB.Action getAction() {
            return DB.Action.DESTROY;
        }

        public override SObjectType getSObjectType() {
            return this.from_stmt.getSObjectType();
        }

        public override List<String> getDataFields() {
            return new List<String>{'Id'};
        }
    }

    @NamespaceAccessible
    public interface BooleanExpression {
        Boolean interpret(SObject record, Map<String,Object> bindMap);

        /**
         * @return a set of all the field path expressions in this where statement
         */
        Set<String> getAllFieldPaths();

        Boolean hasRelativeCondition();

        String toSOQL();
    }

    @NamespaceAccessible
    public class WhereStmt extends Stmt implements BooleanExpression {
        public BooleanExpression root { get; private set; }
        private Token symbol;
        private Scanner lex;

        public WhereStmt (Token t) {
            super(t, null);
        }

        public override void parse (Scanner p) {
            this.lex = p;
            expression();
        }

        public Boolean interpret(SObject record, Map<String,Object> bindMap) {
            Boolean ret = root.interpret(record, bindMap);
            System.debug(LoggingLevel.FINEST, 'WhereStmt.interpret(): ' + ret);
            return ret;
        }

        public Set<String> getAllFieldPaths() {
            return root.getAllFieldPaths();
        }

        public Boolean hasRelativeCondition() {
            return root.hasRelativeCondition();
        }

        public String toSOQL() {
            return 'WHERE ' + root.toSOQL();
        }

        private void expression() {
            term();
            while (symbol.type == TokenType.TOKEN_OR) {
                COr ior = new COr();
                ior.setLeft(root);
                term();
                ior.setRight(root);
                root = ior;
            }
        }
        private void term() {
            factor();
            while (symbol.type == TokenType.TOKEN_AND) {
                CAnd iand = new CAnd();
                iand.setLeft(root);
                factor();
                iand.setRight(root);
                root = iand;
            }
        }
        private void factor() {
            symbol = lex.peek();

            switch on (symbol.type) {
                when IDENTIFIER,FUNCTION {
                    ConditionFieldExpr cfe = new ConditionFieldExpr(null);
                    cfe.parse(lex);
                    root = cfe;
                    symbol = lex.next();
                }
                when TOKEN_NOT {
                    CNot inot = new CNot();
                    symbol = lex.next();
                    factor();
                    inot.setChild(root);
                    root = inot;
                }
                when LEFT_PAREN {
                    symbol = lex.next();
                    expression();
                    symbol = lex.next(); // we don't care about ')'
                }
                when else {
                    symbol.throwUnexpectedTokenException();
                }
            }
        }
    }

    public class ConditionFieldExpr extends Expr implements BooleanExpression {
        public FieldExpr field;
        public FunctionStmt function;
        public Token operator;
        public ComparisonValueExpr comparisonValue;
        public FieldExpr comparisonField;
        public Token comparisonBind;

        public ConditionFieldExpr (Token t) {
            super(t, null);
        }

        public override SObjectType getSObjectType() {
            return null;
        }

        public override void parse(Scanner p) {
            // Must start with an identifier or function
            if (p.check(TokenType.IDENTIFIER)) {
                field = new FieldExpr(p.next(), this.parentFromStmt);
                field.parse(p);
            } else if (p.check(TokenType.FUNCTION)) {
                function = new FunctionStmt(p.next(), this.parentFromStmt);
                function.parse(p);
            } else {
                p.next().throwUnexpectedTokenException();
            }

            // must have a comparison operator =, !=, <, <=, >, >=, NOT, IN, INCLUDES, EXCLUDES
            Token t = p.next();
            switch on (t.type) {
                when BANG_EQUAL, EQUAL, GREATER, GREATER_EQUAL, LESS, LESS_EQUAL, TOKEN_LIKE {
                    operator    = t;
                }
                when INCLUDES, EXCLUDES, TOKEN_IN {
                    operator    = t;
                    // The next token should be (
                    if (p.peek().type != TokenType.LEFT_PAREN) {
                        p.next().throwUnexpectedTokenException();
                    }
                }
                when TOKEN_NOT {
                    // The next token should be IN
                    Token next = p.next();
                    if (next.type == TokenType.TOKEN_IN) {
                        operator = new Token(TokenType.TOKEN_NOT_IN, t.lexeme + ' ' + next.lexeme, null, t.position, t.line);
                    } else {
                        next.throwUnexpectedTokenException();
                    }
                }
                when else {
                    t.throwUnexpectedTokenException();
                }
            }

            switch on (p.peek().type) {
                when TOKEN_STRING, TOKEN_NUMBER, TOKEN_DATE, TOKEN_DATETIME, TOKEN_TRUE, TOKEN_FALSE, TOKEN_NULL, LEFT_PAREN {
                    comparisonValue = new ComparisonValueExpr(p.next());
                    comparisonValue.parse(p);
                }
                when IDENTIFIER {
                    comparisonField = new FieldExpr(p.next(), this.parentFromStmt);
                    comparisonField.parse(p);
                }
                when COLON {
                    p.next();
                    this.comparisonBind = p.consume(TokenType.IDENTIFIER, 'Expected bind variable identifier after :');
                }
                when else {
                    System.debug(p.peek().type);
                    p.next().throwUnexpectedTokenException();
                }
            }
        }

        public Set<String> getAllFieldPaths() {
            Set<String> parts = new Set<String>();
            parts.add(field?.getFieldPath());
            if (comparisonField != null) {
                parts.add(comparisonField.getFieldPath());
            }
            return parts;
        }

        public Boolean interpret(SObject record, Map<String,Object> bindMap) {
            if (function != null) {
                throw new ParserException('functions not supported for interpreter: ' + function.tk.lexeme);
            }

            Object recVal = field?.getValue(record);

            ComparisonValueExpr cve;
            if (comparisonValue != null) {
                cve = this.comparisonValue;
            } else if (comparisonField != null) {
                cve = new ComparisonValueExpr(null);
                cve.val = comparisonField.getValue(record);
            } else if (comparisonBind != null) {
                cve = new ComparisonValueExpr(null);
                cve.val = bindMap?.get(comparisonBind.lexeme);
            } else {
                throw new ParserException('The filter specifies no comparison.');
            }

            return cve.compare(operator.type, recVal);
        }

        public Boolean hasRelativeCondition() {
            return this.comparisonValue == null && this.comparisonField != null;
        }

        public String toSOQL() {
            String ret = '';

            if (this.function != null) {
                ret += this.function.toSOQL();
            } else {
                ret += this.field.toSOQL();
            }

            ret += ' ' + this.operator.lexeme + ' ';

            if (this.comparisonValue != null) {
                ret += this.comparisonValue.toSOQL();
            } else {
                ret += this.comparisonField.toSOQL();
            }

            return ret;
        }
    }

    public class ComparisonValueExpr extends Expr {
        public List<Token> inList; // this could be a String, Number, Boolean, Date, DateTime, or List of Primitives
        public Object val;

        public ComparisonValueExpr (Token t) {
            super(t, null);
        }

        public override SObjectType getSObjectType() {
            return null;
        }

        public override void parse(Scanner p) {
            if (this.tk.type == TokenType.LEFT_PAREN) {
                inList = new List<Token>();

                while (true) {
                    switch on (p.peek().type) {
                        when TOKEN_STRING { // Per SOQL, only Strings are supported here
                            inList.add(p.next());
                        }
                        when COMMA {
                            p.next();
                        }
                        when RIGHT_PAREN {
                            p.next();
                            break;
                        }
                        when else {
                            p.next().throwUnexpectedTokenException();
                        }
                    }
                }
            } else {
                this.val = getValueForToken(this.tk);
            }
        }
        @TestVisible
        private Object getValueForToken(Token t) {
            switch on (t?.type) {
                when TOKEN_STRING, TOKEN_NUMBER, TOKEN_DATE, TOKEN_DATETIME, TOKEN_TRUE, TOKEN_FALSE, TOKEN_NULL {
                    return this.tk.literal;
                }
                when else {
                    this.tk?.throwUnexpectedTokenException();
                }
            }
            return null;
        }

        public Boolean compare(TokenType operator, Object compareValue) {
            switch on (operator) {
                when TOKEN_NOT_IN {
                    return !compareIn(compareValue);
                }
                when TOKEN_IN {
                    return compareIn(compareValue);
                }
                when INCLUDES {
                    throw new ParserException('Unsupported operator: ' + operator.name());
                }
                when EXCLUDES {
                    throw new ParserException('Unsupported operator: ' + operator.name());
                }
                when else {
                    return compare(this.val, operator, compareValue);
                }
            }
        }
        private Boolean compareIn(Object compareValue) {
            for (Token t : inList) {
                if (compare(t.literal, TokenType.EQUAL, compareValue)) {
                    return true;
                }
            }
            return false;
        }
        @TestVisible
        private Boolean compare(Object val, TokenType operator, Object compareValue) {
            System.debug(LoggingLevel.FINEST, val + ' ' + operator.name() + '? ' + compareValue);
            Boolean returnValue;

            switch on operator {
                when EQUAL {
                    returnValue = val == null ? compareValue == null : val.equals(compareValue);
                }
                when BANG_EQUAL {
                    returnValue = val == null ? compareValue != null : !val.equals(compareValue);
                }
                when GREATER {
                    if (val instanceof Decimal) {
                        returnValue = (Decimal)compareValue > (Decimal)val;
                    } else if (val instanceof Date) {
                        returnValue = (Date)compareValue > (Date)val;
                    } else if (val instanceof Datetime) {
                        returnValue = (Datetime)compareValue > (Datetime)val;
                    }
                }
                when GREATER_EQUAL {
                    if (val instanceof Decimal) {
                        returnValue = (Decimal)compareValue >= (Decimal)val;
                    } else if (val instanceof Date) {
                        returnValue = (Date)compareValue >= (Date)val;
                    } else if (val instanceof Datetime) {
                        returnValue = (Datetime)compareValue >= (Datetime)val;
                    }
                }
                when LESS {
                    if (val instanceof Decimal) {
                        returnValue = (Decimal)compareValue < (Decimal)val;
                    } else if (val instanceof Date) {
                        returnValue = (Date)compareValue < (Date)val;
                    } else if (val instanceof Datetime) {
                        returnValue = (Datetime)compareValue < (Datetime)val;
                    }
                }
                when LESS_EQUAL {
                    if (val instanceof Decimal) {
                        returnValue = (Decimal)compareValue <= (Decimal)val;
                    } else if (val instanceof Date) {
                        returnValue = (Date)compareValue <= (Date)val;
                    } else if (val instanceof Datetime) {
                        returnValue = (Datetime)compareValue <= (Datetime)val;
                    }
                }
                when TOKEN_LIKE {
                    String likeExpr     = String.valueOf(val);
                    String compareTo    = String.valueOf(compareValue);

                    if (likeExpr.startsWith('%') && likeExpr.endsWith('%')) {
                        returnValue = compareTo.containsIgnoreCase(likeExpr.removeStart('%').removeEnd('%'));
                    } else if (likeExpr.startsWith('%')) {
                        returnValue = compareTo.endsWithIgnoreCase(likeExpr.removeStart('%'));
                    } else if (likeExpr.endsWith('%')) {
                        returnValue = compareTo.startsWithIgnoreCase(likeExpr.removeEnd('%'));
                    } else {
                        returnValue = likeExpr.equalsIgnoreCase(compareTo);
                    }
                }
            }

            if (returnValue != null) {
                System.debug(LoggingLevel.FINEST, returnValue);
                return returnValue;
            }

            throw new ParserException('non-comparable types. ' + operator.name());
        }

        public String toSOQL() {
            String ret = '';

            if (this.tk.type == TokenType.LEFT_PAREN) {
                List<String> strings = new List<String>();
                for (Token t : this.inList) {
                    strings.add(t.toSOQL());
                }
                ret += '(' + String.join(strings, ',') + ')';
            } else {
                ret += this.tk.toSOQL();
            }

            return ret;
        }
    }

    public abstract class NonTerminal implements BooleanExpression {
        public BooleanExpression left, right;
        public virtual void setLeft(BooleanExpression left) {
            this.left = left;
        }
        public virtual void setRight(BooleanExpression right) {
            this.right = right;
        }

        public Set<String> getAllFieldPaths() {
            Set<String> paths = new Set<String>();
            paths.addAll(left.getAllFieldPaths());
            if (right != null) {
                paths.addAll(right.getAllFieldPaths());
            }
            return paths;
        }

        public Boolean hasRelativeCondition() {
            return left.hasRelativeCondition() || right.hasRelativeCondition();
        }
    }
    public class CAnd extends NonTerminal {
        public Boolean interpret(SObject rec, Map<String,Object> bindMap) {
            return left.interpret(rec, bindMap) && right.interpret(rec, bindMap);
        }

        public String toSOQL() {
            return '(' + left.toSOQL() + ' AND ' + right.toSOQL() + ')';
        }
    }
    public class COr extends NonTerminal {
        public Boolean interpret(SObject rec, Map<String,Object> bindMap) {
            return left.interpret(rec, bindMap) || right.interpret(rec, bindMap);
        }

        public String toSOQL() {
            return '(' + left.toSOQL() + ' OR ' + right.toSOQL() + ')';
        }
    }
    public class CNot extends NonTerminal {
        public void setChild(BooleanExpression child) {
            setLeft(child);
        }

        public override void setRight(BooleanExpression right) {
            throw new UnsupportedOperationException();
        }

        public Boolean interpret(SObject rec, Map<String,Object> bindMap) {
            return !left.interpret(rec, bindMap);
        }

        public String toSOQL() {
            return '(NOT (' + left.toSOQL() + '))';
        }
    }

    public class Token {
        public final TokenType type { get; private set; }
        public final String lexeme { get; private set; }
        public final Object literal { get; private set; }
        public final Integer position { get; private set; }
        public final Integer line { get; private set; }

        public Token (TokenType type, String lexeme, Object literal, Integer position, Integer line) {
            this.type = type;
            this.lexeme = lexeme;
            this.literal = literal;
            this.position = position;
            this.line = line;
        }

        public override String toString () {
            return type + ' ' + lexeme + ' ' + literal + ' @ ' + position;
        }

        public String toSOQL () {
            switch on (type) {
                when TOKEN_STRING {
                    return '\'' + this.literal + '\'';
                }
                when else {
                    return String.valueOf(this.lexeme);
                }
            }
        }

        private void throwUnexpectedTokenException () {
            throw getUnexpectedTokenException(null);
        }

        private ParserException getUnexpectedTokenException (String message) {
            return new ParserException(
                    message == null ?
                            StringUtils.format('Unexpected token "{token}" on line {line} position {position}.')
                                    .set('{token}',this.lexeme)
                                    .set('{line}',String.valueOf(this.line))
                                    .set('{position}',String.valueOf(this.position))
                                    .toString()
                            : message
            );
        }
    }

    public interface Scanner {
        Token peek ();
        Token next ();
        List<Token> scanTokens ();
        Boolean isAtEnd ();
        Boolean matches (List<TokenType> types);
        Token consume (TokenType t, String message);
        Boolean check (TokenType tokenType);
    }
    public class ScannerImpl implements Scanner {

        private final String source;
        //private final List<Token> tokens = new List<Token>();

        // scan state
        private Integer size = 0;
        private Integer endPos = 0;
        private Integer start = 0;
        private Integer line = 1;
        private Integer lastNewLinePos = 0;
        private Token currentToken;
        private Token peekedToken;

        public ScannerImpl (String source) {
            this.source = source;
            size = this.source.length();
            endPos = this.size - 1;

            this.reset();
        }

        public ScannerImpl reset() {
            this.start = 0;
            this.line = 1;
            this.lastNewLinePos = 0;
            this.currentToken = null;
            this.peekedToken = null;
            return this;
        }

        public Token peek () {
            if (peekedToken == null) {
                if (start < size) {
                    peekedToken = scanToken();
                } else {
                    peekedToken = newToken(TokenType.EOF, '', null, size);
                }
                //tokens.add(peekedToken);
            }
            return peekedToken;
        }

        public Token next () {
            if (peekedToken == null) {
                if (start < size) {
                    currentToken = scanToken();
                    //tokens.add(currentToken);
                } else {
                    currentToken = newToken(TokenType.EOF, '', null, size);
                }
            } else {
                currentToken = peekedToken;
                peekedToken = null;
            }
            return currentToken;
        }

        public Boolean isAtEnd () {
            return start >= endPos;
        }

        public Boolean matches (List<TokenType> types) {
            for (TokenType t : types) {
                if (check(t)) {
                    return true;
                }
            }
            return false;
        }

        public Token consume (TokenType t, String message) {
            if (check(t)) return next();

            throw peek().getUnexpectedTokenException(message);
        }

        public Boolean check (TokenType tokenType) {
            return peek().type == tokenType;
        }

        // scan tokens
        public List<Token> scanTokens () {
            List<Token> tokens = new List<Token>();
            while (start < endPos) {
                tokens.add(scanToken());
            }

            tokens.add(newToken(TokenType.EOF, '', null, size));
            return tokens;
        }

        // scan token
        private Token scanToken () {
            Integer position = start;
            Integer startingPos = start;
            List<Integer> tokenParts = new List<Integer>();
            TokenContextType context;
            Boolean inEscape = false;

            for (; position < size; position++) {
                Integer s = this.source.charAt(position);
                //System.debug(s+': ' + String.fromCharArray(new List<Integer>{s}));

                switch on s {
                    when 9, 10, 11, 12, 13, 32 {
                        // Whitespace: horizontal tab, new line, vertical tab, form feed, return, space
                        if (s == 10) {
                            line++;
                            lastNewLinePos = position;
                        }
                        if (context == TokenContextType.lString) {
                            tokenParts.add(s);
                        } else if (tokenParts.size() == 0) {
                            // haven't hit a token yet, ignore whitespace
                            startingPos++;
                        } else {
                            // whitespace after a token indicates end-of-token
                            break;
                        }
                    }
                    when 33, 40, 41, 44, 45, 46, 58, 60, 61, 62 {
                        // !   (   )   ,   -   .   :   <   =   >
                        if (context == TokenContextType.lString) {
                            tokenParts.add(s);
                        } else if (context == TokenContextType.lNumber && s == 46) { // .
                            tokenParts.add(s);
                        } else if (context == TokenContextType.lNumber && s == 45) { // -
                            tokenParts.add(s);
                            context = TokenContextType.lDate;
                        } else if (context == TokenContextType.lDate && s == 45) { // -
                            tokenParts.add(s);
                        } else if (context == TokenContextType.lDate && s == 58) { // :
                            tokenParts.add(s);
                            context = TokenContextType.lDateTime;
                        } else if (context == TokenContextType.lDateTime && s == 58) { // :
                            tokenParts.add(s);
                        } else if (tokenParts.size() == 0) {
                            switch on s {
                                when 33 {
                                    if (this.source.charAt(position + 1) == 61) {
                                        // =
                                        start = position + 2;
                                        return newToken(TokenType.BANG_EQUAL, '!=', null, startingPos);
                                    } else {
                                        throw new ParserException();
                                    }
                                }
                                when 40 {
                                    start = position + 1;
                                    return newToken(TokenType.LEFT_PAREN, '(', null, startingPos);
                                }
                                when 41 {
                                    start = position + 1;
                                    return newToken(TokenType.RIGHT_PAREN, ')', null, startingPos);
                                }
                                when 44 {
                                    start = position + 1;
                                    return newToken(TokenType.COMMA, ',', null, startingPos);
                                }
                                when 46 {
                                    start = position + 1;
                                    return newToken(TokenType.DOT, '.', null, startingPos);
                                }
                                when 58 {
                                    start = position + 1;
                                    return newToken(TokenType.COLON, ':', null, startingPos);
                                }
                                when 60 {
                                    if (this.source.charAt(position + 1) == 61) {
                                        // =
                                        start = position + 2;
                                        return newToken(TokenType.LESS_EQUAL, '<=', null, startingPos);
                                    } else {
                                        start = position + 1;
                                        return newToken(TokenType.LESS, '<', null, startingPos);
                                    }
                                }
                                when 61 {
                                    // FYI, => and =< are not valid SOQL tokens, so they should not be evaluated here.
                                    start = position + 1;
                                    return newToken(TokenType.EQUAL, '=', null, startingPos);
                                }
                                when 62 {
                                    if (this.source.charAt(position + 1) == 61) {
                                        // =
                                        start = position + 2;
                                        return newToken(TokenType.GREATER_EQUAL, '>=', null, startingPos);
                                    } else {
                                        start = position + 1;
                                        return newToken(TokenType.GREATER, '>', null, startingPos);
                                    }
                                }
                            }
                        } else {
                            break;
                        }
                    }
                    when 39 {
                        // '
                        if (tokenParts.size() == 0) {
                            startingPos++;
                            context = TokenContextType.lString;
                        } else if (context == TokenContextType.lString && inEscape) {
                            tokenParts.add(s);
                            inEscape ^= true;
                        } else {
                            position++;
                            break;
                        }
                    }
                    when 48, 49, 50, 51, 52, 53, 54, 55, 56, 57 {
                        // 0-9
                        if (tokenParts.size() == 0 && context == null) {
                            context = TokenContextType.lNumber;
                        }
                        tokenParts.add(s);
                    }
                    when 65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90, // A-Z
                            95, // _
                            97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122 // a-z
                    {
                        if (tokenParts.size() == 0 && context == null) {
                            context = TokenContextType.word;
                        }
                        tokenParts.add(s);
                    }
                    when 92 {
                        // \
                        if (context == TokenContextType.lString) {
                            inEscape ^= true;
                            tokenParts.add(s);
                        } else {
                            throw new ParserException();
                        }
                    }
                    when else {
                        if (context == null) {
                            tokenParts.add(s);
                            context = TokenContextType.symbol;
                            break;
                        } else if (context == TokenContextType.lString) {
                            tokenParts.add(s);
                        } else {
                            break;
                        }
                    }
                }
            }

            String str = String.fromCharArray(tokenParts);
            Token t;

            try {
                switch on (context) {
                    when lString {
                        t = newToken(TokenType.TOKEN_STRING, '\'' + str + '\'', str, startingPos - 1);
                    }
                    when lNumber {
                        Decimal d = Decimal.valueOf(str);
                        t = newToken(TokenType.TOKEN_NUMBER, str, d, startingPos);
                    }
                    when lDate {
                        Date d = DateTimeUtils.parseDate(str);
                        t = newToken(TokenType.TOKEN_DATE, str, d, startingPos);
                    }
                    when lDateTime {
                        Datetime d = DateTimeUtils.parseDatetime(str);
                        t = newToken(TokenType.TOKEN_DATETIME, str, d, startingPos);
                    }
                    when symbol {
                        t = newToken(TokenType.SYMBOL, str, str, startingPos);
                    }
                    when else {
                        throw new System.TypeException();
                    }
                }
            } catch (Exception ste) { // If any of the parses above fail, fallback to an identifier
                TokenType type = KEYWORDS.get(str.toUpperCase());
                if (type == null) type = TokenType.IDENTIFIER;

                Object literal;
                if (type == TokenType.TOKEN_TRUE) {
                    literal = true;
                } else if (type == TokenType.TOKEN_FALSE) {
                    literal = false;
                }
                t = newToken(type, str, literal, startingPos);
            }
            start = position;

            return t;
        }

        private Token newToken (TokenType type, String lexeme, Object literal, Integer position) {
            return new Token(type, lexeme, literal, position - lastNewLinePos, line);
        }
    }

    private enum TokenContextType {
        lString, lNumber, lDate, lDateTime, lEscape, word, symbol
    }
    public enum TokenType {

        // Single-character tokens.
        LEFT_PAREN
        , RIGHT_PAREN
        , COMMA
        , DOT
        , COLON
        , SEMI_COLON

        // Comparison Operators
        , BANG_EQUAL                // !=
        , EQUAL                     // =
        , GREATER, GREATER_EQUAL    // > >=
        , LESS, LESS_EQUAL          // < <=
        , TOKEN_IN                  // IN ('aaa','bbb','ccc')
        , TOKEN_NOT_IN              // NOT IN ('aaa','bbb','ccc')
        , INCLUDES                  // INCLUDES ('aaa;bbb','ccc')
        , EXCLUDES                  // EXCLUDES ('aaa;bbb','ccc')
        , TOKEN_LIKE                // LIKE 'bla%'

        // Logical Operators
        , TOKEN_AND
        , TOKEN_OR
        , TOKEN_NOT

        // Literals
        , TOKEN_STRING
        , TOKEN_NUMBER
        , TOKEN_DATE
        , TOKEN_DATETIME
        , SYMBOL

        // Keywords
        , TOKEN_SELECT
        , FUNCTION
        , TYPEOF
        , TOKEN_WHEN
        , TOKEN_THEN
        , TOKEN_ELSE
        , TOKEN_END
        , TOKEN_FROM
        , TOKEN_AS
        , TOKEN_USING
        , SCOPE
        , USING_SCOPE
        , TOKEN_WHERE
        , IS
        , TOKEN_NULL
        , DATE_LITERAL
        , DATE_LITERAL_N
        , TOKEN_TRUE
        , TOKEN_FALSE
        , WITH
        , DATA
        , CATEGORY
        , AT
        , ABOVE
        , BELOW
        , TOKEN_ABOVE_OR_BELOW
        , TOKEN_GROUP
        , TOKEN_ROLLUP
        , TOKEN_BY
        , TOKEN_HAVING
        , order
        , TOKEN_ASC
        , TOKEN_DESC
        , TOKEN_NULLS
        , first
        , last
        , TOKEN_LIMIT
        , offset
        , TOKEN_FOR
        , view
        , REFERENCE
        , TOKEN_UPDATE
        , TOKEN_SET
        , TOKEN_DELETE

        // Identifiers [A-Za-z0-9_] must start with \w+
        , IDENTIFIER

        // End of File
        , EOF
    }

    private final static Map<String, TokenType> KEYWORDS = new Map<String, TokenType>{
            'SELECT' => TokenType.TOKEN_SELECT

            // FUNCTIONS
            , 'FORMAT' => TokenType.FUNCTION
            , 'TOLABEL' => TokenType.FUNCTION
            , 'CONVERTTIMEZONE' => TokenType.FUNCTION
            , 'CONVERTCURRENCY' => TokenType.FUNCTION
            , 'COUNT' => TokenType.FUNCTION
            , 'COUNT_DISTINCT' => TokenType.FUNCTION
            , 'MIN' => TokenType.FUNCTION
            , 'MAX' => TokenType.FUNCTION
            , 'SUM' => TokenType.FUNCTION
            , 'AVG' => TokenType.FUNCTION
            , 'DISTANCE' => TokenType.FUNCTION
            , 'GEOLOCATION' => TokenType.FUNCTION
            , 'ROLLUP' => TokenType.FUNCTION
            , 'GROUPING' => TokenType.FUNCTION
            , 'CUBE' => TokenType.FUNCTION
            , 'CALENDAR_MONTH' => TokenType.FUNCTION
            , 'CALENDAR_QUARTER' => TokenType.FUNCTION
            , 'CALENDAR_YEAR' => TokenType.FUNCTION
            , 'DAY_IN_MONTH' => TokenType.FUNCTION
            , 'DAY_IN_WEEK' => TokenType.FUNCTION
            , 'DAY_IN_YEAR' => TokenType.FUNCTION
            , 'DAY_ONLY' => TokenType.FUNCTION
            , 'FISCAL_MONTH' => TokenType.FUNCTION
            , 'FISCAL_QUARTER' => TokenType.FUNCTION
            , 'FISCAL_YEAR' => TokenType.FUNCTION
            , 'HOUR_IN_DAY' => TokenType.FUNCTION
            , 'WEEK_IN_MONTH' => TokenType.FUNCTION
            , 'WEEK_IN_YEAR' => TokenType.FUNCTION

            , 'TYPEOF' => TokenType.TYPEOF
            , 'WHEN' => TokenType.TOKEN_WHEN
            , 'THEN' => TokenType.TOKEN_THEN
            , 'ELSE' => TokenType.TOKEN_ELSE
            , 'END' => TokenType.TOKEN_END
            , 'FROM' => TokenType.TOKEN_FROM
            , 'AS' => TokenType.TOKEN_AS
            , 'USING' => TokenType.TOKEN_USING
            , 'SCOPE' => TokenType.SCOPE
            , 'DELEGATED' => TokenType.USING_SCOPE
            , 'EVERYTHING' => TokenType.USING_SCOPE
            , 'MINE' => TokenType.USING_SCOPE
            , 'MY_TERRITORY' => TokenType.USING_SCOPE
            , 'MY_TEAM_TERRITORY' => TokenType.USING_SCOPE
            , 'TEAM' => TokenType.USING_SCOPE
            , 'WHERE' => TokenType.TOKEN_WHERE
            , 'IS' => TokenType.IS
            , 'NULL' => TokenType.TOKEN_NULL
            , 'AND' => TokenType.TOKEN_AND
            , 'OR' => TokenType.TOKEN_OR
            , 'NOT' => TokenType.TOKEN_NOT
            , 'IN' => TokenType.TOKEN_IN
            , 'INCLUDES' => TokenType.INCLUDES
            , 'EXCLUDES' => TokenType.EXCLUDES
            , 'LIKE' => TokenType.TOKEN_LIKE

            // DATE LITERALS
            , 'YESTERDAY' => TokenType.DATE_LITERAL
            , 'TODAY' => TokenType.DATE_LITERAL
            , 'TOMORROW' => TokenType.DATE_LITERAL
            , 'LAST_WEEK' => TokenType.DATE_LITERAL
            , 'THIS_WEEK' => TokenType.DATE_LITERAL
            , 'NEXT_WEEK' => TokenType.DATE_LITERAL
            , 'LAST_MONTH' => TokenType.DATE_LITERAL
            , 'THIS_MONTH' => TokenType.DATE_LITERAL
            , 'NEXT_MONTH' => TokenType.DATE_LITERAL
            , 'LAST_90_DAYS' => TokenType.DATE_LITERAL
            , 'NEXT_90_DAYS' => TokenType.DATE_LITERAL
            , 'LAST_N_DAYS' => TokenType.DATE_LITERAL_N
            , 'NEXT_N_DAYS' => TokenType.DATE_LITERAL_N
            , 'NEXT_N_WEEKS' => TokenType.DATE_LITERAL_N
            , 'LAST_N_WEEKS' => TokenType.DATE_LITERAL_N
            , 'NEXT_N_MONTHS' => TokenType.DATE_LITERAL_N
            , 'LAST_N_MONTHS' => TokenType.DATE_LITERAL_N
            , 'THIS_QUARTER' => TokenType.DATE_LITERAL
            , 'LAST_QUARTER' => TokenType.DATE_LITERAL
            , 'NEXT_QUARTER' => TokenType.DATE_LITERAL
            , 'NEXT_N_QUARTERS' => TokenType.DATE_LITERAL_N
            , 'LAST_N_QUARTERS' => TokenType.DATE_LITERAL_N
            , 'THIS_YEAR' => TokenType.DATE_LITERAL
            , 'LAST_YEAR' => TokenType.DATE_LITERAL
            , 'NEXT_YEAR' => TokenType.DATE_LITERAL
            , 'NEXT_N_YEARS' => TokenType.DATE_LITERAL_N
            , 'LAST_N_YEARS' => TokenType.DATE_LITERAL_N
            , 'THIS_FISCAL_QUARTER' => TokenType.DATE_LITERAL
            , 'LAST_FISCAL_QUARTER' => TokenType.DATE_LITERAL
            , 'NEXT_FISCAL_QUARTER' => TokenType.DATE_LITERAL
            , 'NEXT_N_FISCAL_QUARTERS' => TokenType.DATE_LITERAL_N
            , 'LAST_N_FISCAL_QUARTERS' => TokenType.DATE_LITERAL_N
            , 'THIS_FISCAL_YEAR' => TokenType.DATE_LITERAL
            , 'LAST_FISCAL_YEAR' => TokenType.DATE_LITERAL
            , 'NEXT_FISCAL_YEAR' => TokenType.DATE_LITERAL
            , 'NEXT_N_FISCAL_YEARS' => TokenType.DATE_LITERAL_N
            , 'LAST_N_FISCAL_YEARS' => TokenType.DATE_LITERAL_N

            , 'TRUE' => TokenType.TOKEN_TRUE
            , 'FALSE' => TokenType.TOKEN_FALSE
            , 'WITH' => TokenType.WITH
            , 'DATA' => TokenType.DATA
            , 'CATEGORY' => TokenType.CATEGORY
            , 'AT' => TokenType.AT
            , 'ABOVE' => TokenType.ABOVE
            , 'BELOW' => TokenType.BELOW
            , 'ABOVE_OR_BELOW' => TokenType.TOKEN_ABOVE_OR_BELOW
            , 'GROUP' => TokenType.TOKEN_GROUP
            , 'ROLLUP' => TokenType.TOKEN_ROLLUP
            , 'BY' => TokenType.TOKEN_BY
            , 'HAVING' => TokenType.TOKEN_HAVING
            , 'ORDER' => TokenType.order
            , 'ASC' => TokenType.TOKEN_ASC
            , 'DESC' => TokenType.TOKEN_DESC
            , 'NULLS' => TokenType.TOKEN_NULLS
            , 'FIRST' => TokenType.first
            , 'LAST' => TokenType.last
            , 'LIMIT' => TokenType.TOKEN_LIMIT
            , 'OFFSET' => TokenType.offset
            , 'FOR' => TokenType.TOKEN_FOR
            , 'VIEW' => TokenType.view
            , 'REFERENCE' => TokenType.REFERENCE
            , 'UPDATE' => TokenType.TOKEN_UPDATE
            , 'SET' => TokenType.TOKEN_SET
            , 'DELETE' => TokenType.TOKEN_DELETE
    };
}
