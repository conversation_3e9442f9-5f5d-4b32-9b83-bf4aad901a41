/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 9/7/22.
 */

global with sharing class TypeUtils {

    global static Type getTypeForClassName (String className) {
        System.debug('getTypeForClassName:className = "' + className + '"');
        Type t;
        List<String> parts = className.split('\\.');
        System.debug('getTypeForClassName:parts = ' + parts);
        if (parts.size() > 1) {
            t = Type.forName(parts[0], parts[1]);
            System.debug('getTypeForClassName:t1 = ' + t);
        } else {
            t = Type.forName(null, className);
            System.debug('getTypeForClassName:t2 = ' + t);
            if (t == null) {
                /*t = Type.forName('', className);
                System.debug('getTypeForClassName:t3 = ' + t);
                if (t == null) {
                    t = Type.forName(className);
                    System.debug('getTypeForClassName:t4 = ' + t);
                }*/
            }
        }
        return t;
    }

    public static Type getTypeForObject(Object obj){
        Type result = Datetime.class;
        try {
            Datetime typeCheck = (Datetime)obj;
        } catch (System.TypeException te) {
            System.debug(te);
            System.debug(te.getTypeName());

            String message  = te.getMessage().substringAfter('Invalid conversion from runtime type ');
            String typeName = message.substringBefore(' to Datetime');
            result          = getTypeForClassName(typeName);
        }

        return result;
    }
}