/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * This class exists because Salesforce's `System.assert` methods throw exceptions that cannot be
 * caught and thus immediately halt the transaction, which is fine for Unit Tests but does not
 * work well for use cases where you need to test inputs and throw a catchable exception if the
 * input is invalid.
 */
@NamespaceAccessible
public with sharing class Assert {
    @NamespaceAccessible
    public static void assert (Boolean truth, String message) {
        if (!truth) {
            throw new AssertException(message);
        }
    }
    @NamespaceAccessible
    public static void paramRequired(Object paramVal, String paramName) {
        if (paramVal == null || (paramVal instanceof String && String.isBlank((String)paramVal))) {
            throw new AssertException(
                    StringUtils.format(Label.Exception_ParameterRequired)
                            .set('{paramName}', paramName)
                            .toString()
            );
        }
    }
    @NamespaceAccessible
    public class AssertException extends Exception {
        /*private StringUtils.StringFormatter sf;

        public AssertException() {}
        public AssertException(StringUtils.StringFormatter sf) {
            this.sf = sf;
        }

        public override String toString() {
            if (sf != null) {
                return sf.toString();
            }
            return super.toString();
        }*/
    }
}