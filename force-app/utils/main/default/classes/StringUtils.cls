/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by arbel on 7/7/2022.
 */

@NamespaceAccessible
public inherited sharing class StringUtils {
    @NamespaceAccessible
    public static final String BASE_62_CHARS                = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
    @NamespaceAccessible
    public static final StringUtils.BaseNCoder BASE62_CODER = new StringUtils.BaseNCoder(BASE_62_CHARS);

    @NamespaceAccessible
    public static final Pattern MERGE_FIELD_PATTERN           = Pattern.compile('\\{!([^\\{!}]*)\\}');

    @NamespaceAccessible
    public static Boolean equals(String s1, String s2) {
        s1 = blankValue(s1);
        s2 = blankValue(s2);

        return s1.equals(s2);
    }

    @NamespaceAccessible
    public static Boolean equalsIgnoreCase(String s1, String s2) {
        s1 = blankValue(s1);
        s2 = blankValue(s2);

        return s1.equalsIgnoreCase(s2);
    }

    @NamespaceAccessible
    public static String blankValue(String stringToTest) {
        return blankValue(stringToTest, '');
    }

    @NamespaceAccessible
    public static String blankValue(String stringToTest, String valueToReturn) {
        return String.isBlank(stringToTest) ? valueToReturn : stringToTest;
    }

    @NamespaceAccessible
    public static String capitalizeWords(String input) {
        if (String.isEmpty(input)) {
            return input;
        }

        List<String> parts = new List<String>();
        for (String part : input.split(' ')) {
            parts.add(part.toLowerCase().capitalize());
        }

        return String.join(parts, ' ');
    }

    @NamespaceAccessible
    public static String formatCurrency(Decimal d) {
        return formatDecimal(d, 2);
    }
    @NamespaceAccessible
    public static String formatDecimal(Decimal d, Integer scale) {
        String formatted = d.setScale(scale).format();
        List<String> dParts = formatted.split('\\.');
        if (dParts.size() == 1) {
            dParts.add('');
        }
        dParts[1] = dParts[1].leftPad(scale, '0');
        return dParts[0]+'.'+dParts[1];
    }
    @NamespaceAccessible
    public static String formatDate(Date d) {
        return formatDate(d, 'MM/dd/yyyy');
    }
    @NamespaceAccessible
    public static String formatDate(Date d, String format) {
        return Datetime.newInstance(d, Time.newInstance(0,0,0,0)).format(format);
    }

    @NamespaceAccessible
    public static StringFormatter format (String stringToFormat) {
        return new StringFormatter(stringToFormat);
    }

    @NamespaceAccessible
    public static String base62encode(Long num) {
        return BASE62_CODER.encode(num);
    }

    @NamespaceAccessible
    public static Long base62decode(String encodedString) {
        return BASE62_CODER.decode(encodedString);
    }

    /**
     *
     * @return a UUID
     */
    @NamespaceAccessible
    public static String generateUUID() {
        Blob b = Crypto.generateAesKey(128);
        String h = EncodingUtil.convertToHex(b);
        String guid = h.substring(0,8)+ '-' + h.substring(8,12) + '-' + h.substring(12,16) + '-' + h.substring(16,20) + '-' + h.substring(20);
        return guid;
    }

    /**
     * @param template A string that may have one or more {!MergeField} in it.
     *
     * @return A set of the unique merge fields, returning only the field name, not the full merge syntax.
     */
    public static Set<String> getAllMergeFields(String template) {
        Set<String> setStrField = new Set<String>();

        // Then instantiate a new Matcher object
        Matcher mMatcher = MERGE_FIELD_PATTERN.matcher(template);

        while (mMatcher.find()) {
            // get the fieldname without the {}'s
            String strField = mMatcher.group(1);
            setStrField.add(strField.trim());
        }

        return setStrField;
    }

    public static String getMergedText(String templateWithMergeFields, SObject record) {
        return getMergedText(templateWithMergeFields, getAllMergeFields(templateWithMergeFields), record);
    }
    public static String getMergedText(String templateWithMergeFields, Set<String> mergeFields, SObject record) {
        StringFormatter sf = format(templateWithMergeFields);

        for (String mergeField : mergeFields) {
            sf.set('{!' + mergeField + '}', SObjectUtils.getValueForPath(record, mergeField));
        }

        return sf.toString();
    }

    /**
     * An alternative to String.format():
     * - Developer friendly parameter naming. "The {noun} is {color}" is friendlier than "The {0} is {1}"
     * - Parameters are not order dependent
     * - Supports multi-pass updates.
     *
     * Usage:
     *
     * String s = StringUtil.format('Foo {0} :p0 %1 Bar')
     *                  .set('{0}','My')
     *                  .set(':p0', 'string')
     *                  .set('%1', 'formatter')
     *                  .toString();
     *
     * Yields: 'Foo My string formatter Bar'
     */
    @NamespaceAccessible
    public class StringFormatter {
        private String stringToFormat;

        private StringFormatter (String s) {
            this.stringToFormat = s;
        }

        @NamespaceAccessible
        public StringFormatter set (String paramName, Set<String> paramValue, String separator) {
            if (paramValue == null) {
                paramValue = new Set<String>();
            }
            List<String> paramList = new List<String>(paramValue);

            return set(paramName, paramList, separator);
        }

        @NamespaceAccessible
        public StringFormatter set (String paramName, List<String> paramValue, String separator) {
            if (paramValue == null) {
                paramValue = new List<String>();
            }
            if (separator == null) {
                separator = ', ';
            }
            String sParamValue = String.join((List<String>)paramValue, separator);

            return set(paramName, sParamValue);
        }

        @NamespaceAccessible
        public StringFormatter set (String paramName, Object paramValue) {
            if (String.isEmpty(paramName) || String.isEmpty(stringToFormat)) {
                return this;
            }

            paramName = paramName.trim();

            if (paramValue == null) {
                paramValue = '';
            }

            while (stringToFormat.indexOf(paramName) != -1) {
                stringToFormat = stringToFormat.replace(paramName, String.valueOf(paramValue));
            }

            return this;
        }

        @NamespaceAccessible
        public override String toString () {
            return stringToFormat;
        }
    }

    /**
     * Credit where it's due... The base62encode & decode methods were borrowed and altered from Daniel Peter's
     * PK Chunking article (https://developer.salesforce.com/blogs/developer-relations/2015/11/pk-chunking-techniques-massive-orgs.html)
     */
    @NamespaceAccessible
    public class BaseNCoder {
        private final String[] BASE_DIGITS;
        private final Integer[] BASE_LOOKUP;
        private final Integer BASE;
        private final Integer BITS;

        @NamespaceAccessible
        public BaseNCoder(String baseDigits) {
            BASE_DIGITS = baseDigits.split('');
            BASE        = BASE_DIGITS.size();
            BASE_LOOKUP = new Integer[256];
            BITS        = Math.ceil(Math.log(BASE)/Math.log(2)).intValue(); // determine how many bits are needed for the base. i.e. Base64 = 6 bits, Base62 = 6 bits, Base32 = 5 bits, etc

            for (Integer i=0; i<BASE; i++) {
                Integer chr         = BASE_DIGITS.get(i).charAt(0);
                BASE_LOOKUP[chr]    = i;
            }
        }

        /**
         * Encodes the specified num using the encoders digits
         *
         * @param num the number to encode
         *
         * @return A base-n encoded string
         */
        @NamespaceAccessible
        public String encode(Long num) {
            System.assert(num >= 0, 'expected positive number');
            if (num == 0) {
                return '0';
            }

            List<String> retList = new List<String>();

            while (num != 0) {
                Integer cIndex = (Integer)Math.mod(num, BASE);
                retList.add(BASE_DIGITS[cIndex]);
                num = (num/BASE);
            }

            return String.join(retList, '').reverse();
        }

        @NamespaceAccessible
        public Long decode(String s) {
            s = s.reverse();
            List<Integer> chars = s.getChars();
            Long ret = 0;
            Long mult = 1;
            for (Integer i : chars) {
                Integer index = BASE_LOOKUP[i];
                System.assert(index > -1, 'bad character');
                ret += mult*index;
                mult *= BASE;
            }
            return ret;
        }
    }
}