/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * RecaptchaService
 * @description: 
 * @author: <PERSON><PERSON>
 * @date: 3/4/25
 */

public without sharing class RecaptchaService {

    @AuraEnabled
    public static RecaptchaVerificationResult verifyToken(String token, String siteKey) {
        final String SERVICE_URL = 'https://www.google.com/recaptcha/api/siteverify';
        final String SECRET_KEY = RecaptchaConfig.getConfig(siteKey).SecretKey__c;
        final Decimal SCORE_THRESHOLD = RecaptchaConfig.getConfig(siteKey).ScoreThreshold__c;

        if (String.isBlank(token)) {
            return new RecaptchaVerificationResult(false, 'Missing reCAPTCHA token', null, null);
        }

        HttpRequest request = new HttpRequest();
        request.setEndpoint(SERVICE_URL);
        request.setMethod('POST');
        request.setTimeout(10000);
        request.setHeader('Content-Type', 'application/x-www-form-urlencoded');
        request.setBody('secret=' + EncodingUtil.urlEncode(SECRET_KEY, 'UTF-8') + '&response=' + EncodingUtil.urlEncode(token, 'UTF-8'));

        try {
            Http http = new Http();
            HttpResponse response = http.send(request);

            if (response.getStatusCode() == 200) {
                RecaptchaVerificationResult result = (RecaptchaVerificationResult) JSON.deserialize(response.getBody(), RecaptchaVerificationResult.class);
                if (result.success && (result.score == null || result.score >= SCORE_THRESHOLD)) {
                    return new RecaptchaVerificationResult(true, null, result.score, result.errorCodes);
                }
                return new RecaptchaVerificationResult(false, 'Verification failed: ' + (result.errorCodes != null ? String.join(result.errorCodes, ', ') : 'Low score or unknown error'), result.score, result.errorCodes);
            }
            else {
                return new RecaptchaVerificationResult(false, 'HTTP Error: ' + response.getStatus(), null, null);
            }
        }
        catch (Exception e) {
            return new RecaptchaVerificationResult(false, 'Failed to verify reCAPTCHA token: ' + e.getMessage(), null, null);
        }
    }

    public class RecaptchaVerificationResult {
        @AuraEnabled
        public Boolean success { get; private set; }
        @AuraEnabled
        public String message { get; private set; }
        @AuraEnabled
        public Decimal score { get; private set; }
        @AuraEnabled
        public List<String> errorCodes { get; private set; }

        public RecaptchaVerificationResult(Boolean success, String message, Decimal score, List<String> errorCodes) {
            this.success = success;
            this.message = message;
            this.score = score;
            this.errorCodes = errorCodes;
        }
    }
}