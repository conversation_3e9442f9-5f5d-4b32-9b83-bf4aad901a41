/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 3/23/23.
 */
@NamespaceAccessible
public inherited sharing class OrgInfoUtils {
    @TestVisible
    private static Organization ORG_RECORD;
    private static Boolean HAS_PRODUCT_REV_SCHEDULES;
    private static Boolean HAS_STATE_COUNTRY_PICKLIST;
    @TestVisible
    private static FiscalYearSetting fiscalSetting;

    static {
        String queryString = 'SELECT Id,' +
                ' Name,' +
                ' IsSandbox,' +
                ' FiscalYearStartMonth,' +
                ' UsesStartDateAsFiscalYearName,' +
                ' InstanceName,' +
                ' TimeZoneSidKey' +
                ' FROM Organization' +
                ' LIMIT 1';

        List<Organization> orgResults = (List<Organization>) DB.init(false, false, false).read(queryString).go();
        if (!orgResults.isEmpty()) {
            ORG_RECORD = orgResults.get(0);

            fiscalSetting = new FiscalYearSetting(ORG_RECORD.UsesStartDateAsFiscalYearName, ORG_RECORD.FiscalYearStartMonth);
        }
    }

    @NamespaceAccessible
    public static Boolean isSandbox () {
        return ORG_RECORD.IsSandbox;
    }

    @NamespaceAccessible
    public static Boolean isRevenueSchedulesEnabled() {
        if (HAS_PRODUCT_REV_SCHEDULES == null) {
            HAS_PRODUCT_REV_SCHEDULES = Product2.SObjectType.getDescribe().fields.getMap().containsKey('CanUseRevenueSchedule');
        }
        return HAS_PRODUCT_REV_SCHEDULES;
    }

    @NamespaceAccessible
    public static Boolean isStateAndCountryPicklistsEnabled() {
        if (HAS_STATE_COUNTRY_PICKLIST == null) {
            HAS_STATE_COUNTRY_PICKLIST = Schema.SObjectType.Account.fields.getMap()
                    .containsKey('BillingCountryCode');
        }
        return HAS_STATE_COUNTRY_PICKLIST;
    }

    @NamespaceAccessible
    public static FiscalYearSetting getFiscalYearSetting() {
        return fiscalSetting;
    }

    @NamespaceAccessible
    public static Organization getOrgRecord() {
        return ORG_RECORD;
    }

    public class FiscalYearSetting {
        public final Boolean usesStartDateAsFiscalYearName;
        public final Integer fiscalYearStartMonth;
        public final Boolean usesSandardCalendar;

        public FiscalYearSetting (Boolean usesStartDateAsFiscalYearName, Integer fiscalYearStartMonth) {
            this.usesStartDateAsFiscalYearName  = usesStartDateAsFiscalYearName ?? false;
            this.fiscalYearStartMonth           = fiscalYearStartMonth ?? 1;

            switch on (this.fiscalYearStartMonth) {
                when 1 {
                    this.usesSandardCalendar = true;
                }
                when else {
                    this.usesSandardCalendar = false;
                }
            }
        }
    }
}