/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * RecaptchaConfig
 * @description: 
 * @author: <PERSON><PERSON>
 * @date: 3/4/25
 */

public with sharing class RecaptchaConfig {
    private static Map<String, RecaptchaConfig__mdt> configMap;

    private RecaptchaConfig() {
        // Private constructor to prevent instantiation
    }

    /**
     * Retrieves the RecaptchaConfig__mdt record for the given site key.
     * Caches the result in a static map to ensure singleton behavior.
     *
     * @param siteKey The site key to retrieve the configuration for.
     * @return RecaptchaConfig__mdt custom metadata record.
     */
    public static RecaptchaConfig__mdt getConfig(String siteKey) {
        if (configMap == null) {
            loadConfigs();
        }

        if (configMap.containsKey(siteKey)) {
            return configMap.get(siteKey);
        } else {
            throw new AuraHandledException('No RecaptchaConfig__mdt found for SiteKey: ' + siteKey);
        }
    }

    /**
     * Loads all RecaptchaConfig__mdt records into the static map.
     */
    private static void loadConfigs() {
        configMap = new Map<String, RecaptchaConfig__mdt>();

        for (RecaptchaConfig__mdt config : [SELECT SiteKey__c, SecretKey__c, ScoreThreshold__c FROM RecaptchaConfig__mdt]) {
            if (String.isNotBlank(config.SiteKey__c)) {
                configMap.put(config.SiteKey__c, config);
            }
        }
    }
}