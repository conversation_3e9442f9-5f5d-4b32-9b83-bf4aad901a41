/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */
@NamespaceAccessible
public with sharing class UnitOfWork {

    @TestVisible
    private final Map<DB.Action,Map<SObjectType,ObjectDMLTXs>> eventsByOperation = new Map<DB.Action,Map<SObjectType,ObjectDMLTXs>> {
            DB.Action.CREATE        => new Map<SObjectType,ObjectDMLTXs>(),
            DB.Action.CREATEOREDIT  => new Map<SObjectType,ObjectDMLTXs>(),
            DB.Action.EDIT          => new Map<SObjectType,ObjectDMLTXs>(),
            DB.Action.DESTROY       => new Map<SObjectType,ObjectDMLTXs>(),
            DB.Action.PUBLISH       => new Map<SObjectType,ObjectDMLTXs>()
    };
    private final List<CommitAction> commitSequence = new List<CommitAction>();
    private final Set<CommitAction> commitActions = new Set<CommitAction>();
    private final DB dbInstance; // todo: use mocks here, not with IDML
    private Savepoint dbSavepoint;
    private Boolean isCommitted;
    private final IDML dmlInstance;
    @TestVisible
    private static IDML m_DML; // The mock DML instance used for testing.

    // used to flag triggers to do work
    @NamespaceAccessible
    public static Boolean isCommitActive = false;

    @NamespaceAccessible
    public UnitOfWork() {
        Database.DMLOptions options             = new Database.DMLOptions();
        options.duplicateRuleHeader             = new Database.DMLOptions.DuplicateRuleHeader();
        options.duplicateRuleHeader.allowSave   = true;
        options.optAllOrNone                    = false;
        options.allowFieldTruncation            = true;
        this.dbInstance                         = DB.init(false, false, false, options, null);
        this.isCommitted                        = false;

        if (m_DML != null) {
            this.dmlInstance = m_DML;
        } else {
            this.dmlInstance    = new SimpleDML(this.dbInstance);
        }
    }
    @NamespaceAccessible
    public UnitOfWork(DB dbInstance) {
        this.dbInstance     = dbInstance;
        this.isCommitted    = false;

        if (m_DML != null) {
            this.dmlInstance = m_DML;
        } else {
            this.dmlInstance    = new SimpleDML(this.dbInstance);
        }
    }

    @NamespaceAccessible
    public RecordTx addInsert(SObject record) {
        return addInsert(record, new List<INotify>());
    }
    @NamespaceAccessible
    public RecordTx addInsert(SObject record, INotify relatedEvent) {
        return addInsert(record, new List<INotify>{relatedEvent});
    }
    @NamespaceAccessible
    public RecordTx addInsert(SObject record, List<INotify> relatedEvents) {
        return add(this.eventsByOperation.get(DB.Action.CREATE), record, relatedEvents, null, DB.Action.CREATE);
    }

    @NamespaceAccessible
    public RecordTx addUpdate(SObject record) {
        return addUpdate(record, new List<INotify>());
    }
    @NamespaceAccessible
    public RecordTx addUpdate(SObject record, INotify relatedEvent) {
        return addUpdate(record, new List<INotify>{relatedEvent});
    }
    @NamespaceAccessible
    public RecordTx addUpdate(SObject record, List<INotify> relatedEvents) {
        return add(this.eventsByOperation.get(DB.Action.EDIT), record, relatedEvents, null, DB.Action.EDIT);
    }

    @NamespaceAccessible
    public RecordTx addUpsert(SObject record, Schema.SObjectField externalIdField) {
        return addUpsert(record, new List<INotify>(), externalIdField);
    }
    @NamespaceAccessible
    public RecordTx addUpsert(SObject record, INotify relatedEvent, Schema.SObjectField externalIdField) {
        return addUpsert(record, new List<INotify>{relatedEvent}, externalIdField);
    }
    @NamespaceAccessible
    public RecordTx addUpsert(SObject record, List<INotify> relatedEvents, Schema.SObjectField externalIdField) {
        return add(this.eventsByOperation.get(DB.Action.CREATEOREDIT), record, relatedEvents, externalIdField, DB.Action.CREATEOREDIT);
    }

    @NamespaceAccessible
    public RecordTx addDelete(SObject record) {
        return addDelete(record, new List<INotify>());
    }
    @NamespaceAccessible
    public RecordTx addDelete(SObject record, INotify relatedEvent) {
        return addDelete(record, new List<INotify>{relatedEvent});
    }
    @NamespaceAccessible
    public RecordTx addDelete(SObject record, List<INotify> relatedEvents) {
        return add(this.eventsByOperation.get(DB.Action.DESTROY), record, relatedEvents, null, DB.Action.DESTROY);
    }

    @NamespaceAccessible
    public RecordTx addPublish(SObject record) {
        return addPublish(record, new List<INotify>());
    }
    @NamespaceAccessible
    public RecordTx addPublish(SObject record, INotify relatedEvent) {
        return addPublish(record, new List<INotify>{relatedEvent});
    }
    @NamespaceAccessible
    public RecordTx addPublish(SObject record, List<INotify> relatedEvents) {
        return add(this.eventsByOperation.get(DB.Action.PUBLISH), record, relatedEvents, null, DB.Action.PUBLISH);
    }

    /**
     * Any records added before calling this method will be marked as qualified
     * for commit.
     *
     * Any new INotify instances added to existing records will be qualified for
     * notification.
     */
    @NamespaceAccessible
    public void checkpoint() {
        for (DB.Action op : this.eventsByOperation.keySet()) {
            checkpoint(this.eventsByOperation.get(op));
        }
    }
    private static void checkpoint(Map<SObjectType,ObjectDMLTXs> actionsByType) {
        for (SObjectType sot : actionsByType.keySet()) {
            ObjectDMLTXs dtx = actionsByType.get(sot);
            dtx.checkpoint();
        }
    }

    /**
     * Used only for unit tests
     *
     * @return The number of records that have been included in the most recent
     * checkpoint
     */
    @TestVisible
    private Integer getCheckpointSize() {
        Integer i = 0;
        for (DB.Action op : this.eventsByOperation.keySet()) {
            i += getCheckpointSize(this.eventsByOperation.get(op));
        }
        return i;
    }
    private static Integer getCheckpointSize(Map<SObjectType,ObjectDMLTXs> actionsByType) {
        Integer i = 0;
        for (SObjectType sot : actionsByType.keySet()) {
            ObjectDMLTXs dtx = actionsByType.get(sot);
            i += dtx.getCheckpointSize();
        }
        return i;
    }

    /**
     * Used only for unit tests
     *
     * @return The total number of notifiers, regardless of checkpoint
     */
    @TestVisible
    private Integer getNotifierSize() {
        Integer i = 0;
        for (DB.Action op : this.eventsByOperation.keySet()) {
            i += getNotifierSize(this.eventsByOperation.get(op));
        }
        return i;
    }
    private static Integer getNotifierSize(Map<SObjectType,ObjectDMLTXs> actionsByType) {
        Integer i = 0;
        for (SObjectType sot : actionsByType.keySet()) {
            ObjectDMLTXs dtx = actionsByType.get(sot);
            i += dtx.getNotifierSize();
        }
        return i;
    }

    /**
     * Used only for unit tests
     *
     * @return The total number of notifiers, regardless of checkpoint.
     */
    @TestVisible
    private Integer getRecordSize() {
        Integer i = 0;
        for (DB.Action op : this.eventsByOperation.keySet()) {
            i += getRecordSize(this.eventsByOperation.get(op));
        }
        return i;
    }
    private static Integer getRecordSize(Map<SObjectType,ObjectDMLTXs> actionsByType) {
        Integer i = 0;
        for (SObjectType sot : actionsByType.keySet()) {
            ObjectDMLTXs dtx = actionsByType.get(sot);
            i += dtx.getRecordSize();
        }
        return i;
    }


    /**
     * Removes any INotify instances added since the previous checkpoint.
     * If no INotify instances exist for a record, the record is removed and
     * will not be committed.
     */
    @NamespaceAccessible
    public void reset() {
        for (DB.Action op : this.eventsByOperation.keySet()) {
            reset(this.eventsByOperation.get(op));
        }
    }
    private static void reset(Map<SObjectType,ObjectDMLTXs> actionsByType) {
        for (SObjectType sot : actionsByType.keySet()) {
            ObjectDMLTXs dtx = actionsByType.get(sot);
            dtx.reset();
            if (dtx.externalIdFieldValuesMap.size() == 0) {
                actionsByType.remove(sot);
            }
        }
    }

    private RecordTx add(Map<SObjectType,ObjectDMLTXs> theMap, SObject record, List<INotify> relatedEvents, Schema.SObjectField externalIdField, DB.Action dbAction) {
        if (record == null) {
            throw new UoWException('Record cannot be null.');
        }
        SObjectType sObjectType = record.getSObjectType();

        if (!theMap.containsKey(sObjectType)) {
            theMap.put(sObjectType, new ObjectDMLTXs(sObjectType, dbAction, this));
        }

        if (dbAction == DB.Action.PUBLISH) {
            externalIdField = sObjectType.getDescribe().fields.getMap().get('ReplayId');
        } else if (dbAction != DB.Action.CREATEOREDIT) {
            // assume standard Id
            externalIdField = sObjectType.getDescribe().fields.getMap().get('Id');
        }

        RecordTx t = theMap.get(sObjectType).add(record, relatedEvents, externalIdField);

        return t;//.record;
    }

    public UnitOfWork addCommitAction(DB.Action dbOperation, SObjectType objectType) {
        CommitAction ca  =new CommitAction(dbOperation, objectType);
        if (!this.commitActions.contains(ca)) {
            this.commitSequence.add(ca);
        }
        return this;
    }
    public void commitSequence() {
        for (CommitAction ca : this.commitSequence) {
            System.debug('UOW: Committing Work: ' + ca.dbOperation + ' on ' + ca.objectType);
            this.eventsByOperation.get(ca.dbOperation)?.get(ca.objectType)?.doCommit();
        }
    }

    /**
     * Commits all records that have subscribed INotifys qualified for commit via checkpoint().
     *
     * Any records that have been added but not qualified via checkpoint() will
     * be ignored.
     */
    @NamespaceAccessible
    public void commitWork() {
        commitWork(null);
    }
    @NamespaceAccessible
    public void commitWork(List<SObjectType> sobjectTypeOrder) {
        commitWork(sobjectTypeOrder, new List<DB.Action>{
                DB.Action.CREATE,
                DB.Action.CREATEOREDIT,
                DB.Action.EDIT,
                DB.Action.DESTROY,
                DB.Action.PUBLISH
        });
    }
    @NamespaceAccessible
    public void commitWork(List<SObjectType> sobjectTypeOrder, List<DB.Action> dbOperationOrder) {
        if (!this.isCommitted) {
            System.debug('UOW: Committing Work');
            System.debug('Committing operations in this order: ' + dbOperationOrder);
            System.debug('Committing objects in this order: ' + sobjectTypeOrder);

            for (DB.Action dbOp : dbOperationOrder) {
                doCommit(eventsByOperation.get(dbOp), sobjectTypeOrder);
            }

            this.isCommitted    = true;
        } else {
            throw new UoWException('This unit of work has already been committed.');
        }
    }

    private static void doCommit(Map<SObjectType,ObjectDMLTXs> txMap, List<SObjectType> sobjectTypeOrder) {
        if (sobjectTypeOrder == null || sobjectTypeOrder.size() == 0) {
            sobjectTypeOrder = new List<SObjectType>(txMap.keySet());
        }
        for (SObjectType sot : sobjectTypeOrder) {
            txMap.get(sot)?.doCommit();
        }
    }

    /**
     * Sets a Database Savepoint. This method can be called multiple times
     * but calling rollback() will only rollback to the most recent savepoint.
     */
    @NamespaceAccessible
    public void setSavepoint() {
        this.dbSavepoint                        = Database.setSavepoint();
    }

    /**
     * Be careful calling this if there are multiple instances of UoW active.
     * One UoW's rollback, may cause data created by another UoW to also be
     * rolled-back. Therefore, it's advised that UoW's not be used in hierarchical
     * code and be used serially as much as possible.
     */
    @NamespaceAccessible
    public void rollback() {
        Database.rollback(this.dbSavepoint);
    }

    private static void processDmlResults (List<DB.DMLResult> dmlResults, List<SObject> records, List<RecordTx> txs) {
        for (Integer i=0,j=dmlResults.size(); i<j; i++) {
            /*
             If the transaction was successful, then the Sobject will be updated with an id
             and the caller can do stuff with it.

             If not, loop through all the related TixPayloadEvents and add an error to them
             */
            DB.DMLResult sr = dmlResults.get(i);

            // Get the record
            SObject so = records.get(i);

            // Get the Tx
            RecordTx t = txs.get(i);

            // Set the status
            for (INotify tpe : t.relatedEvents) {
                if (tpe != null) {
                    if (sr.isSuccess()) {
                        tpe.success(so, sr);
                    } else {
                        UoWException xcpt   = new UoWException(sr.getErrors(), so);
                        tpe.fail(xcpt);
                    }
                }
            }
        }
    }

    /**
     * This structure does a few things for us:
     *
     * 1) It allows:
     *      uow.addUpsert(someRecord, somePayload1, ExternalIdField__c);
     *      uow.addUpsert(someRecord, somePayload2, ExternalIdField__c);
     *      uow.addUpsert(someRecord, somePayload3, ExternalIdField__c);
     *
     *   without processing the same "someRecord" 3 times, and instead, registers
     *   each "somePayload" against the single "someRecord" instance.
     *
     * 2) Manage and report errors "more better"
     */
    private class ObjectDMLTXs {
        private final Map<Schema.SObjectField,KeyDMLTXs> externalIdFieldValuesMap = new Map<Schema.SObjectField,KeyDMLTXs>(); // key = The sObjectField used as match during upsert
        private final SObjectType objName;
        private final DB.Action dbAction;
        private final UnitOfWork uow;

        private ObjectDMLTXs (SObjectType objName, DB.Action dbAction, UnitOfWork uow) {
            this.objName    = objName;
            this.dbAction   = dbAction;
            this.uow        = uow;
        }
        private RecordTx add(SObject record, List<INotify> relatedEvents, Schema.SObjectField externalIdField) {
            if(!externalIdFieldValuesMap.containsKey(externalIdField)) {
                this.externalIdFieldValuesMap.put(externalIdField, new KeyDMLTXs(externalIdField, this));
            }
            KeyDMLTXs kUpserts = this.externalIdFieldValuesMap.get(externalIdField);
            return kUpserts.add(record, relatedEvents);
        }

        private void doCommit() {
            UnitOfWork.isCommitActive = true;

            for (Schema.SObjectField keyField : this.externalIdFieldValuesMap.keySet()) {
                this.externalIdFieldValuesMap.get(keyField).doCommit();
            }

            UnitOfWork.isCommitActive = false;
        }

        private void checkpoint() {
            for (KeyDMLTXs t : this.externalIdFieldValuesMap.values()) {
                t.checkpoint();
            }
        }

        private Integer getCheckpointSize() {
            Integer i = 0;
            for (KeyDMLTXs t : this.externalIdFieldValuesMap.values()) {
                i += t.getCheckpointSize();
            }
            return i;
        }

        private Integer getNotifierSize() {
            Integer i = 0;
            for (KeyDMLTXs t : this.externalIdFieldValuesMap.values()) {
                i += t.getNotifierSize();
            }
            return i;
        }

        private Integer getRecordSize() {
            Integer i = 0;
            for (KeyDMLTXs t : this.externalIdFieldValuesMap.values()) {
                i += t.getRecordSize();
            }
            return i;
        }

        private void reset() {
            for (Schema.SObjectField soField : this.externalIdFieldValuesMap.keySet()) {
                KeyDMLTXs t = this.externalIdFieldValuesMap.get(soField);
                t.reset();
                if (t.keyValueTxMap.size() == 0) {
                    this.externalIdFieldValuesMap.remove(soField);
                }
            }
        }
    }

    private class KeyDMLTXs {
        private final Map<Object, RecordTx> keyValueTxMap = new Map<Object, RecordTx>(); //key = the value being matched against
        private final Schema.SObjectField keyField;
        private final ObjectDMLTXs dmlTx;
        private final String keyFieldName;

        private KeyDMLTXs (Schema.SObjectField keyField, ObjectDMLTXs dmlTx) {
            this.keyField       = keyField;
            this.dmlTx          = dmlTx;
            this.keyFieldName   = keyField?.getDescribe()?.name;
        }

        private RecordTx add(SObject record, List<INotify> relatedEvents) {
            if (keyField == null) {
                UoWException uowe =  new UoWException('Matching key cannot be null');
                System.debug(uowe.getStackTraceString());
                throw uowe;
            }

            Object keysValue    = record.get(keyField);
            if (keysValue == null) {
                if (this.dmlTx.dbAction != DB.Action.CREATE && this.dmlTx.dbAction != DB.Action.PUBLISH && !'Id'.equalsIgnoreCase(keyFieldName)) {
                    throw new UoWException('Matching value cannot be null');
                }
                keysValue       = keyValueTxMap.size();
            }

            RecordTx t;
            if (!keyValueTxMap.containsKey(keysValue)) {
                t   = new RecordTx(record, relatedEvents);
                keyValueTxMap.put(keysValue, t);
            } else {
                t   = keyValueTxMap.get(keysValue);
                /*
                 It's tempting to update the record with the latest one, but
                 that causes callers to to manage object references and causes
                 the number of objects to explode. One of the objectives of UoW
                 is to simplify that for callers. So, the first object reference
                 is always kept and returned to the callers. It's their respons-
                 ibility to update that object with new data updates.

                 The other approach would be to update the current object
                 with the data from the new record, but that still doesn't solve
                 the problem of exploding the number of object references
                 created by callers.
                 */
                //  --> t.record   = record;
                t.relatedEvents.addAll(relatedEvents);
            }

            return t;
        }

        private void doCommit() {
            List<SObject> recordsToDml      = new List<SObject>();
            List<RecordTx> txs                    = new List<RecordTx>();

            for (RecordTx t : keyValueTxMap.values()) {
                System.debug('Checkpoint Idx: ' + t.checkpointIdx);
                if (t.checkpointIdx > -1) {
                    // only process records that have been checkpointed
                    recordsToDml.add(t.record);
                    txs.add(t);
                }
            }

            if (recordsToDml.size() > 0) {
                List<DB.DMLResult> dmlResults;

                System.debug('UnitOfWork.doCommit():'+this.dmlTx?.dbAction?.name()+':'+this.dmlTx?.objName+':'+this.keyFieldName);

                dmlResults = this.dmlTx.uow.dmlInstance.doCommit(this.dmlTx.dbAction, recordsToDml, this.keyField);
                System.debug('dmlResults:  ' + dmlResults);

                processDmlResults(dmlResults, recordsToDml, txs);
            }
        }

        private void checkpoint() {
            for (RecordTx t : this.keyValueTxMap.values()) {
                t.checkpoint();
            }
        }

        private Integer getCheckpointSize() {
            Integer i = 0;
            for (RecordTx t : this.keyValueTxMap.values()) {
                if (t.checkpointIdx > -1) {
                    i++;
                }
            }
            return i;
        }

        private Integer getNotifierSize() {
            Integer i = 0;
            for (RecordTx t : this.keyValueTxMap.values()) {
                i += t.relatedEvents.size();
            }
            return i;
        }

        private Integer getRecordSize() {
            return this.keyValueTxMap.size();
        }

        private void reset() {
            for (Object keyValue : this.keyValueTxMap.keySet()) {
                RecordTx t = this.keyValueTxMap.get(keyValue);
                t.reset();
                if (t.relatedEvents.size() == 0) {
                    t.checkpointIdx = -1;
                    this.keyValueTxMap.remove(keyValue);
                }
            }
        }
    }

    @NamespaceAccessible
    public class RecordTx {
        private final List<INotify> relatedEvents;
        public final SObject record;
        private Integer checkpointIdx;

        private RecordTx(SObject record, List<INotify> relatedEvents) {
            this.relatedEvents  = new List<INotify>();
            this.relatedEvents.addAll(relatedEvents);
            this.record         = record;
            this.checkpointIdx  = -1;
        }

        private void checkpoint() {
            this.checkpointIdx = Math.max(this.relatedEvents.size()-1,0);
        }

        private void reset() {
            for (Integer i=this.relatedEvents.size()-1; i > this.checkpointIdx && i > -1; i--) {
                relatedEvents.remove(i);
            }
        }

        @NamespaceAccessible
        public RecordTx addChildRecord(SObject childRecord, SObjectField relationshipField) {
            this.relatedEvents.add(new ChildRecordNotify(childRecord, relationshipField));
            return this;
        }
    }

    @NamespaceAccessible
    public interface INotify {
        void success(SObject so, DB.DMLResult result);
        void fail(UoWException xcpt);
    }

    @NamespaceAccessible
    public class ChildRecordNotify implements INotify {
        private final SObject childRecord;
        private final SObjectField relationshipField;

        @NamespaceAccessible
        public ChildRecordNotify(SObject childRecord, SObjectField relationshipField) {
            this.childRecord        = childRecord;
            this.relationshipField  = relationshipField ;
        }

        @NamespaceAccessible
        public void success(SObject so, DB.DMLResult result) {
            this.childRecord.put(this.relationshipField, so.Id);
            this.childRecord.putSObject(this.relationshipField, null);
        }

        @NamespaceAccessible
        public void fail(UoWException xcpt) {
        }
    }

    /**
     * It's more efficient to declare and implement INotify, but when working
     * with final or 3rd party classes (or other scenarios) a wrapper is needed.
     * Here is a basic one to save time.
     */
    @NamespaceAccessible
    public class INotifyWrapper implements INotify {
        private Boolean wasNotified  = false;
        private Boolean isSuccess    = false;
        private Boolean isFail       = false;
        private UoWException xcpt;
        public Object obj;

        @NamespaceAccessible
        public INotifyWrapper(Object obj) {
            this.obj = obj;
        }

        @NamespaceAccessible
        public void success (SObject so, DB.DMLResult result) {
            this.wasNotified = true;
            this.isSuccess   = true;
        }

        @NamespaceAccessible
        public void fail (UnitOfWork.UoWException xcpt) {
            this.xcpt           = xcpt;
            this.wasNotified    = true;
            this.isFail         = true;
        }

        @NamespaceAccessible
        public Boolean wasNotified() {
            return this.wasNotified;
        }

        @NamespaceAccessible
        public Boolean isSuccess() {
            return this.isSuccess;
        }

        @NamespaceAccessible
        public Boolean isFail() {
            return this.isFail;
        }
    }

    @NamespaceAccessible
    public interface IDML {
        List<DB.DMLResult> doCommit(DB.Action action, List<SObject> records, SObjectField keyField);
        List<DB.DMLResult> doPublish(List<SObject> records);
    }

    @NamespaceAccessible
    public class SimpleDML implements IDML {
        private final DB dbInstance;

        @NamespaceAccessible
        public SimpleDML(DB dbInstance) {
            this.dbInstance = dbInstance;
        }

        @NamespaceAccessible
        public List<DB.DMLResult> doCommit (DB.Action action, List<SObject> recordsToDml, SObjectField keyField) {
            System.debug('doCommit actually called!'+recordsToDml);
            return this.dbInstance.doDML(recordsToDml, action, keyField);
        }

        @NamespaceAccessible
        public List<DB.DMLResult> doPublish (List<SObject> recordsToDml) {
            System.debug('doPublish actually called!'+recordsToDml);
            return this.dbInstance.doDML(recordsToDml, DB.Action.PUBLISH, null);
        }
    }

    @NamespaceAccessible
    public class UoWException extends Exception {
        @NamespaceAccessible
        public final List<Database.Error> errors    = new List<Database.Error>();
        @NamespaceAccessible
        public final SObject sObjectRecord;

        @NamespaceAccessible
        public UoWException(List<Database.Error> errors, SObject sObjectRecord) {
            this.sObjectRecord  = sObjectRecord;

            if (errors != null) {
                this.errors.addAll(errors);
            }

            if (this.errors.size() > 0) {
                String message = 'Database Exception on ' + this.sObjectRecord.getSObjectType() + ' Record: ' + this.sObjectRecord.Id + '\n';
                for (Database.Error e : this.errors) {
                    message += e.getMessage() + '\n\n';
                }
                this.setMessage(message);
            }

            System.debug('UoWException on SObject Record: ' + this.sObjectRecord); // Ironic that this is (or was at one time) line 666
            System.debug(this.getMessage());
            System.debug(this.getStackTraceString());
        }
    }

    private class CommitAction {
        private final DB.Action dbOperation;
        private final SObjectType objectType;

        private CommitAction(DB.Action dbOperation, SObjectType objectType) {
            this.dbOperation = dbOperation;
            this.objectType = objectType;
        }

        public Boolean equals(Object o) {
            if (this === o) return true;
            if (!(o instanceof CommitAction)) return false;

            CommitAction that = (CommitAction) o;

            return System.equals(this.dbOperation, that.dbOperation) && System.equals(this.objectType, that.objectType);
        }

        public override Integer hashCode() {
            return commitActionHashCode(this.dbOperation, this.objectType);
        }
    }

    private static Integer commitActionHashCode(DB.Action dbOperation, SObjectType objectType) {
        return (dbOperation.ordinal() +':'+ String.valueOf(objectType)).hashCode();
    }
}