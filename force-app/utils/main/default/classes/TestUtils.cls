/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */
@IsTest
@NamespaceAccessible
public class TestUtils {
    private static Integer userNumber = 0;

    @NamespaceAccessible
    public static User createStandardUser () {
        return createStandardUser(null);
    }
    @NamespaceAccessible
    public static User createStandardUser (Map<String,Object> overrides) {
        System.assert(true);
        return createUserWithProfile([
                SELECT Id
                FROM Profile
                WHERE Name = 'Standard User'
        ].Id, overrides);
    }

    @NamespaceAccessible
    public static User createPlatformUser () {
        return createPlatformUser(null);
    }
    @NamespaceAccessible
    public static User createPlatformUser (Map<String,Object> overrides) {
        System.assert(true);
        return createUserWithProfile([
                SELECT Id
                FROM Profile
                WHERE Name = 'Standard Platform User'
        ].Id, overrides);
    }

    @NamespaceAccessible
    public static User createUserWithProfile (Id profileId, Map<String,Object> overrides) {
        Integer userNum = userNumber++;
        String email = 'unit.test.user+' + userNum + '@' + UserInfo.getOrganizationId() + '.test.com';
        User u = new User(
                FirstName = 'Test',
                LastName = 'User ',
                Email = email,
                Username = email,
                Alias = ('tuser' + userNum).right(8),
                Phone = '************',
                ProfileId = profileId,
                TimeZoneSidKey = 'America/Los_Angeles',
                LocaleSidKey = 'en_US',
                EmailEncodingKey = 'ISO-8859-1',
                LanguageLocaleKey = 'en_US'
        );
        if (UserInfo.isMultiCurrencyOrganization()) {
            ((SObject) u).put('CurrencyIsoCode', 'USD');
        }

        if (overrides != null) {
            for (String k : overrides.keySet()) {
                u.put(k, overrides.get(k));
            }
        }

        insert u;

        return u;
    }

    /**
     * Example showing how to create a user with a default assignment of some permission
     * sets.
     *
     * @return A user
     */
    @NamespaceAccessible
    public static User createUserWithPerms () {
        System.assert(true);
        return createUser(new List<String> {
                'permission_set_name_1', 'permission_set_name_2'
        });
    }


    @NamespaceAccessible
    public static User createUser (List<String> permissionSets) {
        User u = createStandardUser();

        // add user to perm sets
        List<PermissionSetAssignment> psas = new List<PermissionSetAssignment>();
        for (PermissionSet ps : [
                SELECT Id,
                        Name
                FROM PermissionSet
                WHERE Name IN :permissionSets
        ]) {
            psas.add(
                    new PermissionSetAssignment(
                            AssigneeId = u.Id,
                            PermissionSetId = ps.Id
                    )
            );
        }
        insert psas;

        return u;
    }

    @IsTest
    static void assertTest() {
        try {
            Assert.assert(false, 'Test');
            System.assert(false, 'The custom assert class should have thrown an exception but didnt.');
        } catch (Assert.AssertException ae) {
            System.assertEquals('Test',ae.getMessage());
        }
    }

    /**
     * Useful for mocking SObject instances with parent-child data
     * @credit https://salesforce.stackexchange.com/questions/172925/reading-and-writing-child-relationships-of-not-yet-persisted-sobjects
     */
    public class ParentProxy {
        private final Map<String,ChildListProxy> children;
        private final Type t;

        public ParentProxy(Type apexType) {
            t = apexType;
            children = new Map<String,ChildListProxy>();
        }

        public void addChildList(String childRefName, List<SObject> records) {
            children.put(childRefName, new ChildListProxy(records));
        }

        public SObject generateParent() {
            return (SObject)JSON.deserialize(JSON.serialize(children), t);
        }

        public SObject generateParent(Map<String,Object> parentFields) {
            SObject so = generateParent();
            for(String field : parentFields.keySet()) {
                so.put(field, parentFields.get(field));
            }
            return so;
        }
    }

    /**
     * A simple class that mocks the JSON structure of child SObject records
     */
    public class ChildListProxy {
        private final List<SObject> records;
        private Integer totalSize { get { return records.size(); } }
        private final Boolean done;

        public ChildListProxy(List<SObject> children) {
            this.records        = children;
            this.done           = true;
        }
    }
}