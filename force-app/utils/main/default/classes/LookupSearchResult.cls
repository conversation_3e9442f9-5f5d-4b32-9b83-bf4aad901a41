/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Class used to serialize a single Lookup search result item
 * The Lookup controller returns a List<LookupSearchResult> when sending search result back to Lightning
 */
public inherited sharing class LookupSearchResult {
    private final Id id;
    private final String sObjectType;
    private final String icon;
    private final String title;
    private final String subtitle;
    private final SObject record;

    @TestVisible
    private LookupSearchResult () {

    }

    public LookupSearchResult (
            Id id,
            String sObjectType,
            String icon,
            String title,
            String subtitle
    ) {
        this.id = id;
        this.sObjectType = sObjectType;
        this.icon = icon;
        this.title = title;
        this.subtitle = subtitle;
    }

    public LookupSearchResult (
            Id id,
            String sObjectType,
            String icon,
            String title,
            String subtitle,
            SObject record
    ) {
        this.id = id;
        this.sObjectType = sObjectType;
        this.icon = icon;
        this.title = title;
        this.subtitle = subtitle;
        this.record = record;
    }

    @AuraEnabled
    public Id getId () {
        return id;
    }

    @AuraEnabled
    public String getSObjectType () {
        return sObjectType;
    }

    @AuraEnabled
    public String getIcon () {
        return icon;
    }

    @AuraEnabled
    public String getTitle () {
        return title;
    }

    @AuraEnabled
    public String getSubtitle () {
        return subtitle;
    }

    @AuraEnabled
    public SObject getRecord () {
        return record;
    }
}