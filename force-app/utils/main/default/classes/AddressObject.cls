/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 12/6/24.
 */



public inherited sharing class AddressObject {
    private final Id contactPointAddressId;
    public String street;
    public String city;
    public String state;
    public String stateCode;
    public String postalCode;
    public String country;
    public String countryCode;
    public Decimal latitude;
    public Decimal longitude;
    public String geocodeAccuracy;

    public AddressObject(){}
    public AddressObject(SObject record, String addressField) {
        if (record != null && String.isNotBlank(addressField)) {
            if (addressField.endsWithIgnoreCase('__c')) {
                String fieldStart = addressField.toLowerCase().split('__c')[0];
                this.street = (String) record.get(fieldStart + '__street__s');
                this.city = (String) record.get(fieldStart + '__city__s');
                this.postalCode = (String) record.get(fieldStart + '__postalCode__s');
                this.latitude = (Decimal) record.get(fieldStart + '__latitude__s');
                this.longitude = (Decimal) record.get(fieldStart + '__longitude__s');
                this.geocodeAccuracy = (String) record.get(fieldStart + '__GeocodeAccuracy__s');
                if (OrgInfoUtils.isStateAndCountryPicklistsEnabled()) {
                    this.stateCode = (String) record.get(fieldStart + '__stateCode__s');
                    this.countryCode = (String) record.get(fieldStart + '__countryCode__s');
                } else {
                    this.state = (String) record.get(fieldStart + '__state__s');
                    this.country = (String) record.get(fieldStart + '__country__s');
                }
            } else if (addressField.endsWithIgnoreCase('__pc')) {
                String fieldStart = addressField.toLowerCase().split('__pc')[0];
                this.street = (String) record.get(fieldStart + '__street__ps');
                this.city = (String) record.get(fieldStart + '__city__ps');
                this.postalCode = (String) record.get(fieldStart + '__postalCode__ps');
                this.latitude = (Decimal) record.get(fieldStart + '__latitude__ps');
                this.longitude = (Decimal) record.get(fieldStart + '__longitude__ps');
                this.geocodeAccuracy = (String) record.get(fieldStart + '__GeocodeAccuracy__ps');
                if (OrgInfoUtils.isStateAndCountryPicklistsEnabled()) {
                    this.stateCode = (String) record.get(fieldStart + '__stateCode__ps');
                    this.countryCode = (String) record.get(
                            fieldStart + '__countryCode__ps'
                    );
                } else {
                    this.state = (String) record.get(fieldStart + '__state__ps');
                    this.country = (String) record.get(fieldStart + '__country__ps');
                }
            } else if (addressField.endsWithIgnoreCase('address')) {
                String fieldStart = addressField.toLowerCase().split('address')[0];
                this.street = (String) record.get(fieldStart + 'street');
                this.city = (String) record.get(fieldStart + 'city');
                this.postalCode = (String) record.get(fieldStart + 'postalCode');
                this.latitude = (Decimal) record.get(fieldStart + 'latitude');
                this.longitude = (Decimal) record.get(fieldStart + 'longitude');
                this.geocodeAccuracy = (String) record.get(fieldStart + 'GeocodeAccuracy');
                if (OrgInfoUtils.isStateAndCountryPicklistsEnabled()) {
                    this.stateCode = (String) record.get(fieldStart + 'stateCode');
                    this.countryCode = (String) record.get(fieldStart + 'countryCode');
                } else {
                    this.state = (String) record.get(fieldStart + 'state');
                    this.country = (String) record.get(fieldStart + 'country');
                }
            }
        }
    }
    public AddressObject(ContactPointAddress cpa) {
        this.contactPointAddressId = cpa?.Id;

        this.street = cpa.Street;
        this.city = cpa.City;
        this.postalCode = cpa.PostalCode;
        this.latitude = cpa.Latitude;
        this.longitude = cpa.Longitude;
        this.geocodeAccuracy = cpa.GeocodeAccuracy;
        if (OrgInfoUtils.isStateAndCountryPicklistsEnabled()) {
            this.stateCode = (String) cpa.get('StateCode');
            this.countryCode = (String) cpa.get('CountryCode');
        } else {
            this.state = cpa.State;
            this.country = cpa.Country;
        }
    }

    public Boolean isEmpty() {
        return (String.isBlank(street) &&
                String.isBlank(city) &&
                String.isBlank(state) &&
                String.isBlank(stateCode) &&
                String.isBlank(postalCode) &&
                String.isBlank(country) &&
                String.isBlank(countryCode) &&
                latitude == null &&
                longitude == null);
    }

    public Boolean isTheSame(AddressObject addrObj) {
        return (street == addrObj.street &&
                city == addrObj.city &&
                state == addrObj.state &&
                stateCode == addrObj.stateCode &&
                postalCode == addrObj.postalCode &&
                country == addrObj.country &&
                countryCode == addrObj.countryCode &&
                latitude == addrObj.latitude &&
                longitude == addrObj.longitude);
    }

    public ContactPointAddress setCpaAddress(ContactPointAddress cpa) {
        cpa.street = this.street;
        cpa.city = this.city;
        cpa.postalCode = this.postalCode;
        cpa.latitude = this.latitude;
        cpa.longitude = this.longitude;
        cpa.GeocodeAccuracy = this.geocodeAccuracy;
        if (OrgInfoUtils.isStateAndCountryPicklistsEnabled()) {
            cpa.put('stateCode', this.stateCode);
            cpa.put('countryCode', this.countryCode);
        } else {
            cpa.state = this.state;
            cpa.country = this.country;
        }
        return cpa;
    }
}