/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 1/2/24.
 */

public with sharing class CollectionUtils {

    /**
     * Modifies the specified list (in place) by removing all entries after the specified index.
     *
     * @param lst The list to modify
     * @param index The index at which all entries should be discarded.
     */
    public static void removeAllAtAndAfter(List<Object> lst, Integer index) {
        if (lst != null) {
            index = Utils.nullValue(index, 0);
            while (lst.size() > index) {
                lst.remove(index);
            }
        }
    }

    public class Iterator implements System.Iterator<Object> {
        private final List<Object> l;
        private Integer idx;
        private Boolean hasNext;
        private Boolean removeIsDirty; // prevents remove() from being called multiple times on the current position


        //private final Map m;

        public Iterator(List<Object> l) {
            this.l = l;

            this.idx            = 0;
            this.hasNext        = this.l == null ? false : this.l.size() > 0;
            this.removeIsDirty  = true;
        }

        public Boolean hasNext() {
            return this.hasNext;
        }

        public Object next() {
            Object o            = this.l?.get(this.idx++);
            this.hasNext        = this.idx < l.size();
            this.removeIsDirty  = false;
            return o;
        }

        /**
         * Removes from the underlying collection the last element returned by this iterator (optional operation).
         */
        public void remove() {
            if (!removeIsDirty) {
                this.l.remove(--this.idx);
                this.removeIsDirty = true;
            }
        }
    }
}