<?xml version="1.0" encoding="UTF-8"?>
<CustomLabels xmlns="http://soap.sforce.com/2006/04/metadata">
    <labels>
        <fullName>debug_domain_trigger_event_suppressed</fullName>
        <categories>error</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>debug_domain_trigger_event_suppressed</shortDescription>
        <value>====DOMAIN==== Trigger event &apos;&apos;{0}&apos;&apos; suppressed via setting for object &apos;&apos;{1}&apos;&apos;</value>
    </labels>
    <labels>
        <fullName>debug_domain_trigger_event_suppressed_mdt</fullName>
        <categories>error</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>debug_domain_trigger_event_suppressed_mdt</shortDescription>
        <value>====DOMAIN==== Trigger event &apos;&apos;{eventName}&apos;&apos; suppressed via setting for object &apos;&apos;{objectName}&apos;&apos;</value>
    </labels>
    <labels>
        <fullName>debug_domain_trigger_event_suppressed_runtime</fullName>
        <categories>error</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>debug_domain_trigger_event_suppressed_runtime</shortDescription>
        <value>====DOMAIN==== Trigger suppressed via runtime flag for object &apos;&apos;{objectName}&apos;&apos;</value>
    </labels>
    <labels>
        <fullName>exception_domain_class_not_found</fullName>
        <categories>error</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>exception_domain_class_not_found</shortDescription>
        <value>No class found with name &apos;&apos;{className}&apos;&apos;.</value>
    </labels>
    <labels>
        <fullName>exception_selector_filter_not_found</fullName>
        <categories>error</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>exception_selector_filter_not_found_1</shortDescription>
        <value>Provided querystring does not contain binding for &apos;&apos;{keyName}&apos;&apos; (this is case sensitive, and only allows one (or no) space on binding &apos;&apos;: varName&apos;&apos;)</value>
    </labels>
    <labels>
        <fullName>exception_selector_invalid_order_by</fullName>
        <categories>error</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>exception_selector_invalid_order_by</shortDescription>
        <value>setOrderBY requires a nonblank string and true or false boolean value.</value>
    </labels>
    <labels>
        <fullName>exception_selector_limit_invalid</fullName>
        <categories>error</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>exception_selector_limit_invalid</shortDescription>
        <value>Limit must be a positive integer.</value>
    </labels>
    <labels>
        <fullName>exception_selector_no_fields_specified</fullName>
        <categories>error</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>exception_selector_no_fields_specified</shortDescription>
        <value>Fields not found, or not specified.</value>
    </labels>
    <labels>
        <fullName>exception_selector_no_lead_found</fullName>
        <categories>error</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>exception_selector_no_lead_found</shortDescription>
        <value>No lead found with id {0}.</value>
    </labels>
    <labels>
        <fullName>exception_selector_no_limit_specified</fullName>
        <categories>error</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>exception_selector_no_limit_specified</shortDescription>
        <value>Please call setLimit with a positive integer before calling runQuery.</value>
    </labels>
    <labels>
        <fullName>exception_selector_no_object_specified</fullName>
        <categories>error</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>exception_selector_no_object_specified</shortDescription>
        <value>Object name cannot be null or empty.</value>
    </labels>
    <labels>
        <fullName>exception_selector_no_record_found</fullName>
        <categories>error</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>exception_selector_no_record_found</shortDescription>
        <value>No {0} found with id {1}.</value>
    </labels>
    <labels>
        <fullName>exception_selector_null_fields_to_query</fullName>
        <categories>error</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>exception_selector_null_fields_to_query</shortDescription>
        <value>&apos;&apos;null&apos;&apos; not allowed for individual fields to query, or set of fields to query.</value>
    </labels>
    <labels>
        <fullName>exception_selector_object_not_found</fullName>
        <categories>error</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>exception_selector_object_not_found</shortDescription>
        <value>No object found in global describe with name &apos;&apos;{objName}&apos;&apos;</value>
    </labels>
    <labels>
        <fullName>exception_selector_querystringissue</fullName>
        <categories>error</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>exception_selector_querystringissue</shortDescription>
        <value>Something went wrong with the query string, built or received blank.</value>
    </labels>
    <labels>
        <fullName>exception_selector_too_many_filters</fullName>
        <categories>error</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>exception_selector_too_many_filters</shortDescription>
        <value>Maximum of 10 filters is reached, aborting.</value>
    </labels>
    <labels>
        <fullName>Query_InvalidParams</fullName>
        <categories>DB</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Too many parameters</shortDescription>
        <value>Too many parameters have been specified for the query. Maximum of 20.</value>
    </labels>
    <labels>
        <fullName>CRUDFLS_InvalidFieldAccess</fullName>
        <categories>CRUD/FLS</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>CRUD/FLS: Invalid Field Access</shortDescription>
        <value>User :user does not have :action access to the following field(s) on object :object: :fields</value>
    </labels>
    <labels>
        <fullName>CRUDFLS_InvalidObjectAccess</fullName>
        <categories>CRUD/FLS</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>CRUD/FLS: Invalid Object Access</shortDescription>
        <value>User :user does not have :action access to object :object.</value>
    </labels>
    <labels>
        <fullName>DML_NullCollection</fullName>
        <categories>DB</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Can&apos;t perform DML on a null collection.</shortDescription>
        <value>Cannot :action on a null collection.</value>
    </labels>
    <labels>
        <fullName>DML_NullRecord</fullName>
        <categories>DB</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Can&apos;t perform DML on a null record.</shortDescription>
        <value>Cannot :action a null record.</value>
    </labels>
    <labels>
        <fullName>Exception_ParameterRequired</fullName>
        <categories>DB</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>Parameter required: account</shortDescription>
        <value>Parameter required: {paramName}</value>
    </labels>
</CustomLabels>
