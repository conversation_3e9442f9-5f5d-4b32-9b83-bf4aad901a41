/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by reiswarm<PERSON> on 11/6/19.
 */

/**
 *  Description:
 *  iterator function that returns the "cell" object for each row
 * @param rowIndex
 * @param tableData
 * @param columns
 * @param options
 * @returns {{done: boolean}|{next(): (*)}|{[Symbol.iterator](): *}|{value: {field: *, columnLabel: *, editable: *, options: *, id: *, row: *, type: *, selectColumn: *, parentId: *, typeAttributes: *}, done: boolean}}
 */
const getValue = (rowIndex, tableData, columns, options) => {
    let columnIndex = 0;
    return {
        [Symbol.iterator]() {
            return {
                next() {
                    let cellResult;
                    if (columnIndex < columns.length) {
                        let lookupRecord = columns[columnIndex].lookupField ? getLookup(tableData[rowIndex], columns[columnIndex].typeAttributes.fieldAPIName) : null;

                        let columnData = columns[columnIndex];
                        if (columnData.fieldName === 'Quantity' && tableData[rowIndex].Id !== null) {
                            columnData.editable = false;
                        }

                        cellResult = {
                            value: {
                                id              : tableData[rowIndex].Id,
                                //parentId        : tableData[rowIndex].OpportunityLineItemId,
                                row             : rowIndex,
                                cellValue       : lookupRecord ? lookupRecord["Id"] : tableData[rowIndex][columns[columnIndex].fieldName] ? tableData[rowIndex][columns[columnIndex].fieldName] : null,
                                /*lookupRecord    : columns[columnIndex].lookupField && tableData[rowIndex][columns[columnIndex].fieldName] ?
                                                    (columns[columnIndex].typeAttributes.fieldAPIName.endsWith('__c') ?
                                                        tableData[rowIndex][columns[columnIndex].typeAttributes.fieldAPIName.replace('__c', '__r')] :
                                                            (columns[columnIndex].typeAttributes.fieldAPIName.endsWith('Id') ?
                                                                tableData[rowIndex][columns[columnIndex].typeAttributes.fieldAPIName.replace('Id', '')] :
                                                                tableData[rowIndex][columns[columnIndex].typeAttributes.fieldAPIName]
                                                            )
                                                    ) : null,*/
                                lookupRecord    : lookupRecord,
                                columnData      : columnData,//columns[columnIndex],
                                options         : options
                            },
                            done: false,
                        };

                        columnIndex++;

                        return cellResult;
                    }
                    return { done: true };
                },
            };
        },
        ...tableData[rowIndex],
    };
}

const getLookup = (record, fieldPath) => {
    if (fieldPath.endsWith('__c')) {
        fieldPath = fieldPath.replace('__c','__r');
    } else if (fieldPath.endsWith('Id')) {
        fieldPath = fieldPath.replace('Id','');
    }

    const parts = fieldPath.split(".");
    let value = record;
    for (const part of parts) {
        if (value) {
            value = value[part];
        }
    }

    return value;
}


/**
 * Description:
 * creates an iterator which dynamically generates
 * an object for every cell using the getValue function
 * @param tableData
 * @param columns
 * @returns {{[Symbol.iterator](): *}|{next(): (*)}|{value: *, done: boolean}|{done: boolean}}
 */
const createTableIterator = (tableData, columns, options) => {
    let rowIndex = 0;
    return {
        [Symbol.iterator]() {
            return {
                next() {
                    let result;
                    if(tableData != undefined) {
                        if (rowIndex < tableData.length) {
                            result = {
                                value   : getValue(rowIndex, tableData, columns, options),
                                done    : false,
                            };
                            rowIndex++;
                            return result;
                        }
                    }
                    return { done: true };
                }
            };
        }
    };
}

export { getValue, createTableIterator }