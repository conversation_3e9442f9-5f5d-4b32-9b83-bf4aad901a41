/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

import { createElement } from 'lwc';
import { fireEvent, registerListener, unregisterAllListeners } from 'c/pubsub'
import { flushPromises, LOOKUP_SELECT_EVT } from "../../utility/utility";
import { SEARCH_RESULTS, CURRENT_SELECTION } from "../data/lookupData";
import Lookup from 'c/lookup';
import doSearch from '@salesforce/apex/LookupController.doSearch';
import {setImmediate} from 'timers';

const SAMPLE_SEARCH_RAW = 'Sample search* ';

jest.mock('c/lmsUtility', () => {
    return {
        publishEvent: jest.fn(),
        subscribeToEvents: jest.fn(),
        unsubscribeFromEvents: jest.fn()
    }
});

jest.mock('timers', () => {
    return {
        setImmediate: jest.fn()
    }
});


describe('c-lookup', () => {
    afterEach(() => {
        // The jsdom instance is shared across test cases in a single file so reset the DOM
        while (document.body.firstChild) {
            document.body.removeChild(document.body.firstChild);
        }
    });

    test('search results are retrieved', () => {
        jest.useFakeTimers();

        doSearch.mockResolvedValue(SEARCH_RESULTS);

        const element = createElement('c-lookup', {
            is: Lookup
        });
        element.selection = CURRENT_SELECTION;
        element.objectName = 'Product2';
        document.body.appendChild(element);

        const searchInput = element.shadowRoot.querySelector('input');
        searchInput.value = SAMPLE_SEARCH_RAW;
        searchInput.dispatchEvent(new CustomEvent('input'));

        jest.runAllTimers();

        expect(doSearch).toHaveBeenCalledTimes(1);
    });

    test('selection of search result', () => {

        // Create element with mock search handler
        const element = createElement('c-lookup', {
            is: Lookup
        });
        const mockLookSelectFunction = jest.fn();
        element.addEventListener(LOOKUP_SELECT_EVT, mockLookSelectFunction);

        element.searchResults = SEARCH_RESULTS;
        element.objectName = 'Product2';
        document.body.appendChild(element);

        return flushPromises().then(() => {
            const lookupResultsList = element.shadowRoot.querySelector('c-lookup-results-list');
            expect(lookupResultsList).not.toBeNull();

            lookupResultsList.dispatchEvent(new CustomEvent(LOOKUP_SELECT_EVT, {
                detail : CURRENT_SELECTION,
                bubbles: true,
                composed: true
            }));

        }).then(()=> {
            expect(mockLookSelectFunction).toBeCalledTimes(1);

            const selectEvent1 = mockLookSelectFunction.mock.calls[0][0];
            expect(selectEvent1.detail).toEqual({
                Id: CURRENT_SELECTION.id,
                Name: CURRENT_SELECTION.name
            });
        });
    });

    test('renders single entry (no selection)', () => {
        const element = createElement('c-lookup', {
            is: Lookup
        });
        document.body.appendChild(element);

        const selIcon = element.shadowRoot.querySelector('lightning-icon');
        expect(selIcon.alternativeText).toBe('Selected item icon');

        const clearSelButton = element.shadowRoot.querySelector('button');
        expect(clearSelButton.title).toBe('Remove selected option');

        const selList = element.shadowRoot.querySelectorAll(
            'ul.slds-listbox_inline'
        );
        expect(selList.length).toBe(0);
    });

    test('renders name inside input on selection', () => {
        const element = createElement('c-lookup', {
            is: Lookup
        });
        element.selection = CURRENT_SELECTION;
        element.objectName = 'Product2';
        document.body.appendChild(element);

        const inputBox = element.shadowRoot.querySelector('input');
        expect(inputBox.value).toBe(CURRENT_SELECTION.name);
    });

    test('clearing selection', () => {
        const element = createElement('c-lookup', {
            is: Lookup
        });
        element.selection = CURRENT_SELECTION;
        element.objectName = 'Product2';
        document.body.appendChild(element);

        const clearSelButton = element.shadowRoot.querySelector('button');
        clearSelButton.click();

        expect(element.selection).toBe(null);
    });

    test('render errors', () => {
        const element = createElement('c-lookup', {
            is: Lookup
        });

        document.body.appendChild(element);

        return flushPromises().then(() => {
            element.addError('Product field is required');

        }).then(() => {
            const errorElement = element.shadowRoot.querySelector('.slds-form-element__help');
            expect(errorElement.textContent).toBe('Product field is required');
        });
    });
});

jest.mock(
    '@salesforce/apex/LookupController.doSearch',
    () => {
        return {
            default: jest.fn()
        };
    },
    { virtual: true }
);