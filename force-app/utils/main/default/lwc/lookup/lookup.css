/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by reiswarman on 1/12/20.
 */

.slds-combobox__input,
.slds-combobox_container {
    transition: border 0.1s linear, box-shadow 0.1 linear;
}

.slds-combobox__input {
    box-shadow: none;
}

.slds-combobox__input.custom-border {
    box-shadow: 0 0 0 2px #fff inset, 0 0 0 3px rgb(221, 219, 218) inset;
}

.slds-combobox__input.custom-error {
    border: 1px solid rgb(194, 57, 52);
    box-shadow: rgb(194, 57, 52) 0 0 0 1px inset;
}

.slds-combobox_container.custom-error {
    border: none !important;
}

.slds-combobox__input.custom-height {
    height: 32px !important;
}

.form-error {
    color: rgb(194, 57, 52);
    display: block;
}

.search-icon {
    background-color: transparent;
}

.browsable {
    position: absolute;
    border-radius: 4px;
    border: none;
    right: 0.5px;
    /*border: 1px solid rgb(116,116,116);*/
    margin:2px;
    padding:6px;
    background-color: #FFFFFF;
    width: 2rem;
    line-height: 1;
    z-index: 2;
    vertical-align: middle;
    box-shadow: 0 0 0 0px #fff inset, 0 0 0 1px rgb(116, 116, 116) inset;
}
.browsable:hover {
    background-color: #f3f3f3;
}