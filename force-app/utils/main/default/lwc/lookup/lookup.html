<!--
  ~ Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
  ~
  ~ The code below is part of the Foglight Arts & Culture package and is not
  ~ to be redistributed without express written consent by Foglight.
  -->

<!--
 - Created by re<PERSON><PERSON><PERSON> on 1/6/20.
 -->

<template>
    <div class="slds-form-element">
        <div class="slds-form-element__control">
            <div class={getContainerClass}>
                <div class={getDropdownClass}
                     aria-expanded={isExpanded}
                     aria-haspopup="listbox"
                     role="combobox">

                    <!-- Search input start -->
                    <div class={getComboboxClass} role="none">
                        <lightning-icon
                                icon-name={getSelectIconName}
                                size="small"
                                alternative-text="Selected item icon"
                                class={getSelectIconClass}>
                        </lightning-icon>

                        <!-- Text input -->
                        <input type="text"
                               name="lookup-input"
                               class={getInputClass}
                               placeholder={placeholder}
                               aria-autocomplete="list"
                               aria-controls="listbox"
                               autocomplete="off"
                               role="textbox"
                               id="combobox"
                               value={getInputValue}
                               title={getInputTitle}
                               disabled={isInputDisabled}
                               onfocus={handleFocus}
                               onblur={handleBlur}
                               oninput={handleInput}/>

                        <!-- Search icon / browse button -->
                        <template if:true={browsable}>
                            <button title="Browse results..."
                                    type="button"
                                    onclick={handleBrowseClick}
                                    class={getSearchIconClass}>
                                <lightning-icon
                                        icon-name="utility:search"
                                        size="x-small"
                                        alternative-text="Search icon">
                                </lightning-icon>
                            </button>
                        </template>
                        <template if:false={browsable}>
                            <lightning-icon
                                    icon-name="utility:search"
                                    class={getSearchIconClass}
                                    size="x-small"
                                    alternative-text="Search icon">
                            </lightning-icon>
                        </template>

                        <!-- Clear selection button icon for single entry lookups -->
                        <button title="Remove selected option"
                                type="button"
                                onclick={handleClearSelection}
                                class={getClearSelectionButtonClass}>
                            <lightning-icon
                                    icon-name="utility:close"
                                    size="x-small"
                                    alternative-text="Remove selected option"
                                    class="slds-button__icon">
                            </lightning-icon>
                        </button>
                    </div>
                    <!-- Search input end -->
                    <div class="slds-has-error">
                        <div data-help-message="true"
                             role="alert"
                             class={getInputValidityMessage}>
                            {validityMessage}
                        </div>
                    </div>

                    <div id="listbox"
                         role="listbox"
                         onclick={handleComboboxClick}
                         style={dropdownStyles}>
                        <c-lookup-results-list search-results={searchResults}>
                        </c-lookup-results-list>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>