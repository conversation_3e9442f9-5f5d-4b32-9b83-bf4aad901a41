/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by reiswarman on 11/26/19.
 */

/*import tableSaveSuccessMessage from '@salesforce/label/c.TableSaveSuccessMessage';
import installmentUnderflowMessage from '@salesforce/label/c.InstallmentUnderflowMessage';
import installmentOverflowMessage from '@salesforce/label/c.InstallmentOverflowMessage';
import addRowUnderflowMessage from '@salesforce/label/c.AddRowUnderflowMessage';
import addRowOverflowMessage from '@salesforce/label/c.AddRowOverflowMessage';
import deriveRevenue from '@salesforce/label/c.DeriveRevenue';
import deriveQuantity from '@salesforce/label/c.DeriveQuantity';
import chooseScheduleType from '@salesforce/label/c.ChooseScheduleType';
import selectScheduleType from '@salesforce/label/c.SelectScheduleType';
import quantitySchedule from '@salesforce/label/c.QuantitySchedule';
import revenueSchedule from '@salesforce/label/c.RevenueSchedule';
import copyToRevenue from '@salesforce/label/c.CopyToRevenue';
import copyToQuantity from '@salesforce/label/c.CopyToQuantity';
import startDate from '@salesforce/label/c.StartDate';
import quantity from '@salesforce/label/c.SummaryQuantity';
import revenue from '@salesforce/label/c.SummaryRevenue';
import installmentPeriod from '@salesforce/label/c.InstallmentPeriod';
import installmentType from '@salesforce/label/c.InstallmentType';
import numInstallments from '@salesforce/label/c.NumInstallments';
import pleaseSelect from '@salesforce/label/c.PleaseSelect';
import establishSchedule from '@salesforce/label/c.EstablishSchedule';
import addRow from '@salesforce/label/c.AddRow';
import numOfRows from '@salesforce/label/c.NumOfRows';
import cancel from '@salesforce/label/c.Cancel';
import save from '@salesforce/label/c.Save';
import noOppLineItems from '@salesforce/label/c.NoOpportunityLineItems';
import updateLabel from '@salesforce/label/c.Update';
import selectedItemsLabel from '@salesforce/label/c.SelectedItems';
import apply from '@salesforce/label/c.Apply';
import changeSalesPrice from '@salesforce/label/c.ChangeSalesPrice';
import upvc from '@salesforce/label/c.UnitPriceChangeVerbiage';
import aysc from '@salesforce/label/c.AreYouSureConfirmation';
import yes from '@salesforce/label/c.Yes';
import product from '@salesforce/label/c.Product';
import originalSalesPrice from '@salesforce/label/c.OriginalSalesPrice';
import newSalesPrice from '@salesforce/label/c.NewSalesPrice';
import successTitle from '@salesforce/label/c.ToastSuccessTitle';
import errorTitle from '@salesforce/label/c.ToastErrorTitle';
import warningTitle from '@salesforce/label/c.ToastWarningTitle';
import infoTitle from '@salesforce/label/c.ToastInfoTitle';
import quantityRevenueError from '@salesforce/label/c.QuantityRevenueError';
import isRequiredError from '@salesforce/label/c.IsRequired';
import summaryTotalSched from '@salesforce/label/c.SummaryTotalSched';
import summaryTotalOppProduct from '@salesforce/label/c.SummaryTotalOppProduct';
import summarySalesPrice from '@salesforce/label/c.SummarySalesPrice';
import summaryDelta from '@salesforce/label/c.SummaryDelta';
import gridCalcBehavior from '@salesforce/label/c.GridCalculationBehavior';
import epsSettings from '@salesforce/label/c.EPSSettings';
import defaultSelection from '@salesforce/label/c.DefaultSelection';
import maintainSalesPrice from '@salesforce/label/c.MaintainSalesPrice';
import qrLinkedInfo from '@salesforce/label/c.PopoverQRLinked';
import qrUnlinkedInfo from '@salesforce/label/c.PopoverQRUnlinked';
import qLinkedInfo from '@salesforce/label/c.PopoverQLinked';
import qUnlinkedInfo from '@salesforce/label/c.PopoverQUnlinked';
import allProducts from '@salesforce/label/c.AllProducts';
import selectedProduct from '@salesforce/label/c.SelectedProduct';
import selectedSchedules from '@salesforce/label/c.SelectedSchedules';
import revenueScheduleOnly from '@salesforce/label/c.RevenueScheduleOnly';
import quantityScheduleOnly from '@salesforce/label/c.QuantityScheduleOnly';
import quantityRevenueSchedules from '@salesforce/label/c.QuantityRevenueSchedules';
import divideInstallmentType from '@salesforce/label/c.DivideInstallmentType';
import repeatInstallmentType from '@salesforce/label/c.RepeatInstallmentType';
import dailyInstallmentPeriod from '@salesforce/label/c.DailyInstallmentPeriod';
import weeklyInstallmentPeriod from '@salesforce/label/c.WeeklyInstallmentPeriod';
import monthlyInstallmentPeriod from '@salesforce/label/c.MonthlyInstallmentPeriod';
import quarterlyInstallmentPeriod from '@salesforce/label/c.QuarterlyInstallmentPeriod';
import yearlyInstallmentPeriod from '@salesforce/label/c.YearlyInstallmentPeriod';
import setupCheckFailureIntro from '@salesforce/label/c.SetupCheckFailureIntro';
import setupCheckFailureQREnabled from '@salesforce/label/c.SetupCheckFailureQREnabled';
import setupCheckFailureFLS from '@salesforce/label/c.SetupCheckFailureFLS';
import generalLoadError from '@salesforce/label/c.GeneralLoadError';*/


class CustomLabels {
    constructor() {
        if (!CustomLabels.instance) {
            this._data = {
                //tableSaveSuccessMessage     : tableSaveSuccessMessage,
                //installmentUnderflowMessage : installmentUnderflowMessage,
                //installmentOverflowMessage  : installmentOverflowMessage,
                //addRowUnderflowMessage      : addRowUnderflowMessage,
                //addRowOverflowMessage       : addRowOverflowMessage,
                //deriveQuantity              : deriveQuantity,
                //deriveRevenue               : deriveRevenue,
                //chooseScheduleType          : chooseScheduleType,
                //selectScheduleType          : selectScheduleType,
                //quantitySchedule            : quantitySchedule,
                //revenueSchedule             : revenueSchedule,
                //copyToRevenue               : copyToRevenue,
                //copyToQuantity              : copyToQuantity,
                //startDate                   : startDate,
                //quantity                    : quantity,
                //revenue                     : revenue,
                //installmentPeriod           : installmentPeriod,
                //installmentType             : installmentType,
                //numInstallments             : numInstallments,
                //pleaseSelect                : pleaseSelect,
                //establishSchedule           : establishSchedule,
                addRow                      : 'Add Row',
                //numOfRows                   : numOfRows,
                cancel                      : 'Cancel',
                save                        : 'Save',
                //noOppLineItems              : noOppLineItems,
                updateLabel                 : 'Update',
                selectedItemsLabel          : 'selected item(s)',
                apply                       : 'Apply',
                //areYouSureConfirmation      : aysc,
                //yes                         : yes,
                //product                     : product,
                //successTitle                : successTitle,
                //errorTitle                  : errorTitle,
                //warningTitle                : warningTitle,
                //infoTitle                   : infoTitle,
                //setupCheckFailureIntro      : setupCheckFailureIntro,
                //setupCheckFailureQREnabled  : setupCheckFailureQREnabled,
                //setupCheckFailureFLS        : setupCheckFailureFLS,
                //generalLoadError            : generalLoadError
            };
            CustomLabels.instance = this;
        }

        return CustomLabels.instance;
    }

    getLabels() {
        return this._data;
    }
}

const labels = new CustomLabels();
Object.freeze(labels);

export default labels;