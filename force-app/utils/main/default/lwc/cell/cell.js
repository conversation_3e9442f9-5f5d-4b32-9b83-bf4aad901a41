/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/* eslint-disable no-console */
/* eslint-disable @lwc/lwc/no-async-operation */
/**
 * Created by <PERSON><PERSON> on 9/17/2019.
 */

import { LightningElement, track, api, wire } from 'lwc';
import {
    CELL_CHANGE_EVT,
    SELECT_CHANGE_EVT,
    EDIT_MODAL_SHOW_EVT,
    SCHED_DATE_FIELD,
    TBL_DMSN_CHG_EVT,
    REVENUE_FIELD,
    QUANTITY_FIELD,
    CLICK_EVT
} from 'c/utility';

import LMSUtility from "c/lmsUtility";
import { MessageContext } from 'lightning/messageService';

import labels from 'c/customLabels';
const DELAY = 500;

export default class Cell extends LightningElement {
    @wire(MessageContext)
    messageContext;

    subscription;

    @track cellData;
    @track editedCell;
    @track type;
    @track formatter;
    @track isEditableInput          = false;
    @track isEditablePicklist       = false;
    @track isEditableMultipicklist  = false;
    @track isEditableTextArea       = false;
    @track isNonEditableText        = false;
    @track isActionRow              = false;
    @track isSelectColumn           = false;
    @track isRowNumColumn           = false;
    @track isSelectChecked          = false;
    @track showEditPopup            = false;

    @track editPopupStyles;
    @track _selectedRows;
    @track numRowsSelectedDisplay;

    timeout;
    numRowsSelected = 0;
    applyToSelected = false;
    shiftClicked = false;
    labels;

    @api
    get selectedRows() {
        return this._selectedRows;
    }

    set selectedRows(value) {
        this._selectedRows              = value;
        let selectedRowKeys             = value ? Object.keys(value) : [];
        let selectedRowKeysLength       = selectedRowKeys.length;
        this.numRowsSelectedDisplay     = selectedRowKeysLength;

        if(value[this.row]) {
            selectedRowKeysLength --;
        }
        this.numRowsSelected = selectedRowKeysLength;

        if(this.showEditPopup) {
            if(this.isInputCell) {
                if(this.numRowsSelected === 0 || !this._selectedRows[this.row]) {
                    this.handleCloseEditModal();
                }
            }
        }
    }

    @api
    get cellDataIn() {
        return this.cellData;
    }

    set cellDataIn(data) {
        this.cellData       = {...data};
        this.type           = this.cellData.columnData.type;
        this.formatter      = this.cellData.columnData.formatter;
        this.isSelectColumn = this.cellData.columnData.selectColumn;
        this.isRowNumColumn = this.cellData.columnData.rowNumberColumn;

        this.setRenderAttributes();
        this.setAttribute('data-field', this.fieldName);
        this.setAttribute('data-select-col', this.isSelectColumn);
        this.setAttribute('data-input-cell', this.isInputCell);
    }

    @api
    get value() {
        return this.cellData.cellValue;
    }

    set value(value) {
        this.cellData.cellValue = value;
    }

    @api
    get isRequired() {
        return this.cellData.columnData.required;
    }

    @api
    get fieldLabel() {
        return this.cellData.columnData.label;
    }

    @api
    get isValid() {
        const element = this.getInputElement();
        return element ? element.checkValidity() : true;
    }

    @api
    get fieldName() {
        return this.typeAttributes != null ? this.typeAttributes.fieldAPIName : null;
    }

    get isLookupField() {
        return this.cellData.columnData.lookupField;
    }

    get objectName() {
        return this.typeAttributes != null ? this.typeAttributes.objectAPIName : null;
    }

    get maxLength() {
        return this.typeAttributes != null ? this.typeAttributes.maxLength : null;
    }

    get step() {
        return this.typeAttributes != null ? this.typeAttributes.step : null;
    }

    get fqFieldName() {
        return this.typeAttributes != null ? this.objectName + '.' + this.fieldName : null;
    }

    get picklistValues() {
        return this.typeAttributes != null ? [{ label: 'Select an Option', value: null }, ...this.typeAttributes.picklistValues]: null;
    }

    get isEditable() {
        return this.cellData.columnData.editable || (this.recordId == null && this.cellData.columnData.createable);
    }

    get iconName() {
        return this.cellData.columnData.iconName;
    }

    get parentRecordId() {
        return this.cellData.parentId;
    }

    get recordId() {
        return this.cellData.id;
    }

    get row() {
        return this.cellData.row;
    }


    get userVisibleRow() {
        return this.cellData.row + 1;
    }

    get tableOptions() {
        return this.cellData.options;
    }

    get actions() {
        return this.typeAttributes.rowActions;
    }

    get multipleActions() {
        return this.actions ? this.actions.length > 1 : false;
    }

    get options() {
        return this.typeAttributes.options;
    }

    get typeAttributes() {
        return this.cellData.columnData.typeAttributes;
    }

    get isInputCell() {
        return (this.isEditablePicklist || this.isEditableInput || this.isEditableTextArea || this.isEditableMultipicklist);
    }

    get lookupObjectAPIName() {
        return this.cellData.columnData.lookupRelatedObjectAPIName;
    }

    get lookupRecord() {
        if(this.value) {
            return {
                Id: this.cellData.lookupRecord.Id,
                Name: this.cellData.lookupRecord.Name
            };
        }
        return null;
    }
    get lookupRecordName() {
        if(this.value) {
            return this.cellData.lookupRecord.Name;
        }
        return null;
    }

    get searchFilter() {
        return this.cellData.columnData.typeAttributes.searchFilter;
    }
    get returnFields() {
        return this.cellData.columnData.typeAttributes.returnFields;
    }

    connectedCallback() {
        this.labels = labels.getLabels();
        this.template.addEventListener(CLICK_EVT, this.handleClick.bind(this));

        const eventHandlers = {
            [TBL_DMSN_CHG_EVT]: this.handlePositionEditModal.bind(this),
            [SELECT_CHANGE_EVT]: this.handleSelectEvent.bind(this)
        };

        this.subscription = LMSUtility.subscribeToEvents(this.messageContext, eventHandlers);
    }

    disconnectedCallback() {
        LMSUtility.unsubscribeFromEvents(this.subscription);
    }

    renderedCallback() {
        this.handlePositionEditModal();
    }

    /**
     * Description:
     * listens for position change events and
     * manually adjusts position of an edit
     * popup modal (if one is open)
     * @param event
     */
    @api
    handlePositionEditModal(event) {
        if(this.showEditPopup) {
            let cellContainer = this.template.querySelector('.cell-container');
            if(cellContainer) {
                let containerCoords = cellContainer?.getBoundingClientRect();
                let container2Coords= cellContainer?.offsetParent?.getBoundingClientRect();

                const x = containerCoords?.left - container2Coords?.left + 12;
                const y = containerCoords?.top - 21;
                this.editPopupStyles = 'position: fixed; z-index: 10; top: ' + (y) + 'px; left: ' + (x) + 'px; min-width: ' + (containerCoords?.right - containerCoords?.left) + 'px;';
            }
        }
    }
    getOffset( el ) {
        var _x = 0;
        var _y = 0;
        while( el && !isNaN( el.offsetLeft ) && !isNaN( el.offsetTop ) ) {
            _x += el.offsetLeft - el.scrollLeft;
            _y += el.offsetTop - el.scrollTop;
            el = el.offsetParent;
        }
        return { top: _y, left: _x };
    }

    /**
     * Description:
     * resets all the input rendering attributes
     */
    setRenderAttributes() {

        this.isEditableInput = false;
        this.isEditablePicklist = false;
        this.isEditableMultipicklist = false;
        this.isEditableTextArea = false;
        this.isNonEditableText = false;

        if (this.type === 'textarea' &&
            this.isEditable
        ) {
            this.isEditableTextArea = true;
        } else if (this.type !== 'textarea' &&
            this.type !== 'picklist' &&
            this.type !== 'multipicklist' &&
            this.type !== 'action' &&
            this.isEditable &&
            !this.isSelectColumn
        ) {
            this.isEditableInput = true;

        } else if (this.type === 'picklist' &&
            this.isEditable
        ) {
            this.isEditablePicklist = true;

        } else if (this.type === 'multipicklist' &&
            this.isEditable
        ) {
            this.isEditableMultipicklist = true;

        } else if (this.type === 'action') {
            this.isActionRow = true;

        } else if (!this.cellData.columnData.editable) {
            this.isNonEditableText = true;
        }
    }

    handleSelectEvent(event) {
        if(event.selectAllRows && this.isSelectColumn) {
            this.isSelectChecked = event.value;
        } else if(event.shiftClick && event.shiftSelectedRow === this.row && this.isSelectColumn) {
            this.isSelectChecked = event.value;
        }
    }

    handleClick(event) {
        this.shiftClicked = event.shiftKey;
    }

    /**
     * Description:
     * validates the given cell
     * @returns {boolean}
     */
    @api
    validateCell() {
        var isValid = false;
        var inputCmp = this.getInputElement();

        if(inputCmp) {
            if(this.isLookupField || this.isEditableMultipicklist) {
                isValid = !(!this.getInputValue() && this.isRequired);
            } else {
                inputCmp.reportValidity();
                isValid = inputCmp.checkValidity() !== undefined ? inputCmp.checkValidity() : true;
            }
        }
        return isValid;
    }

    /**
     * Description:
     * sets custom validity message on input
     * component and reports it
     * @param msg
     */
    @api
    setCellError(msg) {
        var inputCmp;

        if(this.isLookupField) {
            inputCmp = this.getLookup();
        } else if(this.isEditableMultipicklist) {
            inputCmp = this.getComboboxMulti();
        } else {
            inputCmp = this.getInputElement();
        }

        if(inputCmp) {
            if(this.isLookupField || this.isEditableMultipicklist) {
                inputCmp.addError(msg);
            } else {
                inputCmp.setCustomValidity(msg);
            }
        }
    }

    /**
     * Description:
     * handles resetting the validity
     * message of the cells input component
     */
    @api
    resetCellValidity() {
        var inputCmp;

        if(this.isLookupField) {
            inputCmp = this.getLookup();
        } else if(this.isEditableMultipicklist) {
            inputCmp = this.getComboboxMulti();
        } else {
            inputCmp = this.getInputElement();
        }

        if(inputCmp) {
            if(this.isLookupField || this.isEditableMultipicklist) {
                inputCmp.clearErrors();
            } else {
                inputCmp.setCustomValidity('');
                inputCmp.reportValidity();
            }
        }
    }

    /**
     * Description:
     * handles closing the edit modal
     */
    @api
    handleCloseEditModal() {
        this.showEditPopup = false;
        this.applyToSelected = false;
    }

    /**
     * Description:
     * single event handler for all cell edit events
     * calls executeCellEdit() with the new value
     * @param event
     */
    handleCellEdit(event) {
        console.log('cell:handleCellEdit');
        try {
            console.log('handleCellEdit: ' + JSON.stringify(event.target.value));
            console.log('handleCellEdit: ' + JSON.stringify(event.detail));
        } catch (e) {
            console.log('handleCellEdit logging error');
        }
        event.preventDefault();
        let eventValue;

        // determine where the event is coming from
        // to get the value
        if(this.isLookupField || this.isEditableMultipicklist) {
            eventValue = event.detail;
        } else if(!this.isSelectColumn) {
            eventValue = event.target.value;
        } else if(this.isSelectColumn) {
            eventValue = event.detail.checked;
        }


        this.executeCellEdit(eventValue);
    }

    /**
     * Description:
     * handles the actual logic for a cell edit
     * @param value
     */
    executeCellEdit(value) {

        if(value === undefined && (this.fieldName === REVENUE_FIELD || this.fieldName === QUANTITY_FIELD)) {
            value = 0;
        }

        if(this.isSelectColumn) {
            this.isSelectChecked = value;

            const messageDetail = {
                type: SELECT_CHANGE_EVT,
                parentRecordId: this.parentRecordId,
                recordId: this.recordId,
                row: this.row,
                shiftClicked: this.shiftClicked,
                value: value
            };
            LMSUtility.publishEvent(this.messageContext, messageDetail);

        } else {
            window.clearTimeout(this.timeout);
            this.timeout = setTimeout(() => {
                this.resetCellValidity();

                if (this.tableOptions !== null) {
                    if (this.tableOptions.saveMethod === 'onchange') {
                        this.handleFieldChangeOnChange(value);
                    } else if (this.tableOptions.saveMethod === 'onsave') {
                        this.handleFieldChangeOnSave(value);
                    }
                }

            }, DELAY);
        }

        this.editedCell = 'edited-cell';
    }

    /**
     * Description:
     * helper method that is called when
     * the option for immediate updates on
     * field changes is selected
     * @param event
     */
    handleFieldChangeOnChange() {}

    /**
     * Description:
     * helper method that is called when
     * the option for waiting to persist data
     * "on save" is selected
     * @param event
     */
    handleFieldChangeOnSave(value) {
        console.log('handleFieldChangeOnSave' + JSON.stringify(value));
        this.dispatchEvent(new CustomEvent(CELL_CHANGE_EVT, {
            detail : {
                parentRecordId  : this.parentRecordId,
                recordId        : this.recordId,
                field           : this.fieldName,
                row             : this.row,
                applyToSelected : this.applyToSelected,
                isLookup        : this.isLookupField,
                value           : value,
            }
        }));
    }

    /**
     * Description:
     * handles firing an event for a row action
     * @param event
     */
    handleRowAction(event) {
        event.preventDefault();
        const actionName = event.detail.value ? event.detail.value : event.target.value;
        const rowAction = new CustomEvent('rowaction', {
            bubbles: true,
            composed: true,
            detail: {
                action          : actionName,
                parentRecordId  : this.parentRecordId,
                recordId        : this.recordId,
                row             : this.row,
            }
        });
        this.dispatchEvent(rowAction);
    }

    /**
     * Description:
     * handles input focus so that we
     * can pop the edit modal if there
     * multiple selections aside from it's
     * own row
     */
    handleOnFocus(event) {
        event.preventDefault();

        this.dispatchEvent(
            new CustomEvent(EDIT_MODAL_SHOW_EVT, {
                bubbles: true,
                composed: true
            })
        );

        if(this.numRowsSelected > 0 && this.fieldName !== SCHED_DATE_FIELD && this._selectedRows[this.row] !== undefined) {
            this.showEditPopup = true;
        }
    }

    /**
     * Description:
     * handles setting the applyToSelected
     * variable when it is checked in the
     * edit modal
     * @param event
     */
    handleApplyToSelected(event) {
        this.applyToSelected = event.detail.checked;
    }

    /**
     * Description:
     * handles saving the value that was
     * set inside the edit modal followed
     * by closing the modal
     */
    handleEditModalApply() {
        console.log('handleEditModalApply');
        this.handleFieldChangeOnSave(this.getInputValue());
        this.handleCloseEditModal();
    }

    /**
     * Description:
     * helper function to get the the base input component
     * for this cell in order to be able to operate on it
     * @returns {Element | any}
     */
    getInputElement() {
        if(this.isEditableInput) {
            if(this.isLookupField) {
                return this.getLookup().getInput();
            }
            return this.template.querySelector('lightning-input');

        } else if(this.isEditablePicklist) {
            return this.template.querySelector('lightning-combobox');
        } else if(this.isEditableMultipicklist) {
            return this.template.querySelector('c-combobox-multi');
        } else if(this.isEditableTextArea) {
            return this.template.querySelector('lightning-textarea');
        }
        return null;
    }

    /**
     * Description:
     * helper function to return the value of whatever
     * the currently rendered input element is
     * @returns {*}
     */
    getInputValue() {
        if(this.isLookupField) {
            return this.getLookup().getSelectedRecordFormatted();

        } else if(this.isEditableMultipicklist) {
            return this.getComboboxMulti().getSelectedItems(true);

        } else {
            const element = this.getInputElement();
            if(element) {
                return element.value;
            }
        }
    }

    /**
     * Description:
     * queries for a lookup component
     * @returns {*}
     */
    getLookup() {
        return this.template.querySelector('c-lookup');
    }

    /**
     * Description:
     * queries for a lookup component
     * @returns {*}
     */
    getComboboxMulti() {
        return this.template.querySelector('c-combobox-multi');
    }
}