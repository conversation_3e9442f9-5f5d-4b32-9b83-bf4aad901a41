/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/* eslint-disable no-console */
/**
 * Created by <PERSON><PERSON> on 9/17/2019.
 */

import { LightningElement, api, track, wire } from 'lwc';
import { CELL_CHANGE_EVT, EDIT_MODAL_SHOW_EVT, RESIZE_COL_EVT, SELECT_CHANGE_EVT, TBL_DMSN_CHG_EVT, SELECT_CHANGE_SCHEDULER_EVT } from 'c/utility';
import { createTableIterator } from 'c/iteratorUtility';

import LMSUtility from "c/lmsUtility";
import { MessageContext } from 'lightning/messageService';

const actions = [
    { label: 'Delete', name: 'delete', iconName: "utility:delete" },
];

export default class Datatable extends LightningElement {
    @wire(MessageContext)
    messageContext;

    subscription;
    handleScrollBound;
    handleResizeBound;

    @track _tableData;
    @track _columns;
    @track tableWidth;
    @track selectedRows = {};

    @api options;
    @api draftState;
    @api schedType;
    @api hasLockingTogglePerm;
    @api minColumnWidth = 30;
    @api maxColumnWidth = 1000;
    @api objectName;
    @api fieldSetName;
    @api parentFieldToFilterBy;
    @api parentRecordId;

    isRelated;
    lastSelected;

    get tableIterator() {
        return createTableIterator(this._tableData, this._columns, this.options);
    }

    get tableStyles() {
        return this.tableWidth ? `width: ${this.tableWidth}px` : undefined;
    }

    @api
    get tableData() {
        return this._tableData;
    }
    set tableData(value) {
        this._tableData = value;
        this.isRelated = this._tableData[0] ? this._tableData[0].epsched__IsRelated__c : false;
    }

    @api
    get columns() {
        return this._columns;
    }
    set columns(value) {
        this._columns   = this.buildColumns(value);
    }

    @api
    getTableWidth() {
        return this.template.querySelector('table').offsetWidth;
    }

    @api
    getSelectedRows() {
        return this.selectedRows;
    }

    @api
    setSelectedRows(rows) {
        this.selectedRows = rows;
    }

    @api
    selectedSchedulesESEnabled() {

    }

    /**
     * Description:
     * called by schedule.js when vertical
     * nav item selection changes
     */
    @api
    clearSelections() {
        this.selectedRows = {};
        this.clearHeaderSelections();

        const messageDetail = {
            type: SELECT_CHANGE_EVT,
            selectAllRows: true,
            value: false
        };
        LMSUtility.publishEvent(this.messageContext, messageDetail);

    }

    connectedCallback() {
        this.template.addEventListener(EDIT_MODAL_SHOW_EVT, this.handleEditModalShow.bind(this));
        this.template.addEventListener(RESIZE_COL_EVT, this.handleResizeColumn.bind(this));

        this.handleScrollBound = this.handleViewChange.bind(this);
        this.handleResizeBound = this.handleViewChange.bind(this);

        window.addEventListener('scroll', this.handleScrollBound);
        window.addEventListener('resize', this.handleResizeBound);

        const eventHandlers = {
            [SELECT_CHANGE_EVT]: this.handleSelectionCellChange.bind(this)
        };
        this.subscription = LMSUtility.subscribeToEvents(this.messageContext, eventHandlers);
    }

    disconnectedCallback() {
        LMSUtility.unsubscribeFromEvents(this.subscription);
        window.removeEventListener('scroll', this.handleScrollBound);
        window.removeEventListener('resize', this.handleResizeBound);
    }

    handleViewChange(event) {
        const messageDetail = {
            type: TBL_DMSN_CHG_EVT
        };
        LMSUtility.publishEvent(this.messageContext, messageDetail);
    }

    /**
     * Description:
     * handles a column selection checkbox change
     * @param event
     */
    handleSelectionCellChange(event) {
        const detail = event;

        if(detail.selectAllRows) {
            this.selectedRows = detail.value === true ? { ...[...Array(this._tableData.length).keys()] } : {};
            this.lastSelected = this._tableData.length - 1;

        } else {
            let tempSelectedRows = this.selectedRows;

            // checking for a shift click multi selection
            if(detail.shiftClicked && this.lastSelected != null) {
                let start = detail.row;
                let end = this.lastSelected;

                for (let row = Math.min(start,end); row <= Math.max(start,end); row++) {
                    const messageDetail = {
                        type: SELECT_CHANGE_EVT,
                        shiftClick: true,
                        shiftSelectedRow: row,
                        value: detail.value
                    };
                    LMSUtility.publishEvent(this.messageContext, messageDetail);

                    if(detail.value === true) {
                        tempSelectedRows[row] = row;
                    } else {
                        delete tempSelectedRows[row];
                    }
                }

            // no shift click multi select so only worry
            // about single selection
            } else {
                if(detail.value === true) {
                    tempSelectedRows[detail.row] = detail.row;
                } else {
                    delete tempSelectedRows[detail.row];
                }
            }

            this.lastSelected = detail.row;
            this.selectedRows = Object.assign({...tempSelectedRows});
            this.clearHeaderSelections();
        }

        this.dispatchEvent(new CustomEvent(SELECT_CHANGE_SCHEDULER_EVT, {detail: Object.values(this.selectedRows)}));
    }

    handleEditModalShow(event) {
        event.stopPropagation();
        this.template.querySelectorAll('c-row')
            .forEach(row => {
                row.clearEditModals();
            }
        );
    }

    range(start, end) {
        return Array(end - start + 1).fill().map((_, idx) => start + idx)
    }

    /**
     * Description:
     * handles bubbling a cell change event up to
     * the parent container component
     * @param event
     */
    handleFieldCellChange(event) {
        console.log('datatable.handleFieldCellChange: ' + JSON.stringify(event.detail));
        const selectedRowsArr = Object.keys(this.selectedRows);

        if(event.detail.applyToSelected && selectedRowsArr.length > 0) {
            selectedRowsArr.forEach((rowNum) => {
                this.dispatchEvent(new CustomEvent(CELL_CHANGE_EVT, {
                    detail: {
                        ...event.detail,
                        row: rowNum
                    }
                }));
            });

        } else {
            this.dispatchEvent(new CustomEvent(CELL_CHANGE_EVT, event));
        }

        /*const detail = event.detail;
        this._columns.filter(col => col.fieldName === detail.field).forEach(col => {
            if (col.total) {
                col.total += detail.value - this._tableData[detail.row][detail.field];
            }
        });*/
    }

    /**
     * Description:
     * validates datatable by calling validateRow()
     * on each row in the datatable
     * @returns {boolean}
     */
    @api
    validateDatatable() {
        var isDatatableValid = true;

        this.template.querySelectorAll('c-row').forEach((row) => {
            var isRowValid = row.validateRow();

            if(!isRowValid)
                isDatatableValid = false;
        });

        return isDatatableValid;
    }

    /**
     * Description:
     * resets the validity of each row
     * in the datatable so errors are
     * not left on inputs
     */
    @api
    resetTableValidity() {
        [...this.template.querySelectorAll('c-row')].forEach((row) => {
            row.resetRowValidity();
        });
    }

    /**
     * Description:
     * handles a resize event
     * @param event
     */
    handleResizeColumn(event) {
        event.stopPropagation();
        const { colIndex, widthDelta } = event.detail;
        if (widthDelta !== 0) {
            this.tableWidth = this.tableWidth + widthDelta;
            this._columns = this._columns.map((column, index) => {
                const isCurrentColumnResized = colIndex === index;
                if (isCurrentColumnResized) {
                    const newColumnWidth = column.width + widthDelta;
                    return {
                        ...column,
                        width: newColumnWidth,
                        style: `width: ${newColumnWidth}px`,
                    };
                }
                return column;
            });
        }
    }

    /**
     * Description:
     * helper method to fire a resize
     * event
     */
    fireOnResize() {
        const event = new CustomEvent('resize', {
            detail: {
                columnWidths: this.columnsWidth,
            },
        });
        this.dispatchEvent(event);
    }

    /**
     * Description:
     * helper methods to clear the checkboxes on
     * all select column header cells
     */
    clearHeaderSelections() {
        this.template.querySelectorAll('c-datatable-header[data-select-col="true"]').forEach(element => {
            if(element) {
                element.clearColumnSelection();
            }
        });
    }

    buildColumns(dataColumns) {
        var baseColumns = [
            {
                type: 'text',
                rowNumberColumn: true,
                editable: false,
                width: 20
            },
            {
                type: 'checkbox',
                selectColumn: true,
                editable: true,
                width: 25
            }
        ];
        baseColumns.push(...dataColumns);
        baseColumns.push({
            type: 'action',
            typeAttributes: {
                rowActions: actions,
                menuAlignment: 'right'
            },
            width: 45
        });

        const initialTableWidth = this.template.querySelector('div[data-id="table"]') ? this.template.querySelector('div[data-id="table"]').offsetWidth : 1000;

        var columns                 = [...baseColumns];
        var preDefinedWidthsTotal   = 0;
        var numDefinedWidths        = 0;

        columns.forEach(column => {
            preDefinedWidthsTotal   += column.width ? column.width+4 : 0;
            numDefinedWidths        += column.width ? 1 : 0;
        });


        const columnWidth = Math.round(
            (initialTableWidth - preDefinedWidthsTotal) / (columns.length - numDefinedWidths)
        );

        /*columns.forEach( column => {
            if (column.width) {
                column.style = `width: ${column.width}px`;
            }
        });*/
        columns = columns.map(column => ({
            ...column,
            width: column.width ? column.width : columnWidth,
            style: `width: ${column.width ? column.width : columnWidth}px`
        }));

        return columns;
    }
}