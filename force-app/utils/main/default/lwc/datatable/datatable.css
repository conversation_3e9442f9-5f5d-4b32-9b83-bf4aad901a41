/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by <PERSON><PERSON> on 9/17/2019.
 */

.table-height {
    height: 100%;/*40em*/
}

.datatable-container {
    position: relative;
    height: 100%;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
}

.datatable-table {
    table-layout: fixed;
}

.datatable-scrollable-y-container {
    height: 100%;
}

.datatable-scrollable-x {
    overflow-x: visible;
}

.row {
    background-color: #FFFFFF;
}
.row:hover {
    background-color: var(--lwc-colorBackground,rgb(243, 243, 243));/*var(--lwc-colorBorder,rgb(229, 229, 229));*/
    border-top: 1px solid #c9c9c9;
    border-bottom: 1px solid #c9c9c9;
}


td {
    padding-top: .25rem;
    padding-bottom: .25rem;
    padding-left: .2rem; /* cell padding since we aren't using slds-table */
    padding-right: .2rem;
    text-align: center; /* center checkbox and plain text horizontally */
}

tfoot tr td {
    text-align: left;
    padding: var(--lwc-spacingSmall);
    border-top: var(--lwc-borderWidthThin,1px) solid var(--slds-g-color-border-base-1, var(--lwc-colorBorder,rgb(229, 229, 229)));
}