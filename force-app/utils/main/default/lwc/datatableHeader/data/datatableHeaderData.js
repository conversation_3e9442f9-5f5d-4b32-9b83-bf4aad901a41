/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

const COLUMN_ROW_NUM =
    {
        type: 'text',
        rowNumberColumn: true,
        editable: false,
        width: 20,
        style: 'width: 20px'
    }

const COLUMN_SELECT =
    {
        type: 'checkbox',
        selectColumn: true,
        editable: true,
        width: 25,
        style: 'width: 25px'
    }

const COLUMN_SCHEDULE_DATE =
    {
        type: 'date',
        label: 'Schedule Date',
        fieldName: 'ScheduleDate',
        typeAttributes: {
            fieldAPIName: 'ScheduleDate',
            objectAPIName: 'OpportunityLineItemSchedule'
        },
        editable: true,
        required: true,
        width: 386,
        style: 'width: 386px'
    }

const COLUMN_QUANTITY =
    {
        type: 'number',
        label: 'Quantity',
        fieldName: 'Quantity',
        typeAttributes: {
            fieldAPIName: 'Quantity',
            objectAPIName: 'OpportunityLineItemSchedule',
            step: '0.01'
        },
        editable: true,
        width: 386,
        style: 'width: 386px'
    }

const COLUMN_REVENUE =
    {
        type: 'action',
        typeAttributes: {
            rowActions: [
                {
                    label: 'Delete',
                    name: 'delete',
                    iconName: 'utility:delete'
                }
            ],
            menuAlignment: 'right'
        },
        width: 80,
        style: 'width: 80px'
    }

export { COLUMN_ROW_NUM, COLUMN_SELECT, COLUMN_SCHEDULE_DATE, COLUMN_QUANTITY, COLUMN_REVENUE }