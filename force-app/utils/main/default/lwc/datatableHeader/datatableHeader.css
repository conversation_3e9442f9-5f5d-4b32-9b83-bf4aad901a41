/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by <PERSON><PERSON> on 9/17/2019.
 */
.datatable-header-container {
    outline: none;
}

.datatable-header-resize-bar {
    will-change: transform;
}

.th__action {
    display: flex;
    padding: 0.25rem;
    height: 2rem;
    align-items: center;
}

.th__label {
    display: flex;
    padding: 0.6rem;
    height: 2rem;
    align-items: center;
    position: relative;
}

.th__link {
    position: absolute;
    right: 0;
}

.invisible-container {
    display: contents;
}