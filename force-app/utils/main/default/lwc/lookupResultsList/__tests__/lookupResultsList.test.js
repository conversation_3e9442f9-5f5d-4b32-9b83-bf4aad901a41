/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

import { createElement } from 'lwc';
import { flushPromises } from "../../utility/utility";
import { SEARCH_RESULTS } from "../data/lookupResultsListData";
import LookupResultsList from 'c/lookupResultsList';


describe('c-lookup-results-list functional', () => {
    afterEach(() => {
        // The jsdom instance is shared across test cases in a single file so reset the DOM
        while (document.body.firstChild) {
            document.body.removeChild(document.body.firstChild);
        }
    });


    test('result is displayed when passed in', () => {
        const element = createElement('c-lookup-results-list', {
            is: LookupResultsList
        });

        document.body.appendChild(element);

        return flushPromises().then(() => {
            const listItemEls = element.shadowRoot.querySelector('li');
            expect(listItemEls).not.toBeNull();
            expect(listItemEls.textContent).toBe('No results.');
        });
    });

    test('test that c-lookup-results-list-item components are rendered', () => {
        const element = createElement('c-lookup-results-list', {
            is: LookupResultsList
        });

        element.searchResults = SEARCH_RESULTS;
        document.body.appendChild(element);

        return flushPromises().then(() => {
            const listItemEls = element.shadowRoot.querySelectorAll('c-lookup-results-list-item');
            expect(listItemEls.length).toBe(1);
        });
    });
});
