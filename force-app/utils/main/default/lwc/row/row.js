/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by <PERSON><PERSON> on 10/4/2019.
 */

import { LightningElement, track, api } from 'lwc';
import labels from 'c/customLabels';

import {
    QUANTITY_SCHEDULE, REVENUE_SCHEDULE, BOTH_SCHEDULE,
    DONT_LOCK_SALESPRICE, LOCK_SALESPRICE,
    QUANTITY_FIELD, REVENUE_FIELD,
    CELL_CHANGE_EVT
}
    from 'c/utility';

export default class Row extends LightningElement {
    @track rowData;
    @api schedType;
    @api selectedRows;
    labels;

    @api
    get rowDataIn() {}

    set rowDataIn( data ) {
        this.rowData = data;
    }

    get unitPrice() {
        return this.rowData?.OpportunityLineItem.UnitPrice;
    }

    get isRelated() {
        return this.rowData?.epsched__IsRelated__c;
    }

    connectedCallback() {
        this.labels = labels.getLabels();
    }

    /**
     * Description:
     * validates the given row and
     * returns validity
     * @returns {boolean}
     */
    @api
    validateRow() {
        var isRowValid = true;
        var allInputCells = this.template.querySelectorAll('c-cell[data-input-cell="true"]');

        if(!this.isRowEmpty(allInputCells)) {
            var quantityCell = null, revenueCell = null;

            [...allInputCells].forEach((cell) => {
                cell.resetCellValidity();

                // get quantity and revenue fields to look at later
                if(cell.fieldName === QUANTITY_FIELD) {
                    quantityCell = cell;
                }

                if(cell.fieldName === REVENUE_FIELD) {
                    revenueCell = cell;
                }

                // set errors for required fields
                if(cell.isRequired && !cell.value) {
                    cell.setCellError(cell.fieldLabel + ' ' + this.labels.isRequiredError);
                }
            });

            if(this.schedType === QUANTITY_SCHEDULE && !quantityCell.value) {
                quantityCell.setCellError(this.labels.quantityRevenueError);

            } else if(this.schedType === REVENUE_SCHEDULE && !revenueCell.value) {
                revenueCell.setCellError(this.labels.quantityRevenueError);

            } else if(this.schedType === BOTH_SCHEDULE && (!quantityCell.value && !revenueCell.value)) {
                quantityCell.setCellError(this.labels.quantityRevenueError);
                revenueCell.setCellError(this.labels.quantityRevenueError);
            }

            allInputCells.forEach((cell) => {
                isRowValid = !cell.validateCell() ? false : isRowValid;
            });
        }
        return isRowValid;
    }

    /**
     * Description:
     * resets the row validity
     * by selecting input cells
     * and calling resetCellValidity()
     */
    @api
    resetRowValidity() {
        var allInputCells = this.template.querySelectorAll('c-cell[data-input-cell="true"]');

        [...allInputCells].forEach((cell) => {
            cell.resetCellValidity();
        });
    }

    /**
     * Description:
     * finds all the cells in the row
     * and closes the currently open edit
     * modal
     */
    @api
    clearEditModals() {
        var allInputCells = this.template.querySelectorAll('c-cell[data-input-cell="true"]');

        [...allInputCells].forEach((cell) => {
            cell.handleCloseEditModal();
        });
    }

    /**
     * Description:
     * handles bubbling a cell change event up to
     * the parent container component
     * @param event
     */
    handleFieldCellChange(event) {
        console.log('row:handleFieldCellChange');
        this.handleFieldRecalculation(event);
        this.dispatchEvent(new CustomEvent(CELL_CHANGE_EVT, event));
    }

    /**
     * Description:
     * handles the sales price behavior
     * field calculations based on selected
     * behavior
     * @param event
     */
    handleFieldRecalculation(event) {
        if(this.schedType === BOTH_SCHEDULE) {
            if(this.isRelated) {
                if (event.detail.field === REVENUE_FIELD) {
                    var qtyValue = parseFloat((event.detail.value / this.unitPrice).toFixed(2));
                    this.dispatchFieldCalcEvent(event, QUANTITY_FIELD, qtyValue);
                }

                if (event.detail.field === QUANTITY_FIELD) {
                    var revValue = parseFloat((event.detail.value * this.unitPrice).toFixed(2));
                    this.dispatchFieldCalcEvent(event, REVENUE_FIELD, revValue);
                }
            }
        }
    }

    /**
     * Description:
     * helper function to dispatch the field
     * update recalculation event
     * @param event
     * @param field
     * @param value
     */
    dispatchFieldCalcEvent(event, field, value) {
        this.dispatchEvent(new CustomEvent(CELL_CHANGE_EVT, {
            detail : {
                ...event.detail,
                field : field,
                value : value
            }
        }));
    }

    /**
     * Description:
     * determines if a row is empty by looking
     * at the value of all of it's cells
     * @param allInputCells
     * @returns Boolean
     */
    isRowEmpty(allInputCells) {
        return [...allInputCells].reduce((validSoFar, cell) => {
            return validSoFar && !cell.value;
        }, true);
    }
}