<!--
  ~ Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
  ~
  ~ The code below is part of the Foglight Arts & Culture package and is not
  ~ to be redistributed without express written consent by Foglight.
  -->

<!--
 - Created by <PERSON><PERSON> on 10/4/2019.
 -->

<!-- Row -->
<template>
    <template if:true={rowData}>
        <template iterator:column={rowData}>
            <td key={column.value.field}
                role="gridcell"
                data-label={column.value.columnData.label}
                class="cell-padding">

                <c-cell cell-data-in={column.value}
                        selected-rows={selectedRows}
                        oncellchange={handleFieldCellChange}>
                </c-cell>
            </td>
        </template>
        <!--<td></td>-->
    </template>
</template>