/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by reis<PERSON><PERSON> on 1/14/20.
 */

import { LightningElement, track, api } from 'lwc';
export default class ComboboxMultiItem extends LightningElement {
    @api item;
    @track showResult = true;

    /**
     * Description:
     * used to hide and show the given
     * option using the showResult attribute
     * @param value
     */
    @api
    setShowResult(value) {
        this.showResult = value;
    }

    /**
     * Description:
     * returns if the result is shown
     * or not
     * @returns {boolean}
     */
    @api
    getShowResult() {
        return this.showResult;
    }

    /**
     * Description:
     * fires an event when an item is selected
     * @param event
     */
    onItemSelected (event) {
        this.dispatchEvent(new CustomEvent ('optionselect', {
            detail : {
                item :this.item,
                selected : !this.item.selected
            }
        }));
        event.stopPropagation();
    }

    /**
     * Description:
     * returns class dynamically to either
     * mark item as selected or not depending
     * on the selected attribute of the current item
     * @returns {string}
     */
    get itemClass () {
        return 'slds-listbox__item ms-list-item' + (this.item.selected ? ' slds-is-selected' : '');
    }
}