/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * flowLauncher
 * @description:
 * @author: <PERSON><PERSON>
 * @date: 11/7/24
 */
import {LightningElement, api} from 'lwc';
import launchFlow from '@salesforce/apex/FlowLauncherController.launchFlow'
import { ShowToastEvent } from "lightning/platformShowToastEvent";

export default class FlowLauncher extends LightningElement {
    _recordId;
    _handlerClass;
    _params;
    _successMessage;
    _failureMessage;

    @api
    get recordId() {
        return this._recordId;
    }

    set recordId(recordId) {
        if (recordId !== this._recordId) {
            this._recordId = recordId;
        }
    }

    @api invoke() {
        let payload = JSON.stringify(
            {
                handlerClass: this._handlerClass,
                recordId: this._recordId,
                params: this._params
            }
        )

        launchFlow({
            payload: payload
        })
        .then(result => {
            this.dispatchEvent(
                new ShowToastEvent({
                    title: "Success",
                    variant: "success",
                    message: this._successMessage,
                })
            );
        })
        .catch(error => {
            this.dispatchEvent(
                new ShowToastEvent({
                    title: "Error",
                    variant: 'error',
                    message: error.body.message || this._failureMessage,
                })
            );
        });
    }
}