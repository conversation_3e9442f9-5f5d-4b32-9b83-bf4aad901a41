/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by reis<PERSON><PERSON> on 1/14/20.
 */

import { LightningElement, track, api } from 'lwc';
import { LOOKUP_SELECT_EVT }  from "c/utility";

export default class LookupResultsListItem extends LightningElement {

    @api result;

    /**
     * Description:
     * handles getting a selection from the
     * results list and publishing an event
     * for the parent to handle
     * @param event
     */
    handleResultClick(event) {
        event.preventDefault();

        this.dispatchEvent(new CustomEvent(
            LOOKUP_SELECT_EVT,
            {
                detail: this.result,
                bubbles: true,
                composed: true
            }
        ));
    }
}