/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 10/8/24.
 */

@IsTest
private class HouseholdNamingTest {
    @IsTest
    static void getFieldsFromSpec() {
        Set<String> fields = HouseholdNaming.setStrFieldFromStrFSpec('{!{!Salutation} {!FirstName} {!MiddleName}} {!LastName} Household');
        System.debug(JSON.serializePretty(fields));

        System.Assert.areEqual(4, fields.size());
        System.Assert.isTrue(fields.contains('Salutation'));
        System.Assert.isTrue(fields.contains('FirstName'));
        System.Assert.isTrue(fields.contains('MiddleName'));
        System.Assert.isTrue(fields.contains('LastName'));
        System.Assert.isFalse(fields.contains('Household'));
    }

    @IsTest
    static void testNameMerging() {
        HouseholdNaming.NameSpec hnns = new HouseholdNaming.NameSpec('{!{!Salutation} {!FirstName} {!MiddleName}} {!LastName} Household', '&', 'Friends', 3, null);

        List<Account> contacts = new List<Account>{
                new Account(Salutation='Dr.',FirstName='Bob',MiddleName='M',LastName='Jones'),
                new Account(Salutation='Mrs.',FirstName='Sally',MiddleName='T',LastName='Jones')
        };

        String name = HouseholdNaming.getName(hnns, contacts);
        System.Assert.areEqual('Dr. Bob M & Mrs. Sally T Jones Household', name);
    }

    @IsTest
    static void testDefaultNameMerging() {
        HouseholdNaming.NameSpec hnns = new HouseholdNaming.NameSpec('{!{!Salutation} {!FirstName} {!MiddleName}} {!LastName} Household', '&', 'Friends', 3, 'Anonymous');

        List<Account> contacts = new List<Account>();

        String name = HouseholdNaming.getName(hnns, contacts);
        System.Assert.areEqual('Anonymous Household', name);
    }

    @IsTest
    static void dml_createPA() {
        Account pa = new Account();
        pa.RecordTypeId = AccountDomain.RT_PERSON_ACCOUNT;
        pa.Salutation   = 'Mr.';
        pa.FirstName    = 'Bob';
        pa.LastName     = 'Jones';
        Test.startTest();
        insert pa;
        Test.stopTest();

        pa = [
                SELECT Id, PersonContactId,
                        flmas__PrimaryHousehold__pr.Id,
                        flmas__PrimaryHousehold__pr.Name,
                        flmas__PrimaryHousehold__pr.FormalGreeting__c,
                        flmas__PrimaryHousehold__pr.InformalGreeting__c,
                        flmas__PrimaryHousehold__pr.PrimaryContact__c
                FROM Account
                WHERE Id = :pa.Id
        ];

        System.Assert.areNotEqual(null, pa.flmas__PrimaryHousehold__pr, 'The PA should have a household');
        System.Assert.areEqual('Bob Jones Household', pa.flmas__PrimaryHousehold__pr.Name, 'The Household should have a name');
        System.Assert.areEqual('Mr. Bob Jones Household', pa.flmas__PrimaryHousehold__pr.FormalGreeting__c, 'The Household should have a Formal Greeting');
        System.Assert.areEqual('Bob Jones Household', pa.flmas__PrimaryHousehold__pr.InformalGreeting__c, 'The Household should have an Informal Greeting');
        System.Assert.areNotEqual(null, pa.PersonContactId, 'The PA should have a Contact Id');
        System.Assert.areEqual(pa.PersonContactId, pa.flmas__PrimaryHousehold__pr.PrimaryContact__c, 'The Household\'s Primary Contact should be the Person Account');

        // Set override fields
        Account hh = new Account();
        hh.Id = pa.flmas__PrimaryHousehold__pr.Id;
        hh.OverrideInformalGreeting__c = true;
        hh.InformalGreeting__c = 'Informal Override';
        update hh;

        // add a new PA to the household
        Account pa2 = new Account();
        pa2.RecordTypeId = AccountDomain.RT_PERSON_ACCOUNT;
        pa2.Salutation   = 'Mrs.';
        pa2.FirstName    = 'Sally';
        pa2.LastName     = 'Jones';
        pa2.flmas__PrimaryHousehold__pc = pa.flmas__PrimaryHousehold__pr.Id;
        insert pa2;

        pa2 = [
                SELECT Id, PersonContactId,
                        flmas__PrimaryHousehold__pr.Id,
                        flmas__PrimaryHousehold__pr.Name,
                        flmas__PrimaryHousehold__pr.FormalGreeting__c,
                        flmas__PrimaryHousehold__pr.InformalGreeting__c,
                        flmas__PrimaryHousehold__pr.PrimaryContact__c
                FROM Account
                WHERE Id = :pa2.Id
        ];
        System.Assert.areNotEqual(null, pa2.flmas__PrimaryHousehold__pr, 'The PA should have a household');
        System.Assert.areEqual('Bob & Sally Jones Household', pa2.flmas__PrimaryHousehold__pr.Name, 'The Household should have a name');
        System.Assert.areEqual('Mr. Bob & Mrs. Sally Jones Household', pa2.flmas__PrimaryHousehold__pr.FormalGreeting__c, 'The Household should have a Formal Greeting');
        System.Assert.areEqual(hh.InformalGreeting__c, pa2.flmas__PrimaryHousehold__pr.InformalGreeting__c, 'The Household should have an Informal Greeting that is overridden');
        System.Assert.areNotEqual(null, pa2.PersonContactId, 'The Person Account should have a contact Id.');

        // rename a household member
        pa2.FirstName = 'Janet';
        update pa2;

        pa2 = [
                SELECT Id, PersonContactId,
                        flmas__PrimaryHousehold__pr.Id,
                        flmas__PrimaryHousehold__pr.Name,
                        flmas__PrimaryHousehold__pr.FormalGreeting__c,
                        flmas__PrimaryHousehold__pr.InformalGreeting__c,
                        flmas__PrimaryHousehold__pr.PrimaryContact__c
                FROM Account
                WHERE Id = :pa2.Id
        ];
        System.Assert.areNotEqual(null, pa2.flmas__PrimaryHousehold__pr, 'The PA should have a household');
        System.Assert.areEqual('Bob & Janet Jones Household', pa2.flmas__PrimaryHousehold__pr.Name, 'The Household should have a name');
        System.Assert.areEqual('Mr. Bob & Mrs. Janet Jones Household', pa2.flmas__PrimaryHousehold__pr.FormalGreeting__c, 'The Household should have a Formal Greeting');
        System.Assert.areEqual(hh.InformalGreeting__c, pa2.flmas__PrimaryHousehold__pr.InformalGreeting__c, 'The Household should have an Informal Greeting that is overridden');

        // Remove the override
        hh.OverrideInformalGreeting__c = false;
        update hh;

        hh = [SELECT Id, InformalGreeting__c FROM Account WHERE Id = :hh.Id];
        System.Assert.areEqual('Bob & Janet Jones Household', hh.InformalGreeting__c, 'The Household should have a non-overridden informal greeting');

        // Change the 2nd PA's HH Id
        pa2.flmas__PrimaryHousehold__pc = null;
        update pa2;

        hh = [
                SELECT Id, Name, FormalGreeting__c, InformalGreeting__c
                FROM Account
                WHERE Id = :hh.Id
        ];
        System.Assert.areEqual('Bob Jones Household', hh.Name, 'The Household should have a non-overridden name');
        System.Assert.areEqual('Mr. Bob Jones Household', hh.FormalGreeting__c, 'The Household should have a non-overridden formal greeting');
        System.Assert.areEqual('Bob Jones Household', hh.InformalGreeting__c, 'The Household should have a non-overridden informal greeting');
    }

    @IsTest
    static void testHHOrder() {
        Account pa = new Account();
        pa.RecordTypeId = AccountDomain.RT_PERSON_ACCOUNT;
        pa.Salutation   = 'Mr.';
        pa.FirstName    = 'Bob';
        pa.LastName     = 'Jones';
        insert pa;

        pa = [
            SELECT Id, PersonContactId,
            flmas__PrimaryHousehold__pr.Id,
            flmas__PrimaryHousehold__pr.Name,
            flmas__PrimaryHousehold__pr.FormalGreeting__c,
            flmas__PrimaryHousehold__pr.InformalGreeting__c,
            flmas__PrimaryHousehold__pr.PrimaryContact__c
            FROM Account
            WHERE Id = :pa.Id
        ];

        Account pa2 = new Account();
        pa2.RecordTypeId = AccountDomain.RT_PERSON_ACCOUNT;
        pa2.Salutation   = 'Mrs.';
        pa2.FirstName    = 'Sally';
        pa2.LastName     = 'Jones';
        pa2.flmas__PrimaryHousehold__pc = pa.flmas__PrimaryHousehold__pr.Id;
        insert pa2;

        pa2 = [
                SELECT Id, PersonContactId,
                        flmas__PrimaryHousehold__pr.Id,
                        flmas__PrimaryHousehold__pr.Name,
                        flmas__PrimaryHousehold__pr.FormalGreeting__c,
                        flmas__PrimaryHousehold__pr.InformalGreeting__c,
                        flmas__PrimaryHousehold__pr.PrimaryContact__c
                FROM Account
                WHERE Id = :pa2.Id
        ];

        Account pa3 = new Account();
        pa3.RecordTypeId = AccountDomain.RT_PERSON_ACCOUNT;
        pa3.FirstName    = 'Jimmy';
        pa3.LastName     = 'Jones';
        pa3.flmas__PrimaryHousehold__pc = pa.flmas__PrimaryHousehold__pr.Id;
        insert pa3;

        Account household = new Account();
        household.Id = pa.flmas__PrimaryHousehold__pr.Id;
        household.PrimaryContact__c = pa2.PersonContactId;
        household.SecondaryContact__c   = pa.PersonContactId;
        System.debug('Household: ' + household);
        Test.startTest();
        update household;
        Test.stopTest();

        household = [
                SELECT Id, Name, FormalGreeting__c, InformalGreeting__c
                FROM Account
                WHERE Id = :household.Id
        ];
        System.Assert.areEqual('Sally & Bob Jones & Friends Household', household.Name, 'The Household should have a non-overridden name');
        System.Assert.areEqual('Mrs. Sally & Mr. Bob Jones Household', household.FormalGreeting__c, 'The Household should have a non-overridden formal greeting');
        System.Assert.areEqual('Sally & Bob Jones Household', household.InformalGreeting__c, 'The Household should have a non-overridden informal greeting');
    }

    @IsTest
    static void overrun_1() {
        HouseholdNaming.NameSpec ns1 = new HouseholdNaming.NameSpec(
                '{!{!FirstName}} Household',
                'and',
                'Friends',
                2,
                'Anonymous'
        );

        String name = HouseholdNaming.getName(ns1, new List<Account>{
                new Account(FirstName = 'Test1'),
                new Account(FirstName = 'Test2'),
                new Account(FirstName = 'Test3')
        });

        System.Assert.areEqual('Test1, Test2 and Friends Household', name);
    }

    @IsTest
    static void overrun_2() {
        HouseholdNaming.NameSpec ns2 = new HouseholdNaming.NameSpec(
                '{!{!FirstName}}',
                'and',
                'Friends',
                2,
                'Anonymous'
        );

        String name2 = HouseholdNaming.getName(ns2, new List<Account>{
                new Account(FirstName = 'Test1'),
                new Account(FirstName = 'Test2'),
                new Account(FirstName = 'Test3')
        });

        System.Assert.areEqual('Test1, Test2 and Friends', name2);
    }

    @IsTest
    static void overrun_3() {
        HouseholdNaming.NameSpec ns3 = new HouseholdNaming.NameSpec(
                '{!{!FirstName}}',
                'and',
                null,
                2,
                'Anonymous'
        );

        String name3 = HouseholdNaming.getName(ns3, new List<Account>{
                new Account(FirstName = 'Test1'),
                new Account(FirstName = 'Test2'),
                new Account(FirstName = 'Test3')
        });

        System.Assert.areEqual('Test1 and Test2', name3);
    }

    @IsTest
    static void dml_deletePA() {
        Account pa = new Account();
        pa.RecordTypeId = AccountDomain.RT_PERSON_ACCOUNT;
        pa.Salutation = 'Mr.';
        pa.FirstName = 'Bob';
        pa.LastName = 'Jones';
        insert pa;

        Test.startTest();
        delete pa; //
        Test.stopTest();
    }
}