/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

@isTest
public class ContactPointPhoneTriggerTest {
  //@isTest
  static void testCopiesPhoneBusinessAccount() {
    Account acc = new Account();
    acc.RecordTypeId = SObjectUtils.getRecordTypeIdForObjectAndName(
      Account.SObjectType,
      'Business_Account'
    );
    acc.Name = 'Test';
    insert acc;

    ContactPointPhone cpp = new ContactPointPhone();
    cpp.IsPrimary = true;
    cpp.TelephoneNumber = '**********';
    cpp.ParentId = acc.Id;
    cpp.PhoneType__c = 'Phone';
    insert cpp;

    acc = [SELECT Phone FROM Account WHERE Id = :acc.Id];
    System.Assert.areEqual(
      cpp.TelephoneNumber,
      acc.Phone,
      'Did not copy phone number correctly'
    );

    cpp.TelephoneNumber = '**********';
    update cpp;

    acc = [SELECT Phone FROM Account WHERE Id = :acc.Id];
    System.Assert.areEqual(
      cpp.TelephoneNumber,
      acc.Phone,
      'Did not copy phone number correctly on update'
    );
    System.Assert.areEqual(
      1,
      [SELECT Id FROM ContactPointPhone].size(),
      'Made duplicate CPP'
    );
  }

  //@isTest
  static void testCopiesPhoneBusinessAccount_markingPrimary() {
    Account acc = new Account();
    acc.RecordTypeId = SObjectUtils.getRecordTypeIdForObjectAndName(
      Account.SObjectType,
      'Business_Account'
    );
    acc.Name = 'Test';
    insert acc;

    ContactPointPhone cpp = new ContactPointPhone();
    cpp.IsPrimary = false;
    cpp.TelephoneNumber = '**********';
    cpp.ParentId = acc.Id;
    cpp.PhoneType__c = 'Phone';
    insert cpp;

    acc = [SELECT Phone FROM Account WHERE Id = :acc.Id];
    System.Assert.isNull(acc.Phone, 'Should not have copied phone number');

    cpp.IsPrimary = true;
    update cpp;

    acc = [SELECT Phone FROM Account WHERE Id = :acc.Id];
    System.Assert.areEqual(
      cpp.TelephoneNumber,
      acc.Phone,
      'Did not copy phone number correctly on update'
    );
    System.Assert.areEqual(
      1,
      [SELECT Id FROM ContactPointPhone].size(),
      'Made duplicate CPP'
    );
  }
  //@isTest
  static void testErrorsPersonPhoneBusinessAccount() {
    Account acc = new Account();
    acc.RecordTypeId = SObjectUtils.getRecordTypeIdForObjectAndName(
      Account.SObjectType,
      'Business_Account'
    );
    acc.Name = 'Test';
    insert acc;

    ContactPointPhone cpp = new ContactPointPhone();
    cpp.IsPrimary = true;
    cpp.TelephoneNumber = '**********';
    cpp.ParentId = acc.Id;
    cpp.PhoneType__c = 'PersonHomePhone';
    try {
      insert cpp;
      System.Assert.isFalse(true, 'Should have thrown exception');
    } catch (System.DmlException e) {
      System.Assert(
        e.getMessage().contains('FIELD_CUSTOM_VALIDATION_EXCEPTION'),
        'Wrong assertion message'
      );
    }
  }

  //@isTest
  static void testPersonPhonePersonAccount() {
    Account acc = AccountTest.createPersonAccount();

    ContactPointPhone cpp = new ContactPointPhone();
    cpp.IsPrimary = true;
    cpp.TelephoneNumber = '**********';
    cpp.ParentId = acc.Id;
    cpp.PhoneType__c = 'PersonHomePhone';
    insert cpp;

    acc = [SELECT PersonHomePhone FROM Account WHERE Id = :acc.Id];
    System.Assert.areEqual(
      cpp.TelephoneNumber,
      acc.PersonHomePhone,
      'Did not copy phone number correctly'
    );
    System.Assert.areEqual(
      1,
      [SELECT Id FROM ContactPointPhone].size(),
      'Made duplicate CPP'
    );
  }

  //@isTest
  static void testUpdatesOldCPPToNotPrimary() {
    Account acc = new Account();
    acc.RecordTypeId = SObjectUtils.getRecordTypeIdForObjectAndName(
      Account.SObjectType,
      'Business_Account'
    );
    acc.Name = 'Test';
    insert acc;

    ContactPointPhone cpp = new ContactPointPhone();
    cpp.IsPrimary = true;
    cpp.TelephoneNumber = '**********';
    cpp.ParentId = acc.Id;
    cpp.PhoneType__c = 'Phone';
    insert cpp;

    ContactPointPhone cpp2 = new ContactPointPhone();
    cpp2.IsPrimary = true;
    cpp2.TelephoneNumber = '**********';
    cpp2.ParentId = acc.Id;
    cpp2.PhoneType__c = 'Phone';
    insert cpp2;

    cpp = [SELECT IsPrimary FROM ContactPointPhone WHERE Id = :cpp.Id];
    System.Assert.isFalse(
      cpp.IsPrimary,
      'Should have updated old CPP Primary to false'
    );
    System.assert.areEqual(
      1,
      [SELECT Id FROM ContactPointPhone WHERE IsPrimary = TRUE].size(),
      'Should be 1 primary cpp'
    );

    acc = [SELECT Phone FROM Account WHERE Id = :acc.Id];
    System.Assert.areEqual(
      cpp2.TelephoneNumber,
      acc.Phone,
      'Did not copy phone number correctly on update'
    );

    cpp.IsPrimary = true;
    update cpp;
    cpp2 = [SELECT IsPrimary FROM ContactPointPhone WHERE Id = :cpp2.Id];
    System.Assert.isFalse(
      cpp2.IsPrimary,
      'Should have updated old CPP Primary to false'
    );
    System.assert.areEqual(
      1,
      [SELECT Id FROM ContactPointPhone WHERE IsPrimary = TRUE].size(),
      'Should be 1 primary cpp'
    );
  }

  //@isTest
  static void testUpdateHandlesMultipleFields() {
    Account acc = AccountTest.createPersonAccount();

    ContactPointPhone cppPersonal = new ContactPointPhone();
    cppPersonal.IsPrimary = true;
    cppPersonal.TelephoneNumber = '**********';
    cppPersonal.ParentId = acc.Id;
    cppPersonal.PhoneType__c = 'PersonHomePhone';
    insert cppPersonal;

    ContactPointPhone cppPhone = new ContactPointPhone();
    cppPhone.IsPrimary = true;
    cppPhone.TelephoneNumber = '**********';
    cppPhone.ParentId = acc.Id;
    cppPhone.PhoneType__c = 'Phone';
    insert cppPhone;

    ContactPointPhone cppNewPeronal = new ContactPointPhone();
    cppNewPeronal.IsPrimary = true;
    cppNewPeronal.TelephoneNumber = '**********';
    cppNewPeronal.ParentId = acc.Id;
    cppNewPeronal.PhoneType__c = 'PersonHomePhone';
    insert cppNewPeronal;

    cppPersonal = [
      SELECT IsPrimary
      FROM ContactPointPhone
      WHERE Id = :cppPersonal.Id
    ];
    System.Assert.isFalse(
      cppPersonal.IsPrimary,
      'Should have updated old CPP Primary to false'
    );
    System.assert.areEqual(
      2,
      [SELECT Id FROM ContactPointPhone WHERE IsPrimary = TRUE].size(),
      'Should be 2 primary cpps'
    );

    acc = [SELECT Phone, PersonHomePhone FROM Account WHERE Id = :acc.Id];
    System.Assert.areEqual(
      cppNewPeronal.TelephoneNumber,
      acc.PersonHomePhone,
      'Did not copy phone number correctly'
    );
    System.Assert.areEqual(
      cppPhone.TelephoneNumber,
      acc.Phone,
      'Did not copy phone number correctly'
    );
  }

  // @isTest
  // static void testPersonPhonePersonAccount_custom() {
  //   Account acc = AccountTest.createPersonAccount();

  //   ContactPointPhone cpp = new ContactPointPhone();
  //   cpp.IsPrimary = true;
  //   cpp.TelephoneNumber = '**********';
  //   cpp.ParentId = acc.Id;
  //   cpp.PhoneType__c = 'TestConPhone__pc';
  //   insert cpp;

  //   acc = [SELECT TestConPhone__pc FROM Account WHERE Id = :acc.Id];
  //   System.Assert.areEqual(
  //     cpp.TelephoneNumber,
  //     acc.TestConPhone__pc,
  //     'Did not copy phone number correctly'
  //   );
  //   System.Assert.areEqual(
  //     1,
  //     [SELECT Id FROM ContactPointPhone].size(),
  //     'Made duplicate CPP'
  //   );
  // }
}
