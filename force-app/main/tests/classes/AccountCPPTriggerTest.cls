/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

@isTest
public with sharing class AccountCPPTriggerTest {
  //@isTest
  public static void testNewAccountCreatesCPP() {
    Account acc = new Account();
    acc.RecordTypeId = SObjectUtils.getRecordTypeIdForObjectAndName(
      Account.SObjectType,
      'Business_Account'
    );
    acc.Name = 'Test';
    acc.Phone = '**********';
    insert acc;

    List<ContactPointPhone> cpps = [
      SELECT Id, IsPrimary, TelephoneNumber, PhoneType__c
      FROM ContactPointPhone
      WHERE ParentId = :acc.Id
    ];
    System.Assert.areEqual(
      1,
      cpps.size(),
      'Should have created 1 ContactPointPhone'
    );
    ContactPointPhone cpp = cpps[0];
    System.Assert.areEqual(acc.Phone, cpp.TelephoneNumber, 'Wrong number');
    System.Assert.areEqual('Phone', cpp.PhoneType__c, 'Wrong PhoneType__c');
    System.Assert.isTrue(cpp.IsPrimary, 'Should be primary phone number');
    delete cpp;

    acc.Phone = '**********';
    update acc;

    cpps = [
      SELECT Id, IsPrimary, TelephoneNumber, PhoneType__c
      FROM ContactPointPhone
      WHERE ParentId = :acc.Id
    ];
    System.Assert.areEqual(
      1,
      cpps.size(),
      'Should have created 1 ContactPointPhone'
    );
    cpp = cpps[0];
    System.Assert.areEqual(acc.Phone, cpp.TelephoneNumber, 'Wrong number');
    System.Assert.areEqual('Phone', cpp.PhoneType__c, 'Wrong PhoneType__c');
    System.Assert.isTrue(cpp.IsPrimary, 'Should be primary phone number');
  }

  //@isTest
  public static void testNewPersonAccountCreatesCPP() {
    Account acc = AccountTest.buildPersonAccount();
    acc.PersonHomePhone = '**********';
    acc.Phone = '**********';
    insert acc;

    List<ContactPointPhone> cpps = [
      SELECT Id, IsPrimary, TelephoneNumber, PhoneType__c
      FROM ContactPointPhone
      WHERE ParentId = :acc.Id
    ];
    System.Assert.areEqual(
      2,
      cpps.size(),
      'Should have created 2 ContactPointPhones'
    );
    for (ContactPointPhone cpp : cpps) {
      if (cpp.PhoneType__c == 'Personhomephone') {
        System.Assert.areEqual(
          acc.PersonHomePhone,
          cpp.TelephoneNumber,
          'Wrong number'
        );
        System.Assert.isTrue(cpp.IsPrimary, 'Should be primary phone number');
      } else if (cpp.PhoneType__c == 'Phone') {
        System.Assert.areEqual(acc.Phone, cpp.TelephoneNumber, 'Wrong number');
        System.Assert.isTrue(cpp.IsPrimary, 'Should be primary phone number');
      } else {
        System.Assert.isFalse(true, 'Unknown Phone Type ' + cpp.PhoneType__c);
      }
    }
  }

  // @isTest
  // public static void testNewPersonAccountCreatesCPP_custom() {
  //   Account acc = AccountTest.buildPersonAccount();
  //   acc.TestPhone__c = '**********';
  //   acc.TestConPhone__pc = '**********';
  //   insert acc;

  //   List<ContactPointPhone> cpps = [
  //     SELECT Id, IsPrimary, TelephoneNumber, PhoneType__c
  //     FROM ContactPointPhone
  //     WHERE ParentId = :acc.Id
  //   ];
  //   System.Assert.areEqual(
  //     2,
  //     cpps.size(),
  //     'Should have created 1 ContactPointPhone'
  //   );
  //   for (ContactPointPhone cpp : cpps) {
  //     if (cpp.PhoneType__c.endsWith('testphone__c')) {
  //       System.Assert.areEqual(
  //         acc.TestPhone__c,
  //         cpp.TelephoneNumber,
  //         'Wrong number'
  //       );
  //       System.Assert.isTrue(cpp.IsPrimary, 'Should be primary phone number');
  //     } else if (cpp.PhoneType__c.endsWith('testconphone__pc')) {
  //       System.Assert.areEqual(
  //         acc.TestConPhone__pc,
  //         cpp.TelephoneNumber,
  //         'Wrong number'
  //       );
  //       System.Assert.isTrue(cpp.IsPrimary, 'Should be primary phone number');
  //     } else {
  //       System.Assert.isFalse(true, 'Unknown Phone Type ' + cpp.PhoneType__c);
  //     }
  //   }
  // }
}
