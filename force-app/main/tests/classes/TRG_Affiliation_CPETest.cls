/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 5/13/25.
 */

@IsTest
public with sharing class TRG_Affiliation_CPETest {

    @IsTest
    static void testValidation() {
        // Create a PA and org
        Account pa = AccountTest.buildPersonAccount();
        Account org = new Account();
        org.Name = 'Test Org';
        org.RecordTypeId = AccountDomain.RT_ORG;

        insert new List<Account>{pa,org}; // This also creates a CPE for <PERSON> since his account has an email on it.

        pa = [SELECT Id, PersonContactId FROM Account WHERE Id = :pa.Id];


        // Create two CPEs, a valid one and an invalid one
        ContactPointEmail cpeValid = new ContactPointEmail();
        cpeValid.ParentId   = pa.Id;
        cpeValid.EmailAddress   = '<EMAIL>';

        ContactPointEmail cpeInvalid = new ContactPointEmail();
        cpeInvalid.ParentId   = org.Id;
        cpeInvalid.EmailAddress   = '<EMAIL>';

        insert new List<ContactPointEmail>{cpeValid,cpeInvalid};

        // Now create an invalid and valid Affiliation
        Affiliation__c validAffiliation         = new Affiliation__c();
        validAffiliation.Contact__c             = pa.PersonContactId;
        validAffiliation.Organization__c        = org.Id;
        validAffiliation.ContactPointEmail__c   = cpeValid.Id;

        Affiliation__c invalidAffiliation         = new Affiliation__c();
        invalidAffiliation.Contact__c             = pa.PersonContactId;
        invalidAffiliation.Organization__c        = org.Id;
        invalidAffiliation.ContactPointEmail__c   = cpeInvalid.Id;

        List<Database.SaveResult> dsrs = Database.insert(new List<Affiliation__c>{validAffiliation,invalidAffiliation}, false);

        System.Assert.areEqual(true, dsrs.get(0).success);
        System.Assert.areEqual(false, dsrs.get(1).success);
        System.Assert.areEqual(TRG_Affiliation_CPE.ERROR_MSG, dsrs.get(1).getErrors().get(0).message);
    }
}