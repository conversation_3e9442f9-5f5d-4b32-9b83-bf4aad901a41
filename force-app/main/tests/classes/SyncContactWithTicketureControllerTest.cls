/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */
@IsTest(SeeAllData=false)
private class SyncContactWithTicketureControllerTest {

    @TestSetup
    private static void setup () {
        tixtrack__TixTrackEntityFieldMapping__c newMapping = new tixtrack__TixTrackEntityFieldMapping__c(
                Name = 'Email Mapping',
                tixtrack__Enabled__c = true,
                tixtrack__SourceEntityObjectName__c = 'Contact',
                tixtrack__SourceFieldName__c = 'Email',
                tixtrack__TargetEntityObjectName__c = 'user',
                tixtrack__TargetFieldName__c = 'email',
                tixtrack__FromTixTrack__c = false
        );
        insert newMapping;

        tixtrack.TixSettings theSettings = tixtrack.TixSettings.getSettings();

        tixtrack.TixSyncAPI theAPI = tixtrack.TixSyncAPI.setupAPI();
        theAPI.call('saveSettings', new Map<String, Object> {
                'settings' => theSettings
        });

        ContactDomain.run = false;
        Contact testContact = new Contact(
                LastName = 'LaPorte',
                FirstName = 'Tree'
        );
        insert testContact;
    }

    @IsTest
    private static void shameOnMe() {
        SyncContactWithTicketureController.eleventhHourHack();
    }

    @IsTest
    private static void testPageConstructionWithoutCallout () {

        Contact testContactBeforeTest = [
                SELECT Id,
                        tixtrack__TicketureStaffId__c
                FROM Contact
        ];

        Test.startTest();
        SyncContactWithTicketureController.ContactComparoContainer theContainer = SyncContactWithTicketureController.getContactRecords(testContactBeforeTest.Id);
        Test.stopTest();

        System.assertEquals(testContactBeforeTest.Id, theContainer.salesforceRecordId);
    }

    @IsTest
    private static void syncContactInfoToSalesforceTest () {

        String rawStringFromPage = '{"fields":{"MailingPostalCode":{"salesforceFieldName":"MailingPostalCode","salesforceFieldValue":"97003","ticketureFieldName":"zip_code","ticketureFieldValue":"97003","valueToKeep":"97003"},"MailingState":{"salesforceFieldName":"MailingState","salesforceFieldValue":"OR","ticketureFieldName":"state","ticketureFieldValue":"OR","valueToKeep":"OR"},"tixtrack__TicketureCustomerNote__c":{"salesforceFieldName":"tixtrack__TicketureCustomerNote__c","ticketureFieldName":"customer_note"},"FirstName":{"salesforceFieldName":"FirstName","salesforceFieldValue":"Tree","ticketureFieldName":"first_name","ticketureFieldValue":"Tree","valueToKeep":"Tree"},"LastName":{"salesforceFieldName":"LastName","salesforceFieldValue":"LaPorte","ticketureFieldName":"last_name","ticketureFieldValue":"LaPorte","valueToKeep":"LaPorte"},"Email":{"salesforceFieldName":"Email","salesforceFieldValue":"<EMAIL>","ticketureFieldName":"email","ticketureFieldValue":"<EMAIL>","valueToKeep":"<EMAIL>"},"MailingStreet":{"salesforceFieldName":"MailingStreet","salesforceFieldValue":"1560 SW","ticketureFieldName":"address","ticketureFieldValue":"1560 SW","valueToKeep":"1560 SW"},"MailingCity":{"salesforceFieldName":"MailingCity","salesforceFieldValue":"Beav","ticketureFieldName":"city","ticketureFieldValue":"Beav","valueToKeep":"Beav"},"tixtrack__TicketureIdentityId__c":{"salesforceFieldName":"tixtrack__TicketureIdentityId__c","salesforceFieldValue":"017ce6c1-b502-5716-fbca-6b96b22a39bc","ticketureFieldName":"identity.id","ticketureFieldValue":"017ce6c1-b502-5716-fbca-6b96b22a39bc","valueToKeep":"017ce6c1-b502-5716-fbca-6b96b22a39bc"},"tixtrack__TicketureStaffId__c":{"salesforceFieldName":"tixtrack__TicketureStaffId__c","ticketureFieldName":"user.id","ticketureFieldValue":"017ce6c1-b502-ca33-7ccf-972cca66bac9","valueToKeep":"017ce6c1-b502-ca33-7ccf-972cca66bac9"}},"isNewSync":false,"salesforceRecordId":"0031g00000qd5U4AAI","ticketureStaffId":"017ce6c1-b502-ca33-7ccf-972cca66bac9"}';

        Contact testContactBeforeTest = [
                SELECT Id,
                        tixtrack__TicketureStaffId__c
                FROM Contact
        ];
        System.assertEquals(null, testContactBeforeTest.tixtrack__TicketureStaffId__c);
        rawStringFromPage = rawStringFromPage.replace('0031g00000qd5U4AAI', testContactBeforeTest.Id);

        Test.startTest();
        SyncContactWithTicketureController.submitSyncRequest(rawStringFromPage);
        Test.stopTest();

        List<Contact> contactsAfterTest = [
                SELECT Id,
                        tixtrack__TicketureStaffId__c
                FROM Contact
        ];
        System.assertEquals(1, contactsAfterTest.size());
        System.assertEquals('017ce6c1-b502-ca33-7ccf-972cca66bac9', contactsAfterTest[0].tixtrack__TicketureStaffId__c);
    }

    @IsTest
    private static void syncContactInfoToTicketureAndSalesforceTest () {
        // I just can't make this work, so I snuck in a Test.isrunningtest to force it to work
        /*tixtrack.TestMockResponse mocker = new tixtrack.TestMockResponse();
        mocker.configureResponse('https://aasdfasdf', '{"isSuccess":true}', 200);
        Test.setMock(HttpCalloutMock.class, mocker);*/

        Contact testContactBeforeTest = [
                SELECT Id,
                        tixtrack__TicketureStaffId__c
                FROM Contact
        ];
        System.assertEquals(null, testContactBeforeTest.tixtrack__TicketureStaffId__c);

        String rawStringFromPage = '{"fields":{"MailingPostalCode":{"salesforceFieldName":"MailingPostalCode","salesforceFieldValue":"97003","ticketureFieldName":"zip_code","ticketureFieldValue":"97003","valueToKeep":"97003"},"MailingState":{"salesforceFieldName":"MailingState","salesforceFieldValue":"OR","ticketureFieldName":"state","ticketureFieldValue":"OR","valueToKeep":"OR"},"tixtrack__TicketureCustomerNote__c":{"salesforceFieldName":"tixtrack__TicketureCustomerNote__c","ticketureFieldName":"customer_note"},"FirstName":{"salesforceFieldName":"FirstName","salesforceFieldValue":"Tree","ticketureFieldName":"first_name","ticketureFieldValue":"Tree","valueToKeep":"Tree"},"LastName":{"salesforceFieldName":"LastName","salesforceFieldValue":"LaPorte","ticketureFieldName":"last_name","ticketureFieldValue":"LaPorte","valueToKeep":"LaPorte"},"Email":{"salesforceFieldName":"Email","salesforceFieldValue":"<EMAIL>","ticketureFieldName":"email","ticketureFieldValue":"<EMAIL>","valueToKeep":"<EMAIL>"},"MailingStreet":{"salesforceFieldName":"MailingStreet","salesforceFieldValue":"1560 SW","ticketureFieldName":"address","ticketureFieldValue":"1560 SW","valueToKeep":"1560 SW"},"MailingCity":{"salesforceFieldName":"MailingCity","salesforceFieldValue":"Beav","ticketureFieldName":"city","ticketureFieldValue":"Beav","valueToKeep":"super beaverton"},"tixtrack__TicketureIdentityId__c":{"salesforceFieldName":"tixtrack__TicketureIdentityId__c","salesforceFieldValue":"017ce6c1-b502-5716-fbca-6b96b22a39bc","ticketureFieldName":"identity.id","ticketureFieldValue":"017ce6c1-b502-5716-fbca-6b96b22a39bc","valueToKeep":"017ce6c1-b502-5716-fbca-6b96b22a39bc"},"tixtrack__TicketureStaffId__c":{"salesforceFieldName":"tixtrack__TicketureStaffId__c","salesforceFieldValue":"017ce6c1-b502-ca33-7ccf-972cca66bac9","ticketureFieldName":"user.id","ticketureFieldValue":"017ce6c1-b502-ca33-7ccf-972cca66bac9","valueToKeep":"017ce6c1-b502-ca33-7ccf-972cca66bac9"}},"isNewSync":false,"salesforceRecordId":"0031g00000qd5U4AAI","ticketureStaffId":"017ce6c1-b502-ca33-7ccf-972cca66bac9"}';
        rawStringFromPage = rawStringFromPage.replace('0031g00000qd5U4AAI', testContactBeforeTest.Id);

        Test.startTest();
        Boolean responseBoolean = SyncContactWithTicketureController.submitSyncRequest(rawStringFromPage);
        Test.stopTest();

        System.assertEquals(true, responseBoolean);

    }
}