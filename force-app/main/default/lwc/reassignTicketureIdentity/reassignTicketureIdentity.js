/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by triestelaporte on 2/6/2023.
 */

import {api, LightningElement, track} from "lwc";
import {CloseActionScreenEvent} from "lightning/actions";
import {handleError,showToast} from "c/flModalBase";
import {loadStyle} from "lightning/platformResourceLoader";
import flActionModal from '@salesforce/resourceUrl/flActionModal';
import getPageSetupInfoInApex from "@salesforce/apex/ContactTixReassignmentController.getPageSetupInfo";
import submitCustomMoveRequestInApex from "@salesforce/apex/ContactTixReassignmentController.submitCustomMoveRequest";
import submitSyncRequestInApex from "@salesforce/apex/ContactTixReassignmentController.submitSyncRequest";

export default class CustomMoveContact extends LightningElement {
    _recordId;
    hasRenderedCallback;

    @track sourceContact = {};
    @track potentialTargetContacts = [];
    @track tableColumns = [];
    @track sourceContactFields = [];
    @track completedLoad = false;
    selectedTargetContactId;
    @track showSpinner = false;


    set recordId(value) {
        if (value && this._recordId == null && this.hasRenderedCallback) {
            console.log("Got record id: " + value);
            this._recordId = value;

            let pageSetupParams = {
                "sourceContactId": this._recordId
            };

            // optionally you can put alerts or anything else you like in here
            getPageSetupInfoInApex(pageSetupParams).then(pageSetupResult => {
                console.log('Got Page Setup Information:  ', JSON.parse(JSON.stringify(pageSetupResult)));

                this.sourceContact = pageSetupResult.sourceContact;
                this.potentialTargetContacts = pageSetupResult.potentialTargetContacts;
                this.tableColumns = pageSetupResult.tableColumns;
                this.sourceContactFields = pageSetupResult.sourceContactFields;

                console.log('this.tableColumns:  ', JSON.parse(JSON.stringify(this.tableColumns)));

                // hack to bring down the dot notated household name to the contact level
                // apex put in AccountName as a column to output, but we can't create the 'holder' in apex since it won't compile
                for (const potentialTarget of this.potentialTargetContacts) {
                    potentialTarget.AccountName = potentialTarget.Account.Name;
                }
                // do the same for the source contact
                this.sourceContact.AccountName = this.sourceContact.Account.Name;

                this.completedLoad = true;
            }).catch(
                err => {
                    handleError(err);
                }
            );
        }
    }
    @api get recordId() {
        return this._recordId;
    }

    connectedCallback() {
        Promise.all([
            loadStyle(this, flActionModal)
        ]);
    }

    renderedCallback() {
        if (!this.hasRenderedCallback) {
            this.hasRenderedCallback = true;
        }
    }

    radioSelect(event) {
        const selectedRows = event.detail.selectedRows;
        // Display that fieldName of the selected rows
        for (const potentialTarget of selectedRows) {
            console.log('You selected: ' + potentialTarget.Id);
            this.selectedTargetContactId = potentialTarget.Id;
        }
    }

    submitMoveRequest() {

        this.showSpinner = true;

        console.log('this.selectedTargetContactId', this.selectedTargetContactId);

        if (!this.selectedTargetContactId) {
            showToast("Error", "Please select a target contact.", "error", "sticky", null);
            this.showSpinner = false;
            return;
        }

        let moveRequestParams = {
            "sourceContact": this.sourceContact
        };

        for (const potentialTargetContact of this.potentialTargetContacts) {
            if (potentialTargetContact.Id === this.selectedTargetContactId) {
                moveRequestParams.targetContact = potentialTargetContact;
                delete moveRequestParams.targetContact['Account'];// no need to send back the relationship, complicates serialization.
            }
        }

        console.log('moveRequestParams:  ', JSON.parse(JSON.stringify(moveRequestParams)));

        submitCustomMoveRequestInApex(moveRequestParams).then(moveRequestResult => {
            console.log('Got Move Request Result:  ', JSON.parse(JSON.stringify(moveRequestResult)));

            if (!moveRequestResult.isSuccess) {
                showToast("Error", "There was an while submitting move request, error:  " + moveRequestResult.messageText, "error", "sticky", null);
            } else {
                showToast("Info", moveRequestResult.messageText, "info", "sticky", null);
                showToast("Info", "Attempting to push target contact information to Ticketure.", "info", "sticky", null);

                const syncRequestParams = {
                    "targetContactId": this.selectedTargetContactId
                };
                submitSyncRequestInApex(syncRequestParams).then(syncRequestResult => {
                    if (syncRequestResult === true) {
                        showToast("Success", "Success!  Target contact information successfully pushed to Ticketure.", "success", "sticky", null);

                        this.closeThisModal();
                    } else {
                        showToast("Error", "Error syncing new contact info to Ticketure.", "error", "sticky", null);
                    }
                }).catch(error => {
                    console.log('We got an error:  ', JSON.parse(JSON.stringify(error)));
                    showToast("Error", "There was an error while submitting sync request, error:  " + error.body.message, "error", "sticky", null);
                });


            }
        }).catch(
            err => {
                handleError(err);
            }
        );
    }

    closeThisModal() {
        this.dispatchEvent(new CloseActionScreenEvent());
    }
}