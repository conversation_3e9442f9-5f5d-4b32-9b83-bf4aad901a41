/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

import {LightningElement, api, track} from "lwc";

export default class SyncContactWithTicketureField extends LightningElement {
    @api fieldComparoContainer;

    @track valueToKeep;
    @track showArrows;

    connectedCallback() {
        this.valueToKeep = this.fieldComparoContainer.valueToKeep;

        // if a user id is found, let's not let them remove it
        this.showArrows = !(this.fieldComparoContainer.ticketureFieldName === 'user.id' || this.fieldComparoContainer.ticketureFieldName === 'identity.id');
    }

    setSalesforceValue() {
        console.log('setSalesforceValue', this.fieldComparoContainer.salesforceFieldValue);
        this.valueToKeep = this.fieldComparoContainer.salesforceFieldValue;
        this.sendValueToKeepToParent(this.valueToKeep);
    }

    setTicketureValue() {
        console.log('setTicketureValue', this.fieldComparoContainer.ticketureFieldValue);
        this.valueToKeep = this.fieldComparoContainer.ticketureFieldValue;
        this.sendValueToKeepToParent(this.valueToKeep);
    }


    valueToKeepOnChange(event) {
        console.log('valueToKeepOnChange got event:  ', event);
        this.valueToKeep = event.target.value;
        this.sendValueToKeepToParent(event.target.value);
    }

    sendValueToKeepToParent(valueToKeep) {
        console.log('sendValueToKeepToParent got value:  ', valueToKeep);

        const payload = {
            "fieldKey": this.fieldComparoContainer.salesforceFieldName,
            "valueToKeep": valueToKeep
        }

        const selectedEvent = new CustomEvent("storevaluetokeep", {
            detail: payload
        });

        this.dispatchEvent(selectedEvent);
    }
}