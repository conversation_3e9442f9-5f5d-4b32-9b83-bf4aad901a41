<!--
  ~ Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
  ~
  ~ The code below is part of the Foglight Arts & Culture package and is not
  ~ to be redistributed without express written consent by Foglight.
  -->

<!--
 - Created by triestelaporte on 11/10/2021.
 -->

<!-- Sync Contact Field -->
<template>
    <div class="slds-grid slds-gutters slds-wrap syncRow">
        <div class="slds-col slds-size_2-of-12 slds-text-title_bold">
            <!--<lightning-input label={fieldComparoContainer.salesforceFieldLabel}
                             value={fieldComparoContainer.salesforceFieldName} disabled="true"
                             variant="label-inline"></lightning-input>--><!--
            <lightning-input value={fieldComparoContainer.salesforceFieldLabel} disabled="true"
                             variant="label-hidden"></lightning-input>-->
            {fieldComparoContainer.salesforceFieldLabel}
        </div>
        <div class="slds-col slds-size_2-of-12">
            <!--<lightning-input value={fieldComparoContainer.salesforceFieldValue} disabled="true"
                             variant="label-hidden"></lightning-input>-->
            {fieldComparoContainer.salesforceFieldValue}
        </div>
        <div class="slds-col slds-size_1-of-12 slds-text-align_right">
            <template if:true={showArrows}>
                <lightning-icon icon-name="utility:chevronright" onclick={setSalesforceValue}></lightning-icon>
            </template>
        </div>
        <div class="slds-col slds-size_4-of-12">
            <template if:true={showArrows}>
                <lightning-input value={valueToKeep} variant="label-hidden"
                                 onchange={valueToKeepOnChange}></lightning-input>
            </template>
            <template if:false={showArrows}>
                <lightning-input value={fieldComparoContainer.valueToKeep} variant="label-hidden"
                                 disabled="true"></lightning-input>
            </template>
        </div>
        <div class="slds-col slds-size_1-of-12 slds-text-align_left">
            <template if:true={showArrows}>
                <lightning-icon icon-name="utility:chevronleft" onclick={setTicketureValue}></lightning-icon>
            </template>
        </div>
        <div class="slds-col slds-size_2-of-12">
            <!--<lightning-input value={fieldComparoContainer.ticketureFieldValue} disabled="true"
                             variant="label-hidden"></lightning-input>-->
            {fieldComparoContainer.ticketureFieldValue}
        </div>
        <!--<div class="slds-col slds-size_1-of-12">
            <lightning-input value={fieldComparoContainer.ticketureFieldName} disabled="true"
                             variant="label-hidden"></lightning-input>
        </div>-->
    </div>
</template>