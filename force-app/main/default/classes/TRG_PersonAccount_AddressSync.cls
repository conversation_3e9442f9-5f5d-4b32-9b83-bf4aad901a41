/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 10/23/24.
 *
 * Keeps the Account.Billing address in sync with the Contact.Mailing
 * and the Account.Shipping address in sync with the Contact.Other
 */
public without sharing class TRG_PersonAccount_AddressSync extends Domain {
    public static Boolean suppress = false;

    public TRG_PersonAccount_AddressSync() {
    }
    public TRG_PersonAccount_AddressSync(List<Account> records) {
        super(records);
    }

    public override void doBeforeInsert() {
        setAddressFields(this.triggerRecords, null);
    }

    public override void doBeforeUpdate(Map<Id, SObject> oldRecordsMap) {
        setAddressFields(this.triggerRecords, oldRecordsMap);
    }

    public static void setAddressFields(List<Account> accounts, Map<Id, SObject> oldRecordsMap) {
        for (Account aNew : accounts) {

            if (aNew.RecordTypeId == AccountDomain.RT_PERSON_ACCOUNT) {
                Account aOld = (Account)oldRecordsMap?.get(aNew.Id);

                if (aOld == null) {
                    String billingStreet = aNew.BillingStreet ?? aNew.PersonMailingStreet;
                    String billingCity = aNew.BillingCity ?? aNew.PersonMailingCity;
                    String billingPostalCode = aNew.BillingPostalCode ?? aNew.PersonMailingPostalCode;

                    String shippingStreet = aNew.ShippingStreet ?? aNew.PersonOtherStreet;
                    String shippingCity = aNew.ShippingCity ?? aNew.PersonOtherCity;
                    String shippingPostalCode = aNew.ShippingPostalCode ?? aNew.PersonOtherPostalCode;

                    if (OrgInfoUtils.isStateAndCountryPicklistsEnabled()) {
                        String billingStateCode = (String) aNew.get('BillingStateCode') ?? (String)aNew.get('PersonMailingStateCode');
                        String billingCountryCode = (String) aNew.get('BillingCountryCode') ?? (String)aNew.get('PersonMailingCountryCode');

                        String shippingStateCode = (String) aNew.get('ShippingStateCode') ?? (String)aNew.get('PersonOtherStateCode');
                        String shippingCountryCode = (String) aNew.get('ShippingCountryCode') ?? (String)aNew.get('PersonOtherCountryCode');

                        aNew.put('BillingStateCode', billingStateCode);
                        aNew.put('BillingCountryCode', billingCountryCode);
                        aNew.put('PersonMailingStateCode', billingStateCode);
                        aNew.put('PersonMailingCountryCode', billingCountryCode);
                        aNew.put('ShippingStateCode', shippingStateCode);
                        aNew.put('ShippingCountryCode', shippingCountryCode);
                        aNew.put('PersonOtherStateCode', shippingStateCode);
                        aNew.put('PersonOtherCountryCode', shippingCountryCode);
                    } else {
                        String billingState = aNew.BillingState ?? aNew.PersonMailingState;
                        String billingCountry = aNew.BillingCountry ?? aNew.PersonMailingCountry;

                        String shippingState = aNew.ShippingState ?? aNew.PersonOtherState;
                        String shippingCountry = aNew.ShippingCountry ?? aNew.PersonOtherCountry;

                        aNew.BillingState = billingState;
                        aNew.BillingCountry = billingCountry;
                        aNew.PersonMailingState = billingState;
                        aNew.PersonMailingCountry = billingCountry;
                        aNew.ShippingState = shippingState;
                        aNew.ShippingCountry = shippingCountry;
                        aNew.PersonOtherState = shippingState;
                        aNew.PersonOtherCountry = shippingCountry;
                    }

                    aNew.BillingStreet = billingStreet;
                    aNew.BillingCity = billingCity;
                    aNew.BillingPostalCode = billingPostalCode;
                    aNew.PersonMailingStreet = billingStreet;
                    aNew.PersonMailingCity = billingCity;
                    aNew.PersonMailingPostalCode = billingPostalCode;

                    aNew.ShippingStreet = shippingStreet;
                    aNew.ShippingCity = shippingCity;
                    aNew.ShippingPostalCode = shippingPostalCode;
                    aNew.PersonOtherStreet = shippingStreet;
                    aNew.PersonOtherCity = shippingCity;
                    aNew.PersonOtherPostalCode = shippingPostalCode;
                } else {
                    // See if any of the fields have changed and sync them the other way

                    if (aNew.BillingStreet != aOld.BillingStreet) {
                        aNew.PersonMailingStreet = aNew.BillingStreet;
                    } else if (aNew.PersonMailingStreet != aOld.PersonMailingStreet) {
                        aNew.BillingStreet = aNew.PersonMailingStreet;
                    }

                    if (aNew.BillingCity != aOld.BillingCity) {
                        aNew.PersonMailingCity = aNew.BillingCity;
                    } else if (aNew.PersonMailingCity != aOld.PersonMailingCity) {
                        aNew.BillingCity = aNew.PersonMailingCity;
                    }

                    if (aNew.BillingPostalCode != aOld.BillingPostalCode) {
                        aNew.PersonMailingPostalCode = aNew.BillingPostalCode;
                    } else if (aNew.PersonMailingPostalCode != aOld.PersonMailingPostalCode) {
                        aNew.BillingPostalCode = aNew.PersonMailingPostalCode;
                    }

                    if (aNew.ShippingStreet != aOld.ShippingStreet) {
                        aNew.PersonOtherStreet = aNew.ShippingStreet;
                    } else if (aNew.PersonOtherStreet != aOld.PersonOtherStreet) {
                        aNew.ShippingStreet = aNew.PersonOtherStreet;
                    }

                    if (aNew.ShippingCity != aOld.ShippingCity) {
                        aNew.PersonOtherCity = aNew.ShippingCity;
                    } else if (aNew.PersonOtherCity != aOld.PersonOtherCity) {
                        aNew.ShippingCity = aNew.PersonOtherCity;
                    }

                    if (aNew.ShippingPostalCode != aOld.ShippingPostalCode) {
                        aNew.PersonOtherPostalCode = aNew.ShippingPostalCode;
                    } else if (aNew.PersonOtherPostalCode != aOld.PersonOtherPostalCode) {
                        aNew.ShippingPostalCode = aNew.PersonOtherPostalCode;
                    }

                    if (OrgInfoUtils.isStateAndCountryPicklistsEnabled()) {
                        if ((String) aNew.get('BillingStateCode') != (String) aOld.get('BillingStateCode')) {
                            aNew.put('PersonMailingStateCode', aNew.get('BillingStateCode'));
                        } else if ((String) aNew.get('PersonMailingStateCode') != (String) aOld.get('PersonMailingStateCode')) {
                            aNew.put('BillingStateCode', aNew.get('PersonMailingStateCode'));
                        }

                        if ((String) aNew.get('BillingCountryCode') != (String) aOld.get('BillingCountryCode')) {
                            aNew.put('PersonMailingCountryCode', aNew.get('BillingCountryCode'));
                        } else if ((String) aNew.get('PersonMailingCountryCode') != (String) aOld.get('PersonMailingCountryCode')) {
                            aNew.put('BillingCountryCode', aNew.get('PersonMailingCountryCode'));
                        }

                        if ((String) aNew.get('ShippingStateCode') != (String) aOld.get('ShippingStateCode')) {
                            aNew.put('PersonOtherStateCode', aNew.get('ShippingStateCode'));
                        } else if ((String) aNew.get('PersonOtherStateCode') != (String) aOld.get('PersonOtherStateCode')) {
                            aNew.put('ShippingStateCode', aNew.get('PersonOtherStateCode'));
                        }

                        if ((String) aNew.get('ShippingCountryCode') != (String) aOld.get('ShippingCountryCode')) {
                            aNew.put('PersonOtherCountryCode', aNew.get('ShippingCountryCode'));
                        } else if ((String) aNew.get('PersonOtherCountryCode') != (String) aOld.get('PersonOtherCountryCode')) {
                            aNew.put('ShippingCountryCode', aNew.get('PersonOtherCountryCode'));
                        }
                    } else {
                        if (aNew.BillingState != aOld.BillingState) {
                            aNew.PersonMailingState = aNew.BillingState;
                        } else if (aNew.PersonMailingState != aOld.PersonMailingState) {
                            aNew.BillingState = aNew.PersonMailingState;
                        }

                        if (aNew.BillingCountry != aOld.BillingCountry) {
                            aNew.PersonMailingCountry = aNew.BillingCountry;
                        } else if (aNew.PersonMailingCountry != aOld.PersonMailingCountry) {
                            aNew.BillingCountry = aNew.PersonMailingCountry;
                        }

                        if (aNew.ShippingState != aOld.ShippingState) {
                            aNew.PersonOtherState = aNew.ShippingState;
                        } else if (aNew.PersonOtherState != aOld.PersonOtherState) {
                            aNew.ShippingState = aNew.PersonOtherState;
                        }

                        if (aNew.ShippingCountry != aOld.ShippingCountry) {
                            aNew.PersonOtherCountry = aNew.ShippingCountry;
                        } else if (aNew.PersonOtherCountry != aOld.PersonOtherCountry) {
                            aNew.ShippingCountry = aNew.PersonOtherCountry;
                        }
                    }
                }
            }
        }
    }
}