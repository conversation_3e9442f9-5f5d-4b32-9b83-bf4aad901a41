/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 6/27/24.
 */

public without sharing class OpportunityLineItemScheduleDomain extends Domain {

    public OpportunityLineItemScheduleDomain() {
        super();
    }
    public OpportunityLineItemScheduleDomain(List<OpportunityLineItemSchedule> triggerRecords) {
        super(triggerRecords);
    }

    public override void doBeforeInsert() {
        defaults((List<OpportunityLineItemSchedule>)this.triggerRecords);
    }

    public override void doBeforeUpdate(Map<Id, SObject> oldRecordsMap) {
        defaults((List<OpportunityLineItemSchedule>)this.triggerRecords);
    }

    static void defaults(List<OpportunityLineItemSchedule> olis) {
        for (OpportunityLineItemSchedule oli : olis) {
            oli.OpportunityLineItem__c = oli.OpportunityLineItemId; // this should only be set for Payment Allocations
        }
    }
}