/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 10/15/24.
 */

public without sharing class ContactPointEmailDomain extends Domain {
    public static Boolean RUN_SETACCOUNTEMAIL = true;

    public ContactPointEmailDomain() {
    }
    public ContactPointEmailDomain(List<ContactPointEmail> records) {
        super(records);
    }

    public override void doBeforeInsert() {
        setAccount(this.triggerRecords);
    }

    public override void doBeforeUpdate(Map<Id, SObject> oldRecordsMap) {
        setAccount(this.triggerRecords);
    }

    public static void setAccount(List<ContactPointEmail> records) {
        for (ContactPointEmail cpa : records) {
            if (cpa.ParentId.getSobjectType() == AccountDomain.ACCOUNT_TYPE) {
                cpa.Account__c = cpa.ParentId;
            }
        }
    }

    public override void doAfterInsert() {
        //setAccountEmail(this.triggerRecords, null);
    }

    public override void doAfterUpdate(Map<Id, SObject> oldRecordsMap) {
        //setAccountEmail(this.triggerRecords, oldRecordsMap);
    }

    public static void setAccountEmail(List<ContactPointEmail> newRecords, Map<Id, SObject> oldRecordsMap) {
        if (!RUN_SETACCOUNTEMAIL) {
            return;
        }

        // If the Primary flag is changed on an existing record, update the account with the email and set other CPEs to not primary
        // If the Email address is changed on an existing record and it's primary, update the account with the email

        // map usage type to primary email type

        Set<Id> qualifiedAccountIds = new Set<Id>();
        List<Account> accountsToUpdate = new List<Account>();
        for (ContactPointEmail cpeNew : newRecords) {
            ContactPointEmail cpeOld = (ContactPointEmail)oldRecordsMap?.get(cpeNew.Id);

            if (cpeNew.IsPrimary && (cpeOld == null || cpeOld.IsPrimary == false || !cpeNew.EmailAddress.equalsIgnoreCase(cpeOld.EmailAddress))) {
                qualifiedAccountIds.add(cpeNew.ParentId);
                accountsToUpdate.add(new Account(
                        Id = cpeNew.ParentId,
                        PersonEmail = cpeNew.EmailAddress,
                        PrimaryEmailType__c = cpeNew.UsageType
                ));
            }
        }

        if (accountsToUpdate.size() > 0) {
            Domain.suppressExecution(Account.SObjectType);
            DB.init().edit(accountsToUpdate);
            Domain.allowExecution(Account.SObjectType);
        }

        if (qualifiedAccountIds.size() > 0) {
            // Get all the accounts and their existing primary CPEs
            List<ContactPointEmail> cpesToUpdate = [
                    SELECT Id, IsPrimary
                    FROM ContactPointEmail
                    WHERE ParentId IN :qualifiedAccountIds
                            AND IsPrimary = TRUE
                            AND Id NOT IN :newRecords
            ];
            for (ContactPointEmail cpe : cpesToUpdate) {
                cpe.IsPrimary = false;
            }

            Domain.suppressExecution(Account.SObjectType);
            Domain.suppressExecution(ContactPointEmail.SObjectType);
            DB.init().edit(cpesToUpdate);
            Domain.allowExecution(ContactPointEmail.SObjectType);
            Domain.allowExecution(Account.SObjectType);
        }
    }
}