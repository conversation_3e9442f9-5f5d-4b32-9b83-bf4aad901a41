/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 4/22/25.
 */

public without sharing class TRG_GSC_HHMembers extends Domain {
    public static Boolean noRun = false;

    public TRG_GSC_HHMembers(){}
    public TRG_GSC_HHMembers(List<Opportunity> records) {
        super(records);
    }

    public override void doAfterInsert() {
        hhSoftCredits(this.triggerRecords);
    }

    public static void hhSoftCredits(List<GiftSoftCredit> gscs) {
        if (noRun == true || ACSettings.getInstance().createSoftCreditForHHMembers != true) {
            return;
        }

        Map<Id,Account> accounts = new Map<Id,Account>();

        for (GiftSoftCredit gsc : gscs) {
            accounts.put(gsc.RecipientId, null);
        }

        accounts = new Map<Id,Account>([
                SELECT Id, (
                        SELECT Id, AccountId
                        FROM HouseholdMembers__r
                        WHERE ExcludeFromHouseholdSoftCredit__c = FALSE
                )
                FROM Account
                WHERE Id IN :accounts.keySet() AND RecordTypeId = :AccountDomain.RT_HH
        ]);

        List<GiftSoftCredit> memberSoftCredits = new List<GiftSoftCredit>();
        for (GiftSoftCredit gsc : gscs) {
            for (Contact c : accounts.get(gsc.RecipientId)?.HouseholdMembers__r ?? new List<Contact>()) {
                memberSoftCredits.add(
                        new GiftSoftCredit(GiftTransactionId = gsc.GiftTransactionId, RecipientId = c.AccountId, Role = ACSettings.getInstance().hhMemberSoftCreditRole ?? gsc.Role)
                );
            }
        }

        noRun = true;
        Database.insert(memberSoftCredits, false);
        noRun = false;
    }
}