/**
 * Created by edwardblazer on 6/20/23.
 */

@SuppressWarnings('InconsistentSalesforceApiVersion')
public with sharing class ACSettings {
    @TestVisible
    private static ACSettings singleton;
    private final ACSettings__mdt record;

    @TestVisible
    private ACSettings(ACSettings__mdt record) {
        this.record = record;


        this.rollupBatchSize                = this.record.RollupBatchSize__c?.intValue();
        this.createSoftCreditForHHMembers   = this.record.CreateSoftCreditForHHMembers__c;
        this.hhMemberSoftCreditRole         = this.record.HHMemberSoftCreditRole__c;
        this.hhCreationCriteria             = this.record.CreateHouseholdCriteria__c;

    }

    public static ACSettings getInstance() {
        if (singleton == null) {
            singleton = new ACSettings(ACSettings__mdt.getInstance('Default'));
        }
        return singleton;
    }

    public Integer rollupBatchSize;
    public Boolean createSoftCreditForHHMembers;
    public String hhMemberSoftCreditRole;
    public String hhCreationCriteria;
}