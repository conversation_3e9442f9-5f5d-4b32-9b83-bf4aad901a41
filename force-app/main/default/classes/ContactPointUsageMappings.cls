/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 5/27/25.
 */

public with sharing class ContactPointUsageMappings {
    public static final Map<String,ContactPointUsageMapping__mdt> emailMappingsByUsage = new Map<String,ContactPointUsageMapping__mdt>();
    public static final Map<String,ContactPointUsageMapping__mdt> emailMappingsByField = new Map<String,ContactPointUsageMapping__mdt>();
    public static final Map<String,ContactPointUsageMapping__mdt> phoneMappingsByUsage = new Map<String,ContactPointUsageMapping__mdt>();
    public static final Map<String,ContactPointUsageMapping__mdt> phoneMappingsByField = new Map<String,ContactPointUsageMapping__mdt>();

    public static final List<ContactPointUsageMapping__mdt> allMappings = [
            SELECT DeveloperName, ContactPointObject__c, SourceField__c, UsageType__c
            FROM ContactPointUsageMapping__mdt
    ];

    static {
        for (ContactPointUsageMapping__mdt mapping : allMappings) {
            if (mapping.ContactPointObject__c.equalsIgnoreCase('ContactPointEmail')) {
                emailMappingsByUsage.put(mapping.UsageType__c, mapping);
                emailMappingsByField.put(mapping.SourceField__c, mapping);
            } else if (mapping.ContactPointObject__c.equalsIgnoreCase('ContactPointPhone')) {
                phoneMappingsByUsage.put(mapping.UsageType__c, mapping);
                phoneMappingsByField.put(mapping.SourceField__c, mapping);
            }
        }
    }
}