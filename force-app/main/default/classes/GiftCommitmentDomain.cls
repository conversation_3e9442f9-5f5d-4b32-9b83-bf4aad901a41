/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 7/1/24.
 */

public without sharing class GiftCommitmentDomain extends Domain {

    public GiftCommitmentDomain (){}
    public GiftCommitmentDomain (List<GiftCommitment> records) {
        super(records);
    }

    public override void doBeforeInsert() {
        defaults((List<GiftCommitment>)this.triggerRecords);
    }

    public override void doBeforeUpdate(Map<Id, SObject> oldRecordsMap) {
        defaults((List<GiftCommitment>) this.triggerRecords);
    }

    public void defaults(List<GiftCommitment> records) {
        Map<Id,Account> parentAccountMap = new Map<Id,Account>();
        for (GiftCommitment gc : records) {
            if (gc.RevenueType__c == null || gc.Household__c == null) {
                parentAccountMap.put(gc.DonorId, null);
            }
        }
        parentAccountMap.remove(null);

        if (parentAccountMap.size() > 0) {
            parentAccountMap = new Map<Id, Account>([
                    SELECT Id, RevenueType__c, flmas__PrimaryHousehold__pc, RecordType.IsPersonType
                    FROM Account
                    WHERE Id IN :parentAccountMap.keySet()
            ]);
            for (GiftCommitment gc : records) {
                Account a = parentAccountMap.get(gc.DonorId);
                if (a != null) {
                    if (gc.RevenueType__c == null) {
                        gc.RevenueType__c = a?.RevenueType__c;
                    }
                    if (gc.Household__c == null && a != null && a.RecordType?.IsPersonType == true && a.flmas__PrimaryHousehold__pc != null) {
                        gc.Household__c = a.flmas__PrimaryHousehold__pc;
                    }
                }
            }
        }
    }
}