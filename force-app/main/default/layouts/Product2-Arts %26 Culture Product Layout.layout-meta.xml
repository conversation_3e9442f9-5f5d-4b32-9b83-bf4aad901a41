<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>OpenSlackRecordChannel</excludeButtons>
    <excludeButtons>Submit</excludeButtons>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Product  Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ProductCode</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CanUseQuantitySchedule</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsActive</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Family</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CanUseRevenueSchedule</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Ticketure Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>tixtrack__TicketureEventName__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TicketureHierarchy__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MembershipRoleRank__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>TicketureProductSequence__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>tixtrack__TicketureEventTemplateId__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ParentProduct__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TicketureSeller__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Fields</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Description Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Description</field>
            </layoutItems>
        </layoutColumns>
        <style>OneColumn</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <relatedLists>
        <relatedList>RelatedStandardPriceList</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedPricebookEntryList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>IsEnabled__c</fields>
        <fields>EffectiveDate__c</fields>
        <fields>FairMarketValuePct__c</fields>
        <fields>MaxFairMarketValue__c</fields>
        <relatedList>FairMarketValue__c.Product__c</relatedList>
        <sortField>EffectiveDate__c</sortField>
        <sortOrder>Desc</sortOrder>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>Category__c</fields>
        <fields>IsAvailable__c</fields>
        <relatedList>Benefit__c.Product__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>PRODUCT2.NAME</fields>
        <fields>TicketureHierarchy__c</fields>
        <fields>PRODUCT2.DESCRIPTION</fields>
        <relatedList>Product2.ParentProduct__c</relatedList>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00hOt0000041F7p</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>0</sizeY>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
