<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>OpenSlackRecordChannel</excludeButtons>
    <excludeButtons>Submit</excludeButtons>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Campaign Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OwnerId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsActive</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Type</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Status</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>StartDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EndDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ExpectedRevenue</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>BudgetedCost</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ActualCost</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ExpectedResponse</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>NumberSent</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ParentId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsFundraisingCampaign__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>NumberOfLeads</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>NumberOfConvertedLeads</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>NumberOfContacts</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>NumberOfResponses</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>NumberOfOpportunities</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>NumberOfWonOpportunities</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>AmountAllOpportunities</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>AmountWonOpportunities</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Other Information</label>
        <layoutColumns/>
        <layoutColumns/>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Additional Information</label>
        <layoutColumns/>
        <layoutColumns/>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Description Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Description</field>
            </layoutItems>
        </layoutColumns>
        <style>OneColumn</style>
    </layoutSections>
    <relatedContent>
        <relatedContentItems>
            <layoutItem>
                <behavior>Readonly</behavior>
                <field>ParentId</field>
            </layoutItem>
        </relatedContentItems>
    </relatedContent>
    <relatedLists>
        <fields>CAMPAIGN.NAME</fields>
        <fields>CAMPAIGN.TOTAL_NUM_LEADS</fields>
        <fields>CAMPAIGN.TOT_CONVERTED_LEADS</fields>
        <fields>CAMPAIGN.TOT_CONTACTS</fields>
        <fields>CAMPAIGN.TOT_OPPORTUNITIES</fields>
        <fields>CAMPAIGN.TOT_WON_OPPORTUNITIES</fields>
        <fields>TOT_AMOUNT_WON</fields>
        <fields>TOT_AMOUNT_ALL</fields>
        <fields>CAMPAIGN.TOT_NUM_RESPONSES</fields>
        <fields>TOT_NUM_SENT</fields>
        <fields>TOT_EXP_REVENUE</fields>
        <fields>TOT_BUDGETED_COST</fields>
        <fields>TOT_ACTUAL_COST</fields>
        <relatedList>RelatedCampaignHierarchyList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>MEMBER_TYPE</fields>
        <fields>STATUS</fields>
        <fields>FULL_NAME</fields>
        <fields>TITLE</fields>
        <fields>FIRST_NAME</fields>
        <fields>LAST_NAME</fields>
        <fields>COMPANY</fields>
        <relatedList>RelatedCampaignMemberList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>StartDate__c</fields>
        <fields>EndDate__c</fields>
        <fields>Goal__c</fields>
        <fields>PipelineAmount__c</fields>
        <fields>CommittedAmount__c</fields>
        <fields>ReceivedAmount__c</fields>
        <relatedList>FundraisingCampaignGoal__c.FundraisingCampaign__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>Name</fields>
        <fields>Status</fields>
        <fields>SourceCode</fields>
        <relatedList>OutreachSourceCodes</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>OPPORTUNITY.NAME</fields>
        <fields>OPPORTUNITY.STAGE_NAME</fields>
        <fields>OPPORTUNITY.AMOUNT</fields>
        <fields>OPPORTUNITY.CLOSE_DATE</fields>
        <relatedList>RelatedOpportunityList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>TASK.SUBJECT</fields>
        <fields>TASK.WHO_NAME</fields>
        <fields>ACTIVITY.TASK</fields>
        <fields>TASK.DUE_DATE</fields>
        <fields>TASK.STATUS</fields>
        <fields>TASK.PRIORITY</fields>
        <fields>CORE.USERS.FULL_NAME</fields>
        <relatedList>RelatedActivityList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>TASK.SUBJECT</fields>
        <fields>TASK.WHO_NAME</fields>
        <fields>ACTIVITY.TASK</fields>
        <fields>TASK.DUE_DATE</fields>
        <fields>CORE.USERS.FULL_NAME</fields>
        <fields>TASK.LAST_UPDATE</fields>
        <relatedList>RelatedHistoryList</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedAttachmentList</relatedList>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00hEi000004P1rR</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>0</sizeY>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
