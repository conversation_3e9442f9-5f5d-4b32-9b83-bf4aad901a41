<?xml version="1.0" encoding="UTF-8"?>
<Report xmlns="http://soap.sforce.com/2006/04/metadata">
    <chart>
        <backgroundColor1>#FFFFFF</backgroundColor1>
        <backgroundColor2>#FFFFFF</backgroundColor2>
        <backgroundFadeDir>Diagonal</backgroundFadeDir>
        <chartSummaries>
            <aggregate>Sum</aggregate>
            <axisBinding>y</axisBinding>
            <column>QUANTITY</column>
        </chartSummaries>
        <chartType>VerticalColumnStacked</chartType>
        <enableHoverLabels>false</enableHoverLabels>
        <expandOthers>true</expandOthers>
        <groupingColumn>OpportunityLineItem.tixtrack__TicketureEventSessionDateTime__c</groupingColumn>
        <legendPosition>Right</legendPosition>
        <location>CHART_BOTTOM</location>
        <secondaryGroupingColumn>OpportunityLineItem.tixtrack__TicketureTicketGroup__c</secondaryGroupingColumn>
        <showAxisLabels>false</showAxisLabels>
        <showPercentage>false</showPercentage>
        <showTotal>false</showTotal>
        <showValues>false</showValues>
        <size>Medium</size>
        <summaryAxisRange>Auto</summaryAxisRange>
        <textColor>#000000</textColor>
        <textSize>12</textSize>
        <titleColor>#000000</titleColor>
        <titleSize>18</titleSize>
    </chart>
    <columns>
        <field>OPPORTUNITY_NAME</field>
    </columns>
    <columns>
        <field>OpportunityLineItem.tixtrack__TicketureStatus__c</field>
    </columns>
    <columns>
        <field>PRODUCT_NAME</field>
    </columns>
    <columns>
        <aggregateTypes>Sum</aggregateTypes>
        <field>QUANTITY</field>
    </columns>
    <columns>
        <aggregateTypes>Sum</aggregateTypes>
        <field>TOTAL_PRICE</field>
    </columns>
    <columns>
        <field>OpportunityLineItem.tixtrack__TicketureTicketId__c</field>
    </columns>
    <columns>
        <field>OpportunityLineItem.tixtrack__TicketureScanCode_ExtId__c</field>
    </columns>
    <columns>
        <field>OpportunityLineItem.tixtrack__TicketureScanCode__c</field>
    </columns>
    <columns>
        <field>CON.EMAIL</field>
    </columns>
    <description>Shows forecasted daily attendance for the next 30 days grouped by event and ticket group</description>
    <filter>
        <criteriaItems>
            <column>OpportunityLineItem.tixtrack__TicketureStatus__c</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>true</isUnlocked>
            <operator>equals</operator>
            <value>force_redeemed,held,purchased,redeemed,revalidated,transfer_from,transfer_to,unredeemed</value>
        </criteriaItems>
        <criteriaItems>
            <column>OpportunityLineItem.tixtrack__TicketureRefundedDateTime__c</column>
            <columnToColumn>false</columnToColumn>
            <isUnlocked>true</isUnlocked>
            <operator>equals</operator>
            <value></value>
        </criteriaItems>
    </filter>
    <format>Matrix</format>
    <groupingsAcross>
        <field>OpportunityLineItem.tixtrack__TicketureEventSessionDateTime__c</field>
        <sortOrder>Asc</sortOrder>
    </groupingsAcross>
    <groupingsDown>
        <dateGranularity>Day</dateGranularity>
        <field>Product2.tixtrack__TicketureEventName__c</field>
        <sortOrder>Asc</sortOrder>
    </groupingsDown>
    <groupingsDown>
        <dateGranularity>Day</dateGranularity>
        <field>OpportunityLineItem.tixtrack__TicketureTicketGroup__c</field>
        <sortOrder>Asc</sortOrder>
    </groupingsDown>
    <name>Admissions Forecast Report</name>
    <params>
        <name>open</name>
        <value>all</value>
    </params>
    <params>
        <name>probability</name>
        <value>&gt;0</value>
    </params>
    <params>
        <name>co</name>
        <value>1</value>
    </params>
    <reportType>OpportunityProduct</reportType>
    <scope>organization</scope>
    <showDetails>false</showDetails>
    <showGrandTotal>true</showGrandTotal>
    <showSubTotals>true</showSubTotals>
    <timeFrameFilter>
        <dateColumn>OpportunityLineItem.tixtrack__TicketureEventSessionDateTime__c</dateColumn>
        <interval>INTERVAL_NEXT30</interval>
    </timeFrameFilter>
</Report>
