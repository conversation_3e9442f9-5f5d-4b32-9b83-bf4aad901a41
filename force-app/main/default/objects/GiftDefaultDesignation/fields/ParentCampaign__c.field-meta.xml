<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>ParentCampaign__c</fullName>
    <deleteConstraint>SetNull</deleteConstraint>
    <description>This field exists because the standard "Parent Record" field is polymorphic. Polymorphic lookups have limitations including an inability to pull in related data from the parent record via Formula Fields and Report Types, etc. This field is automatically populated by a trigger.</description>
    <label>Parent Campaign</label>
    <referenceTo>Campaign</referenceTo>
    <relationshipLabel>Gift Default Designations (PC)</relationshipLabel>
    <relationshipName>GiftDefaultDesignations_PC</relationshipName>
    <required>false</required>
    <trackHistory>false</trackHistory>
    <type>Lookup</type>
</CustomField>
