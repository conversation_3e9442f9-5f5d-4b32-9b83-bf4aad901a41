<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>CashOrWOAccount__c</fullName>
    <deleteConstraint>SetNull</deleteConstraint>
    <label>Cash/Write-Off Account</label>
    <lookupFilter>
        <active>true</active>
        <errorMessage>The selected Ledger Account must be a bank or write-off account.</errorMessage>
        <filterItems>
            <field>LedgerAccount__c.GLAccount__r.AccountSubType__c</field>
            <operation>equals</operation>
            <value>Bank Account, Write-Off</value>
        </filterItems>
        <isOptional>false</isOptional>
    </lookupFilter>
    <referenceTo>LedgerAccount__c</referenceTo>
    <relationshipLabel>Gift Transaction Designations (Cash/WO)</relationshipLabel>
    <relationshipName>GiftTransactionDesignations_CashOrWO</relationshipName>
    <required>false</required>
    <trackHistory>false</trackHistory>
    <type>Lookup</type>
</CustomField>
