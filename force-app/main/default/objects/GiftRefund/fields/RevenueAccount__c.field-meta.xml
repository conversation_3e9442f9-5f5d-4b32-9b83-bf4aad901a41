<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>RevenueAccount__c</fullName>
    <deleteConstraint>SetNull</deleteConstraint>
    <label>Revenue Account</label>
    <lookupFilter>
        <active>true</active>
        <errorMessage>The selected Ledger Account must be a revenue account.</errorMessage>
        <filterItems>
            <field>LedgerAccount__c.GLAccount__r.AccountSubType__c</field>
            <operation>equals</operation>
            <value>Deferred Revenue, Earned Revenue</value>
        </filterItems>
        <isOptional>false</isOptional>
    </lookupFilter>
    <referenceTo>LedgerAccount__c</referenceTo>
    <relationshipLabel>GiftRefunds (Revenue)</relationshipLabel>
    <relationshipName>GiftRefunds_Revenue</relationshipName>
    <required>false</required>
    <trackHistory>false</trackHistory>
    <type>Lookup</type>
</CustomField>
