<?xml version="1.0" encoding="UTF-8"?>
<CustomMetadata xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <label>Example GiftDefDes: Rcv-Rev (Tx)</label>
    <protected>false</protected>
    <values>
        <field>DefinitionType__c</field>
        <value xsi:type="xsd:string">Tx</value>
    </values>
    <values>
        <field>Description__c</field>
        <value xsi:type="xsd:string">An example definition that creates Ledger Entry records that book revenue with receivables from Gift Default Designations.

This definition references Ledger Account lookup fields directly on the source record. Those fields should be populated with appropriate Ledger Format metadata definitions.</value>
    </values>
    <values>
        <field>GroupLedgersByAccount__c</field>
        <value xsi:type="xsd:boolean">false</value>
    </values>
    <values>
        <field>HeaderDate__c</field>
        <value xsi:type="xsd:string">TransactionDate</value>
    </values>
    <values>
        <field>HeaderFilter__c</field>
        <value xsi:type="xsd:string">IsPaid = TRUE
AND GiftCommitmentId != NULL</value>
    </values>
    <values>
        <field>HeaderObject__c</field>
        <value xsi:type="xsd:string">GiftTransaction</value>
    </values>
    <values>
        <field>IsEnabled__c</field>
        <value xsi:type="xsd:boolean">true</value>
    </values>
    <values>
        <field>LedgerEntryHeaderLookup__c</field>
        <value xsi:type="xsd:string">GiftTransaction__c</value>
    </values>
    <values>
        <field>LedgerEntryLineItemHeaderLookup__c</field>
        <value xsi:type="xsd:string">GiftTransaction__c</value>
    </values>
    <values>
        <field>LedgerEntryLineItemObject__c</field>
        <value xsi:type="xsd:string">LedgerEntryLineItem__c</value>
    </values>
    <values>
        <field>LedgerEntryLineItemSourceLookup__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>LedgerEntryObject__c</field>
        <value xsi:type="xsd:string">LedgerEntry__c</value>
    </values>
    <values>
        <field>LineItemARorAPAcctLookup__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>LineItemAmount__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>LineItemHeaderLookup__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>LineItemObject__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>LineItemRevOrExpAcctLookup__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>LineItemRevOrExpDateField__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>LineItemType__c</field>
        <value xsi:type="xsd:string">None</value>
    </values>
    <values>
        <field>PaymentCashOrWOAcctLookup__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>PaymentDueDate__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>PaymentHeaderLookup__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>PaymentLineItemARorAPAcctLookup__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>PaymentLineItemAmount__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>PaymentLineItemCashOrWOAcctLookup__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>PaymentLineItemLineItemLookup__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>PaymentLineItemObject__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>PaymentLineItemPaymentLookup__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>PaymentLineItemRevOrExpAcctLookup__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>PaymentObject__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>PaymentPaidDate__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>PaymentPaymentAmount__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>PaymentWriteoffDate__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>TransactionAmountFieldPath__c</field>
        <value xsi:type="xsd:string">Amount</value>
    </values>
    <values>
        <field>TransactionCreditAccountFieldPath__c</field>
        <value xsi:type="xsd:string">flmas__RevenueAccount__c</value>
    </values>
    <values>
        <field>TransactionDateFieldPath__c</field>
        <value xsi:type="xsd:string">GiftTransaction.TransactionDate</value>
    </values>
    <values>
        <field>TransactionDebitAccountFieldPath__c</field>
        <value xsi:type="xsd:string">flmas__ReceivablesAccount__c</value>
    </values>
    <values>
        <field>TransactionFilter__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>TransactionObject__c</field>
        <value xsi:type="xsd:string">GiftTransactionDesignation</value>
    </values>
    <values>
        <field>TransactionPathToHeader__c</field>
        <value xsi:type="xsd:string">GiftTransaction</value>
    </values>
    <values>
        <field>TxCreditAccountFormat__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>TxDebitAccountFormat__c</field>
        <value xsi:nil="true"/>
    </values>
</CustomMetadata>
