<?xml version="1.0" encoding="UTF-8"?>
<CustomMetadata xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <label>Generate Ledgers</label>
    <protected>false</protected>
    <values>
        <field>ApexClassName__c</field>
        <value xsi:type="xsd:string">flmas.SLQualifyRecords</value>
    </values>
    <values>
        <field>IsAsync__c</field>
        <value xsi:type="xsd:boolean">false</value>
    </values>
    <values>
        <field>IsDisabledAfterDelete__c</field>
        <value xsi:type="xsd:boolean">false</value>
    </values>
    <values>
        <field>IsDisabledAfterInsert__c</field>
        <value xsi:type="xsd:boolean">false</value>
    </values>
    <values>
        <field>IsDisabledAfterUndelete__c</field>
        <value xsi:type="xsd:boolean">false</value>
    </values>
    <values>
        <field>IsDisabledAfterUpdate__c</field>
        <value xsi:type="xsd:boolean">false</value>
    </values>
    <values>
        <field>IsDisabledBeforeDelete__c</field>
        <value xsi:type="xsd:boolean">false</value>
    </values>
    <values>
        <field>IsDisabledBeforeInsert__c</field>
        <value xsi:type="xsd:boolean">false</value>
    </values>
    <values>
        <field>IsDisabledBeforeUpdate__c</field>
        <value xsi:type="xsd:boolean">false</value>
    </values>
    <values>
        <field>IsDisabledCompletely__c</field>
        <value xsi:type="xsd:boolean">false</value>
    </values>
    <values>
        <field>ObjectAPIName__c</field>
        <value xsi:type="xsd:string">GiftTransaction</value>
    </values>
    <values>
        <field>Priority__c</field>
        <value xsi:type="xsd:double">50.0</value>
    </values>
</CustomMetadata>
