<?xml version="1.0" encoding="UTF-8"?>
<FlexiPage xmlns="http://soap.sforce.com/2006/04/metadata">
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>Edit</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Account.tixtrack__ViewInTicketure</value>
                            <visibilityRule>
                                <booleanFilter>1 OR 2</booleanFilter>
                                <criteria>
                                    <leftValue>{!Record.RecordTypeName__c}</leftValue>
                                    <operator>CONTAINS</operator>
                                    <rightValue>Person</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.RecordTypeName__c}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>Organization</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>Delete</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Account.RecalculateRollups</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Account.Add_Household_Member</value>
                            <visibilityRule>
                                <criteria>
                                    <leftValue>{!Record.RecordTypeName__c}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>Household</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>Account.New_Affiliation</value>
                            <visibilityRule>
                                <criteria>
                                    <leftValue>{!Record.RecordType.Name}</leftValue>
                                    <operator>CONTAINS</operator>
                                    <rightValue>Person</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>CreateDonorBrief</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Account.NewGiftEntry</value>
                        </valueListItems>
                        <valueListItems>
                            <value>ChangeRecordType</value>
                        </valueListItems>
                        <valueListItems>
                            <value>ChangeOwnerOne</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>collapsed</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>enableActionsConfiguration</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>enableActionsInNative</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>hideChatterActions</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>numVisibleActions</name>
                    <value>3</value>
                </componentInstanceProperties>
                <componentName>force:highlightsPanel</componentName>
                <identifier>force_highlightsPanel</identifier>
            </componentInstance>
        </itemInstances>
        <name>header</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Name</fieldItem>
                <identifier>RecordNameField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Nickname__pc</fieldItem>
                <identifier>Recordflmas_Nickname_pcField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.PersonPronouns</fieldItem>
                <identifier>RecordPersonPronounsField2</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.PersonGenderIdentity</fieldItem>
                <identifier>RecordPersonGenderIdentityField2</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.PersonBirthdate</fieldItem>
                <identifier>RecordPersonBirthdateField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.DeceasedDate__pc</fieldItem>
                <identifier>Recordflmas_DeceasedDate_pcField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-103954f7-244c-4a12-88e2-8c8e307a4e56</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.PrimaryHousehold__pc</fieldItem>
                <identifier>Recordflmas_PrimaryHousehold_pcField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ExcludefromHouseholdNaming__pc</fieldItem>
                <identifier>Recordflmas_ExcludefromHouseholdNaming_pcField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ExcludeFromHouseholdSoftCredit__pc</fieldItem>
                <identifier>Recordflmas_ExcludeFromHouseholdSoftCredit__pcField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.IsPrimaryContact__pc</fieldItem>
                <identifier>Recordflmas_IsPrimaryContact_pcField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.IsSecondaryContact__pc</fieldItem>
                <identifier>Recordflmas_IsSecondaryContact_pcField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-a82f3747-5037-457d-907a-78359c7b3cd8</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-103954f7-244c-4a12-88e2-8c8e307a4e56</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-a82f3747-5037-457d-907a-78359c7b3cd8</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column2</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-146c79f8-97ce-452e-b95d-584f5b8a3a08</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Name</fieldItem>
                <identifier>RecordNameField2</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.PrimaryContact__c</fieldItem>
                <identifier>Recordflmas_PrimaryContact_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.SecondaryContact__c</fieldItem>
                <identifier>Recordflmas_SecondaryContact_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-28e9078f-0758-4a48-9e75-026d5f81a81d</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.OverrideName__c</fieldItem>
                <identifier>Recordflmas_OverrideName_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Phone</fieldItem>
                <identifier>RecordPhoneField2</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-966fde38-51cf-4671-ae53-2410d6b08bb0</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-28e9078f-0758-4a48-9e75-026d5f81a81d</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column5</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-966fde38-51cf-4671-ae53-2410d6b08bb0</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column6</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-e5a87f53-b99e-4e92-9849-06acb4f4afcd</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Name</fieldItem>
                <identifier>RecordNameField3</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ShortName__c</fieldItem>
                <identifier>Recordflmas_ShortName_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ParentId</fieldItem>
                <identifier>RecordParentIdField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.PrimaryContact__c</fieldItem>
                <identifier>Recordflmas_PrimaryContact_cField2</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-031bfede-705e-4e7e-849f-cad868071af2</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Type</fieldItem>
                <identifier>RecordTypeField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Industry</fieldItem>
                <identifier>RecordIndustryField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.RevenueType__c</fieldItem>
                <identifier>Recordflmas_RevenueType_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-ce297b22-536b-4b4d-802d-1c95f9fe4c2e</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-031bfede-705e-4e7e-849f-cad868071af2</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column7</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-ce297b22-536b-4b4d-802d-1c95f9fe4c2e</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column8</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-4e2ff118-aa88-4fdc-bbad-cabafc1d8b97</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.FormalGreeting__c</fieldItem>
                <identifier>Recordflmas_FormalGreeting_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.InformalGreeting__c</fieldItem>
                <identifier>Recordflmas_InformalGreeting_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-15832097-0c5d-4900-a322-23f7ea57c5b8</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.OverrideFormalGreeting__c</fieldItem>
                <identifier>Recordflmas_OverrideFormalGreeting_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.OverrideInformalGreeting__c</fieldItem>
                <identifier>Recordflmas_OverrideInformalGreeting_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-b85b8f73-9d61-4dca-87c3-dbd49e7ab17a</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-15832097-0c5d-4900-a322-23f7ea57c5b8</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column9</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-b85b8f73-9d61-4dca-87c3-dbd49e7ab17a</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column10</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-786324a8-4de5-4bfe-bace-af58cf672e46</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.PrimaryEmailType__c</fieldItem>
                <identifier>Recordflmas_PrimaryEmailType_cField2</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.PersonalEmail__pc</fieldItem>
                <identifier>Recordflmas_PersonalEmail_pcField2</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.WorkEmail__pc</fieldItem>
                <identifier>Recordflmas_WorkEmail_pcField2</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.OtherEmail__pc</fieldItem>
                <identifier>Recordflmas_OtherEmail_pcField2</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.AssistantEmail__pc</fieldItem>
                <identifier>Recordflmas_AssistantEmail_pcField2</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-kwhv3xect6</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.PrimaryPhoneType__c</fieldItem>
                <identifier>Recordflmas_PrimaryPhoneType_cField2</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.PersonHomePhone</fieldItem>
                <identifier>RecordPersonHomePhoneField2</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.PersonMobilePhone</fieldItem>
                <identifier>RecordPersonMobilePhoneField2</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.WorkPhone__pc</fieldItem>
                <identifier>Recordflmas_WorkPhone_pcField2</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.PersonOtherPhone</fieldItem>
                <identifier>RecordPersonOtherPhoneField2</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.PersonAssistantPhone</fieldItem>
                <identifier>RecordPersonAssistantPhoneField2</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-7jt0hg6cwf6</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-kwhv3xect6</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column11</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-7jt0hg6cwf6</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column12</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-rjt0tre53wn</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Phone</fieldItem>
                <identifier>RecordPhoneField3</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Fax</fieldItem>
                <identifier>RecordFaxField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Website</fieldItem>
                <identifier>RecordWebsiteField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-46a4d835-0d97-4756-8a05-6d05706d0bcc</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.NumberOfEmployees</fieldItem>
                <identifier>RecordNumberOfEmployeesField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.AnnualRevenue</fieldItem>
                <identifier>RecordAnnualRevenueField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ClientPrimaryAccount__c</fieldItem>
                <identifier>Recordflmas_ClientPrimaryAccount_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-2f72dddd-6a5b-481e-a0d5-209cd5a2738e</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-46a4d835-0d97-4756-8a05-6d05706d0bcc</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column27</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-2f72dddd-6a5b-481e-a0d5-209cd5a2738e</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column28</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-584273f1-390f-4ab7-baed-beb8b3be8f15</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.BillingAddress</fieldItem>
                <identifier>RecordBillingAddressField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-a8ccc9b4-80cc-44bd-a39b-38bf14ed4a60</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ShippingAddress</fieldItem>
                <identifier>RecordShippingAddressField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-d6827f86-e18e-4e7c-9389-0ea733ec873c</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-a8ccc9b4-80cc-44bd-a39b-38bf14ed4a60</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column13</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-d6827f86-e18e-4e7c-9389-0ea733ec873c</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column14</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-a65c0f3c-93c7-487a-9a4d-e309ff7fb0e1</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Description</fieldItem>
                <identifier>RecordDescriptionField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-22f59cdf-afbf-4ecd-926a-9b270e230168</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-22f59cdf-afbf-4ecd-926a-9b270e230168</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column25</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-40714d80-10f9-4e2b-8111-93ef2aec2dc7</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureMembershipCode__pc</fieldItem>
                <identifier>Recordtixtrack_TicketureMembershipCode_pcField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordTypeName__c}</leftValue>
                        <operator>CONTAINS</operator>
                        <rightValue>Person</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureMembershipCode__c</fieldItem>
                <identifier>Recordtixtrack_TicketureMembershipCode_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordTypeName__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Organization</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureIsStaff__pc</fieldItem>
                <identifier>Recordtixtrack_TicketureIsStaff_pcField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordTypeName__c}</leftValue>
                        <operator>CONTAINS</operator>
                        <rightValue>Person</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureCustomerNote2__pc</fieldItem>
                <identifier>Recordtixtrack_TicketureCustomerNote2_pcField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordTypeName__c}</leftValue>
                        <operator>CONTAINS</operator>
                        <rightValue>Person</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureCustomerNote2__c</fieldItem>
                <identifier>Recordtixtrack_TicketureCustomerNote2_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordTypeName__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Organization</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-4aca6389-8ce0-48e1-8529-f1e6468119ff</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureDoNotSyncFrom__pc</fieldItem>
                <identifier>Recordtixtrack_TicketureDoNotSyncFrom_pcField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordTypeName__c}</leftValue>
                        <operator>CONTAINS</operator>
                        <rightValue>Person</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureDoNotSyncFrom__c</fieldItem>
                <identifier>Recordtixtrack_TicketureDoNotSyncFrom_cField2</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordTypeName__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Organization</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureDoNotSync__pc</fieldItem>
                <identifier>Recordtixtrack_TicketureDoNotSync_pcField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordTypeName__c}</leftValue>
                        <operator>CONTAINS</operator>
                        <rightValue>Person</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureDoNotSync__c</fieldItem>
                <identifier>Recordtixtrack_TicketureDoNotSync_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordTypeName__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Organization</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureForgottenCustomer__pc</fieldItem>
                <identifier>Recordtixtrack_TicketureForgottenCustomer_pcField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordTypeName__c}</leftValue>
                        <operator>CONTAINS</operator>
                        <rightValue>Person</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureOccurredAt__pc</fieldItem>
                <identifier>Recordtixtrack_TicketureOccurredAt_pcField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordTypeName__c}</leftValue>
                        <operator>CONTAINS</operator>
                        <rightValue>Person</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureOccurredAt__c</fieldItem>
                <identifier>Recordtixtrack_TicketureOccurredAt_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordTypeName__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Organization</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-6892a4ac-afb1-4edf-9cac-5f38c1aafa19</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-4aca6389-8ce0-48e1-8529-f1e6468119ff</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column15</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-6892a4ac-afb1-4edf-9cac-5f38c1aafa19</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column16</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-443c4c92-78d3-4417-a181-783be60e11c8</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.CreatedById</fieldItem>
                <identifier>RecordCreatedByIdField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LegacySystemId__c</fieldItem>
                <identifier>Recordflmas_LegacySystemId_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureIdentityId__pc</fieldItem>
                <identifier>Recordtixtrack_TicketureIdentityId_pcField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordTypeName__c}</leftValue>
                        <operator>CONTAINS</operator>
                        <rightValue>Person</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureIdentityId__c</fieldItem>
                <identifier>Recordtixtrack_TicketureIdentityId_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordTypeName__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Organization</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureStaffId__pc</fieldItem>
                <identifier>Recordtixtrack_TicketureStaffId_pcField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordTypeName__c}</leftValue>
                        <operator>CONTAINS</operator>
                        <rightValue>Person</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureOrganizationId__c</fieldItem>
                <identifier>Recordtixtrack_TicketureOrganizationId_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordTypeName__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Organization</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-3e00cc89-4e0b-4fe9-a763-054d71bcd4dc</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LastModifiedById</fieldItem>
                <identifier>RecordLastModifiedByIdField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.RecordTypeId</fieldItem>
                <identifier>RecordRecordTypeIdField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.OwnerId</fieldItem>
                <identifier>RecordOwnerIdField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-099874f0-9fb8-4ace-a83b-113a8cc8e95f</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-3e00cc89-4e0b-4fe9-a763-054d71bcd4dc</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column3</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-099874f0-9fb8-4ace-a83b-113a8cc8e95f</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column4</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-3e91730b-e761-4cbc-aa19-a5d8b941a158</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-146c79f8-97ce-452e-b95d-584f5b8a3a08</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Person Account Information</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordTypeName__c}</leftValue>
                        <operator>CONTAINS</operator>
                        <rightValue>Person</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-e5a87f53-b99e-4e92-9849-06acb4f4afcd</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Household Information</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection3</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordTypeName__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Household</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-4e2ff118-aa88-4fdc-bbad-cabafc1d8b97</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Organization Information</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection4</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordTypeName__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Organization</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-786324a8-4de5-4bfe-bace-af58cf672e46</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Greetings</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection5</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.RecordTypeName__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Household</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.RecordTypeName__c}</leftValue>
                        <operator>CONTAINS</operator>
                        <rightValue>Person</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-rjt0tre53wn</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Person Contact Information</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection6</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordType.Name}</leftValue>
                        <operator>CONTAINS</operator>
                        <rightValue>Person</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-584273f1-390f-4ab7-baed-beb8b3be8f15</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Organization Additional Information</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection14</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordTypeName__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Organization</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-a65c0f3c-93c7-487a-9a4d-e309ff7fb0e1</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Address Information</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection7</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-40714d80-10f9-4e2b-8111-93ef2aec2dc7</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Description</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection13</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-443c4c92-78d3-4417-a181-783be60e11c8</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Ticketure Information</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection8</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.RecordTypeName__c}</leftValue>
                        <operator>CONTAINS</operator>
                        <rightValue>Person</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.RecordTypeName__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Organization</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-3e91730b-e761-4cbc-aa19-a5d8b941a158</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>System Information</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection2</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-f2c27765-002e-439d-ab9e-12cbd3db2181</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>New</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>adminFilters</name>
                    <valueList>
                        <valueListItems>
                            <value>Account.ClientPrimaryAccount__c|EQUALS|true</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>maxRecordsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Account.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>Affiliations__pr</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListDisplayType</name>
                    <value>ADVGRID</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListFieldAliases</name>
                    <valueList>
                        <valueListItems>
                            <value>NAME</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Role__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Group__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>SubGroup__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Status__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>StartDate__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>EndDate__c</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListLabel</name>
                    <value>[CLIENT] Affiliations</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldAlias</name>
                    <value>StartDate__c</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldOrder</name>
                    <value>Descending</value>
                </componentInstanceProperties>
                <componentName>lst:dynamicRelatedList</componentName>
                <identifier>lst_dynamicRelatedList6</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordTypeName__c}</leftValue>
                        <operator>CONTAINS</operator>
                        <rightValue>Person</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>New</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>adminFilters</name>
                    <valueList>
                        <valueListItems>
                            <value>Account.ClientPrimaryAccount__c|EQUALS|false</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>maxRecordsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Account.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>Affiliations__pr</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListDisplayType</name>
                    <value>ADVGRID</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListFieldAliases</name>
                    <valueList>
                        <valueListItems>
                            <value>NAME</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Account.NAME</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Status__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Title__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>StartDate__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>EndDate__c</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListLabel</name>
                    <value>Other Organization Affiliations</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldAlias</name>
                    <value>__DEFAULT__</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldOrder</name>
                    <value>Default</value>
                </componentInstanceProperties>
                <componentName>lst:dynamicRelatedList</componentName>
                <identifier>lst_dynamicRelatedList7</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordTypeName__c}</leftValue>
                        <operator>CONTAINS</operator>
                        <rightValue>Person</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>adminFilters</name>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>maxRecordsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Account.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>Affiliations__r</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListDisplayType</name>
                    <value>ADVGRID</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListFieldAliases</name>
                    <valueList>
                        <valueListItems>
                            <value>NAME</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Contact.CNTC_FIRST_NAME</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Contact.NAME</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Title__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>StartDate__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>EndDate__c</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListLabel</name>
                    <value>Affiliated Contacts</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldAlias</name>
                    <value>StartDate__c</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldOrder</name>
                    <value>Descending</value>
                </componentInstanceProperties>
                <componentName>lst:dynamicRelatedList</componentName>
                <identifier>lst_dynamicRelatedList8</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordTypeName__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Organization</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>adminFilters</name>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>maxRecordsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Account.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>Affiliations__r</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListDisplayType</name>
                    <value>ADVGRID</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListFieldAliases</name>
                    <valueList>
                        <valueListItems>
                            <value>NAME</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Contact.CNTC_FIRST_NAME</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Contact.NAME</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Role__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Group__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>SubGroup__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Title__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>StartDate__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>EndDate__c</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListLabel</name>
                    <value>[CLIENT] Affiliated Contacts</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldAlias</name>
                    <value>__DEFAULT__</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldOrder</name>
                    <value>Default</value>
                </componentInstanceProperties>
                <componentName>lst:dynamicRelatedList</componentName>
                <identifier>lst_dynamicRelatedList9</identifier>
                <visibilityRule>
                    <booleanFilter>1 AND 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.RecordTypeName__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Organization</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.ClientPrimaryAccount__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <name>Facet-931aa849-e320-4b1c-b9a1-eb2111800e6f</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TotalGiftAmountAllTime__c</fieldItem>
                <identifier>Recordflmas_TotalGiftAmountAllTime_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TotalGiftAmountThisYear__c</fieldItem>
                <identifier>Recordflmas_TotalGiftAmountThisYear_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TotalGiftAmountLastYear__c</fieldItem>
                <identifier>Recordflmas_TotalGiftAmountLastYear_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TotalGiftAmountLast2Years__c</fieldItem>
                <identifier>Recordflmas_TotalGiftAmountLast2Years_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>flexipage:blankSpace</componentName>
                <identifier>flexipage_blankSpace2</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.FirstGiftAmount__c</fieldItem>
                <identifier>Recordflmas_FirstGiftAmount_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LastGiftAmount__c</fieldItem>
                <identifier>Recordflmas_LastGiftAmount_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.SmallestGiftAmount__c</fieldItem>
                <identifier>Recordflmas_SmallestGiftAmount_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LargestGiftAmount__c</fieldItem>
                <identifier>Recordflmas_LargestGiftAmount_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-ef2cb13b-e6a4-42ad-8a4b-3062f1164940</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TotalGiftCountAllTime__c</fieldItem>
                <identifier>Recordflmas_TotalGiftCountAllTime_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TotalGiftCountThisYear__c</fieldItem>
                <identifier>Recordflmas_TotalGiftCountThisYear_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TotalGiftCountLastYear__c</fieldItem>
                <identifier>Recordflmas_TotalGiftCountLastYear_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TotalGiftCountLast2Years__c</fieldItem>
                <identifier>Recordflmas_TotalGiftCountLast2Years_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>flexipage:blankSpace</componentName>
                <identifier>flexipage_blankSpace3</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.FirstGiftDate__c</fieldItem>
                <identifier>Recordflmas_FirstGiftDate_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LastGiftDate__c</fieldItem>
                <identifier>Recordflmas_LastGiftDate_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.SmallestGiftDate__c</fieldItem>
                <identifier>Recordflmas_SmallestGiftDate_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LargestGiftDate__c</fieldItem>
                <identifier>Recordflmas_LargestGiftDate_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-1ee2ccd2-c1f4-4b71-b241-acc59914f23c</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-ef2cb13b-e6a4-42ad-8a4b-3062f1164940</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column19</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-1ee2ccd2-c1f4-4b71-b241-acc59914f23c</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column20</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-55fbdbfa-6155-45c8-a4cb-0df56a1235c8</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TotalSoftCreditAmountAllTime__c</fieldItem>
                <identifier>Recordflmas_TotalSoftCreditAmountAllTime_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TotalSoftCreditAmountThisYear__c</fieldItem>
                <identifier>Recordflmas_TotalSoftCreditAmountThisYear_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TotalSoftCreditAmountLastYear__c</fieldItem>
                <identifier>Recordflmas_TotalSoftCreditAmountLastYear_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TotalSoftCreditAmountLast2Years__c</fieldItem>
                <identifier>Recordflmas_TotalSoftCreditAmountLast2Years_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>flexipage:blankSpace</componentName>
                <identifier>flexipage_blankSpace4</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.FirstSoftCreditAmount__c</fieldItem>
                <identifier>Recordflmas_FirstSoftCreditAmount_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LastSoftCreditAmount__c</fieldItem>
                <identifier>Recordflmas_LastSoftCreditAmount_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.SmallestSoftCreditAmount__c</fieldItem>
                <identifier>Recordflmas_SmallestSoftCreditAmount_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LargestSoftCreditAmount__c</fieldItem>
                <identifier>Recordflmas_LargestSoftCreditAmount_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-d4414729-9fda-4318-b783-70af5de142c2</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TotalSoftCreditsCountAllTime__c</fieldItem>
                <identifier>Recordflmas_TotalSoftCreditsCountAllTime_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TotalSoftCreditsCountThisYear__c</fieldItem>
                <identifier>Recordflmas_TotalSoftCreditsCountThisYear_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TotalSoftCreditsCountLastYear__c</fieldItem>
                <identifier>Recordflmas_TotalSoftCreditsCountLastYear_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TotalSoftCreditsCountLast2Years__c</fieldItem>
                <identifier>Recordflmas_TotalSoftCreditsCountLast2Years_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>flexipage:blankSpace</componentName>
                <identifier>flexipage_blankSpace5</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.FirstSoftCreditDate__c</fieldItem>
                <identifier>Recordflmas_FirstSoftCreditDate_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LastSoftCreditDate__c</fieldItem>
                <identifier>Recordflmas_LastSoftCreditDate_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.SmallestSoftCreditDate__c</fieldItem>
                <identifier>Recordflmas_SmallestSoftCreditDate_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LargestSoftCreditDate__c</fieldItem>
                <identifier>Recordflmas_LargestSoftCreditDate_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-b2fa9d4b-0311-488d-b1b7-52253d3ce701</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-d4414729-9fda-4318-b783-70af5de142c2</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column23</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-b2fa9d4b-0311-488d-b1b7-52253d3ce701</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column24</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-1d16b7e3-2001-47e1-8c82-1d3fe6d47e08</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-55fbdbfa-6155-45c8-a4cb-0df56a1235c8</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Giving Summary</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection10</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-1d16b7e3-2001-47e1-8c82-1d3fe6d47e08</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Soft Credit Summary</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection12</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>adminFilters</name>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>maxRecordsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Account.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>Opportunities</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListDisplayType</name>
                    <value>ADVGRID</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListFieldAliases</name>
                    <valueList>
                        <valueListItems>
                            <value>OPPORTUNITY.NAME</value>
                        </valueListItems>
                        <valueListItems>
                            <value>OPPORTUNITY.STAGE_NAME</value>
                        </valueListItems>
                        <valueListItems>
                            <value>OPPORTUNITY.AMOUNT</value>
                        </valueListItems>
                        <valueListItems>
                            <value>OPPORTUNITY.CLOSE_DATE</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListLabel</name>
                    <value>Proposals</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldAlias</name>
                    <value>__DEFAULT__</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldOrder</name>
                    <value>Default</value>
                </componentInstanceProperties>
                <componentName>lst:dynamicRelatedList</componentName>
                <identifier>lst_dynamicRelatedList</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>New</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>adminFilters</name>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>maxRecordsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Account.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>GiftCommitments</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListDisplayType</name>
                    <value>ADVGRID</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListFieldAliases</name>
                    <valueList>
                        <valueListItems>
                            <value>Name</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Status</value>
                        </valueListItems>
                        <valueListItems>
                            <value>ExpectedTotalCmtAmount</value>
                        </valueListItems>
                        <valueListItems>
                            <value>TotalPaidTransactio846</value>
                        </valueListItems>
                        <valueListItems>
                            <value>ExpectedEndDate</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListLabel</name>
                    <value>Pledges</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldAlias</name>
                    <value>__DEFAULT__</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldOrder</name>
                    <value>Default</value>
                </componentInstanceProperties>
                <componentName>lst:dynamicRelatedList</componentName>
                <identifier>lst_dynamicRelatedList2</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>New</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>adminFilters</name>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>maxRecordsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Account.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>GiftTransactions</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListDisplayType</name>
                    <value>ADVGRID</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListFieldAliases</name>
                    <valueList>
                        <valueListItems>
                            <value>Name</value>
                        </valueListItems>
                        <valueListItems>
                            <value>CurrentAmount</value>
                        </valueListItems>
                        <valueListItems>
                            <value>TransactionDueDate</value>
                        </valueListItems>
                        <valueListItems>
                            <value>TransactionDate</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Status</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListLabel</name>
                    <value>Gift Transactions</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldAlias</name>
                    <value>__DEFAULT__</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldOrder</name>
                    <value>Default</value>
                </componentInstanceProperties>
                <componentName>lst:dynamicRelatedList</componentName>
                <identifier>lst_dynamicRelatedList3</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-b39e9936-87e3-4d5b-a8f4-58c20455ae09</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureMembershipCode__c</fieldItem>
                <identifier>Recordtixtrack_TicketureMembershipCode_cField2</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-17e671bc-8387-4d6c-98b8-d8037db9791a</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.CurrentCycleMembership__c</fieldItem>
                <identifier>Recordflmas_CurrentCycleMembership_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.MostRecentMembership__c</fieldItem>
                <identifier>Recordflmas_MostRecentMembership_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-75098786-e748-481a-9866-568b2c00ee3e</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-17e671bc-8387-4d6c-98b8-d8037db9791a</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column21</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-75098786-e748-481a-9866-568b2c00ee3e</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column22</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-7ec7f110-50cf-476b-a381-07fc1ea45696</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-7ec7f110-50cf-476b-a381-07fc1ea45696</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Membership Information</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection11</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>adminFilters</name>
                    <valueList>
                        <valueListItems>
                            <value>MembershipLevel__c|NOT_EQUAL|</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>maxRecordsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Account.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>tixtrack__TixTrackOrders__pr</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListDisplayType</name>
                    <value>ADVGRID</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListFieldAliases</name>
                    <valueList>
                        <valueListItems>
                            <value>OPPORTUNITY.NAME</value>
                        </valueListItems>
                        <valueListItems>
                            <value>MembershipLevel__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>tixtrack__TixTrackMembershipStartDate__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>tixtrack__TixTrackMembershipEndDate__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>tixtrack__TicketureNamedMember2__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>MembershipOrigin__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>MembershipChangeType__c</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListLabel</name>
                    <value>My Memberships</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldAlias</name>
                    <value>tixtrack__TixTrackMembershipEndDate__c</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldOrder</name>
                    <value>Descending</value>
                </componentInstanceProperties>
                <componentName>lst:dynamicRelatedList</componentName>
                <identifier>lst_dynamicRelatedList4</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordTypeName__c}</leftValue>
                        <operator>CONTAINS</operator>
                        <rightValue>Person</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>adminFilters</name>
                    <valueList>
                        <valueListItems>
                            <value>MembershipLevel__c|NOT_EQUAL|</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>maxRecordsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Account.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>Opportunities</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListDisplayType</name>
                    <value>ADVGRID</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListFieldAliases</name>
                    <valueList>
                        <valueListItems>
                            <value>OPPORTUNITY.NAME</value>
                        </valueListItems>
                        <valueListItems>
                            <value>MembershipLevel__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>tixtrack__TixTrackMembershipStartDate__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>tixtrack__TixTrackMembershipEndDate__c</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListLabel</name>
                    <value>Corporate Memberships</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldAlias</name>
                    <value>tixtrack__TixTrackMembershipEndDate__c</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldOrder</name>
                    <value>Descending</value>
                </componentInstanceProperties>
                <componentName>lst:dynamicRelatedList</componentName>
                <identifier>lst_dynamicRelatedList5</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordTypeName__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Organization</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <name>Facet-wezr2qhzqv</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.PersonHasOptedOutOfEmail</fieldItem>
                <identifier>RecordPersonHasOptedOutOfEmailField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.PersonDoNotCall</fieldItem>
                <identifier>RecordPersonDoNotCallField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-bcf4b3bd-6291-44c8-9b1c-776cb4ab218d</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TicketureEmailOptIn__pc</fieldItem>
                <identifier>Recordflmas_TicketureEmailOptIn_pcField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-8370d5c8-ce43-4880-8ad8-6caaef606dec</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-bcf4b3bd-6291-44c8-9b1c-776cb4ab218d</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column17</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-8370d5c8-ce43-4880-8ad8-6caaef606dec</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column18</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-1e5fd229-f01b-4815-b012-0d2ec2c04f1d</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-1e5fd229-f01b-4815-b012-0d2ec2c04f1d</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Communication Preferences</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection9</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordTypeName__c}</leftValue>
                        <operator>CONTAINS</operator>
                        <rightValue>Person</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Account.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>ContactPointPhones</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListComponentOverride</name>
                    <value>NONE</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>rowsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentName>force:relatedListSingleContainer</componentName>
                <identifier>force_relatedListSingleContainer</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Account.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>ContactPointEmails</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListComponentOverride</name>
                    <value>NONE</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>rowsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentName>force:relatedListSingleContainer</componentName>
                <identifier>force_relatedListSingleContainer2</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Account.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>ContactPointAddresses</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListComponentOverride</name>
                    <value>NONE</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>rowsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentName>force:relatedListSingleContainer</componentName>
                <identifier>force_relatedListSingleContainer3</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-7f3f1480-dff8-43c5-a722-a33e16b1439c</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>New</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>adminFilters</name>
                    <valueList>
                        <valueListItems>
                            <value>ExpirationDate__c|GREATER_OR_EQUAL|TODAY</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>maxRecordsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Account.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>Benefits__r</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListDisplayType</name>
                    <value>ADVGRID</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListFieldAliases</name>
                    <valueList>
                        <valueListItems>
                            <value>NAME</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Category__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>EffectiveDate__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>ExpirationDate__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>QuantityEntitled__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>QuantityIssued__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>QuantityRemaining__c</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListLabel</name>
                    <value>Current Benefits</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldAlias</name>
                    <value>__DEFAULT__</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldOrder</name>
                    <value>Default</value>
                </componentInstanceProperties>
                <componentName>lst:dynamicRelatedList</componentName>
                <identifier>lst_dynamicRelatedList10</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-e9227c21-b5c8-4282-a053-5a5549364ea2</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>adminFilters</name>
                    <valueList>
                        <valueListItems>
                            <value>tixtrack__TicketureCartId__c|NOT_EQUAL|</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>maxRecordsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Account.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>Opportunities</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListDisplayType</name>
                    <value>ADVGRID</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListFieldAliases</name>
                    <valueList>
                        <valueListItems>
                            <value>OPPORTUNITY.NAME</value>
                        </valueListItems>
                        <valueListItems>
                            <value>tixtrack__TicketureCartNumber__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>OPPORTUNITY.AMOUNT</value>
                        </valueListItems>
                        <valueListItems>
                            <value>OPPORTUNITY.CLOSE_DATE</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListLabel</name>
                    <value>Purchases</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldAlias</name>
                    <value>OPPORTUNITY.CLOSE_DATE</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldOrder</name>
                    <value>Descending</value>
                </componentInstanceProperties>
                <componentName>lst:dynamicRelatedList</componentName>
                <identifier>lst_dynamicRelatedList11</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-db6ceaef-4fba-4acc-89b8-6c9933ae39e8</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>active</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-f2c27765-002e-439d-ab9e-12cbd3db2181</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Standard.Tab.detail</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab2</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-931aa849-e320-4b1c-b9a1-eb2111800e6f</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Affiliations</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab6</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-b39e9936-87e3-4d5b-a8f4-58c20455ae09</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Gifts</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab3</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-wezr2qhzqv</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Membership</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab4</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-7f3f1480-dff8-43c5-a722-a33e16b1439c</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Communication</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab5</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-e9227c21-b5c8-4282-a053-5a5549364ea2</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Benefits</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab8</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-db6ceaef-4fba-4acc-89b8-6c9933ae39e8</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Purchases</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-d9bad689-f2ee-4c8c-93d4-94800f90f22b</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>tabs</name>
                    <value>Facet-d9bad689-f2ee-4c8c-93d4-94800f90f22b</value>
                </componentInstanceProperties>
                <componentName>flexipage:tabset</componentName>
                <identifier>flexipage_tabset</identifier>
            </componentInstance>
        </itemInstances>
        <name>leftcol</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentName>forceChatter:recordFeedContainer</componentName>
                <identifier>forceChatter_recordFeedContainer</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-69fadbc4-9c9a-48d5-bbdc-f7bea250be09</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>showLegacyActivityComposer</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentName>runtime_sales_activities:activityPanel</componentName>
                <identifier>runtime_sales_activities_activityPanel</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-e3b18220-d8db-40b5-bdeb-821060ba2a7b</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Account.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>AttachedContentNotes</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListComponentOverride</name>
                    <value>NONE</value>
                </componentInstanceProperties>
                <componentName>force:relatedListSingleContainer</componentName>
                <identifier>force_relatedListSingleContainer5</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-b3bb5147-e194-4140-8a10-8d3d3f88911d</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Account.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>AttachedContentDocuments</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListComponentOverride</name>
                    <value>NONE</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>rowsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentName>force:relatedListSingleContainer</componentName>
                <identifier>force_relatedListSingleContainer6</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-l2e6m9ldw9a</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>active</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-69fadbc4-9c9a-48d5-bbdc-f7bea250be09</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Standard.Tab.collaborate</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab9</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-e3b18220-d8db-40b5-bdeb-821060ba2a7b</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Standard.Tab.activity</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab10</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-b3bb5147-e194-4140-8a10-8d3d3f88911d</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Standard.Tab.crisisCenterNotes</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab11</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-l2e6m9ldw9a</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Files</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab12</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-0eefdb82-0fb1-4bbe-ac4a-bf7c513554b2</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>displayOption</name>
                    <value>BOTH</value>
                </componentInstanceProperties>
                <componentName>runtime_sales_merge:mergeCandidatesPreviewCard</componentName>
                <identifier>runtime_sales_merge_mergeCandidatesPreviewCard</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>New</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>maxRecordsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Account.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>AARAccounts</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListDisplayType</name>
                    <value>ADVGRID</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListFieldAliases</name>
                    <valueList>
                        <valueListItems>
                            <value>Name</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Account</value>
                        </valueListItems>
                        <valueListItems>
                            <value>RelatedAccount</value>
                        </valueListItems>
                        <valueListItems>
                            <value>PartyRoleRelation</value>
                        </valueListItems>
                        <valueListItems>
                            <value>StartDate</value>
                        </valueListItems>
                        <valueListItems>
                            <value>EndDate</value>
                        </valueListItems>
                        <valueListItems>
                            <value>IsActive</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListLabel</name>
                    <value>Account Account Relationships (Account)</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldAlias</name>
                    <value>__DEFAULT__</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldOrder</name>
                    <value>Default</value>
                </componentInstanceProperties>
                <componentName>lst:dynamicRelatedList</componentName>
                <identifier>lst_dynamicRelatedList14</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.RecordTypeName__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Household</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.RecordTypeName__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Organization</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>New</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>adminFilters</name>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>maxRecordsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Account.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>PersonCCRContacts</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListDisplayType</name>
                    <value>ADVGRID</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListFieldAliases</name>
                    <valueList>
                        <valueListItems>
                            <value>RelatedContact</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Type__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>RelationshipExplanation__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Status__c</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListLabel</name>
                    <value>Contact Contact Relationships</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldAlias</name>
                    <value>__DEFAULT__</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldOrder</name>
                    <value>Default</value>
                </componentInstanceProperties>
                <componentName>lst:dynamicRelatedList</componentName>
                <identifier>lst_dynamicRelatedList13</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordTypeName__c}</leftValue>
                        <operator>CONTAINS</operator>
                        <rightValue>Person</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>NewContact</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>adminFilters</name>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>maxRecordsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Account.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>HouseholdMembers__r</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListDisplayType</name>
                    <value>ADVGRID</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListFieldAliases</name>
                    <valueList>
                        <valueListItems>
                            <value>FULL_NAME</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Nickname__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>CONTACT.PHONE1</value>
                        </valueListItems>
                        <valueListItems>
                            <value>CONTACT.EMAIL</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListLabel</name>
                    <value>Household Members</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldAlias</name>
                    <value>__DEFAULT__</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldOrder</name>
                    <value>Default</value>
                </componentInstanceProperties>
                <componentName>lst:dynamicRelatedList</componentName>
                <identifier>lst_dynamicRelatedList12</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordTypeName__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Household</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>tabs</name>
                    <value>Facet-0eefdb82-0fb1-4bbe-ac4a-bf7c513554b2</value>
                </componentInstanceProperties>
                <componentName>flexipage:tabset</componentName>
                <identifier>flexipage_tabset2</identifier>
            </componentInstance>
        </itemInstances>
        <name>rightcol</name>
        <type>Region</type>
    </flexiPageRegions>
    <masterLabel>A&amp;C Account Record Page</masterLabel>
    <sobjectType>Account</sobjectType>
    <template>
        <name>flexipage:recordHomeTwoColEqualHeaderTemplateDesktop</name>
        <properties>
            <name>enablePageActionConfig</name>
            <value>false</value>
        </properties>
        <properties>
            <name>saveOptions</name>
            <value>[{&quot;name&quot;:&quot;UseDefaultAssignmentRule&quot;,&quot;value&quot;:&quot;NONE&quot;}]</value>
        </properties>
    </template>
    <type>RecordPage</type>
</FlexiPage>
