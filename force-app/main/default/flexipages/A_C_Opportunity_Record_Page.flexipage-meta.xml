<?xml version="1.0" encoding="UTF-8"?>
<FlexiPage xmlns="http://soap.sforce.com/2006/04/metadata">
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>Edit</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Delete</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Opportunity.tixtrack__CreateCart</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Opportunity.tixtrack__ViewInTicketure</value>
                            <visibilityRule>
                                <criteria>
                                    <leftValue>{!Record.tixtrack__TicketureCartId__c}</leftValue>
                                    <operator>NE</operator>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>Clone</value>
                        </valueListItems>
                        <valueListItems>
                            <value>DeepClone</value>
                        </valueListItems>
                        <valueListItems>
                            <value>ChangeOwnerOne</value>
                        </valueListItems>
                        <valueListItems>
                            <value>ChangeRecordType</value>
                        </valueListItems>
                        <valueListItems>
                            <value>PrintableView</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Submit</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Opportunity.GenerateLedgers</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Opportunity.TAccountBalance</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>collapsed</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>enableActionsConfiguration</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>enableActionsInNative</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>hideChatterActions</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>numVisibleActions</name>
                    <value>5</value>
                </componentInstanceProperties>
                <componentName>force:highlightsPanel</componentName>
                <identifier>force_highlightsPanel</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>hideUpdateButton</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>variant</name>
                    <value>linear</value>
                </componentInstanceProperties>
                <componentName>runtime_sales_pathassistant:pathAssistant</componentName>
                <identifier>runtime_sales_pathassistant_pathAssistant</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>hideHeader</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentName>force:relatedListQuickLinksContainer</componentName>
                <identifier>force_relatedListQuickLinksContainer</identifier>
            </componentInstance>
        </itemInstances>
        <name>header</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Name</fieldItem>
                <identifier>RecordNameField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.CloseDate</fieldItem>
                <identifier>RecordCloseDateField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.AccountId</fieldItem>
                <identifier>RecordAccountIdField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TixTrackPrimaryContact__c</fieldItem>
                <identifier>Recordtixtrack_TixTrackPrimaryContact_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Type</fieldItem>
                <identifier>RecordTypeField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LeadSource</fieldItem>
                <identifier>RecordLeadSourceField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.CampaignId</fieldItem>
                <identifier>RecordCampaignIdField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TotalFairMarketValue__c</fieldItem>
                <identifier>Recordflmas_TotalFairMarketValue_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TotalTaxDeductibleAmount__c</fieldItem>
                <identifier>Recordflmas_TotalTaxDeductibleAmount_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.MembershipGreeting__c</fieldItem>
                <identifier>Recordflmas__MembershipGreeting__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-83e8f361-cbc8-4adc-88a7-7ace1d3f6877</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.StageName</fieldItem>
                <identifier>RecordStageNameField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Amount</fieldItem>
                <identifier>RecordAmountField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ExpectedRevenue</fieldItem>
                <identifier>RecordExpectedRevenueField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.NextStep</fieldItem>
                <identifier>RecordNextStepField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Probability</fieldItem>
                <identifier>RecordProbabilityField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ForecastCategoryName</fieldItem>
                <identifier>RecordForecastCategoryNameField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.RevenueType__c</fieldItem>
                <identifier>Recordflmas_RevenueType_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.IsReceivable__c</fieldItem>
                <identifier>Recordflmas_IsReceivable_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TotalTaxDeductibleAmount__c</fieldItem>
                <identifier>Recordflmas_TotalTaxDeductibleAmount_cField2</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TotalFairMarketValue__c</fieldItem>
                <identifier>Recordflmas_TotalFairMarketValue_cField2</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-882b7a10-2024-4957-af60-c040ec986e2f</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-83e8f361-cbc8-4adc-88a7-7ace1d3f6877</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-882b7a10-2024-4957-af60-c040ec986e2f</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column2</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-c212961c-d9cf-4d3f-82ec-49f180f4389a</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Description</fieldItem>
                <identifier>RecordDescriptionField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-4b5644dc-4d4b-4eaa-9a3e-2f0d7086597a</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-4b5644dc-4d4b-4eaa-9a3e-2f0d7086597a</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column3</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-219e919f-e54e-4fd3-bf46-b092924fb4d9</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.CreatedById</fieldItem>
                <identifier>RecordCreatedByIdField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.OwnerId</fieldItem>
                <identifier>RecordOwnerIdField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.RecordTypeId</fieldItem>
                <identifier>RecordRecordTypeIdField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-646fbfa2-cd88-45ec-9249-c176a5a4c1a8</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LastModifiedById</fieldItem>
                <identifier>RecordLastModifiedByIdField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LegacySystemId__c</fieldItem>
                <identifier>Recordflmas_LegacySystemId_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureOccurredAt__c</fieldItem>
                <identifier>Recordtixtrack_TicketureOccurredAt_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-84a6e7a8-3773-4d8c-b585-09b6cc81a7ac</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-646fbfa2-cd88-45ec-9249-c176a5a4c1a8</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column4</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-84a6e7a8-3773-4d8c-b585-09b6cc81a7ac</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column5</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-ddcc443c-1428-4085-88df-1aabc9e9c70f</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-c212961c-d9cf-4d3f-82ec-49f180f4389a</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Opportunity Information</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-219e919f-e54e-4fd3-bf46-b092924fb4d9</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Description</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection2</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-ddcc443c-1428-4085-88df-1aabc9e9c70f</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>System Information</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection3</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-9088b939-c7e8-4c05-968f-977cb3133840</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.IsMembership__c</fieldItem>
                <identifier>Recordflmas_IsMembership_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TixTrackMembershipStartDate__c</fieldItem>
                <identifier>Recordtixtrack_TixTrackMembershipStartDate_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.IsMembership__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.MembershipOrigin__c</fieldItem>
                <identifier>Recordflmas_MembershipOrigin_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.IsMembership__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.PriorMembership__c</fieldItem>
                <identifier>Recordflmas_PriorMembership_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.IsMembership__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.IsMembershipRefunded__c</fieldItem>
                <identifier>Recordflmas_IsMembershipRefunded_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.MembershipLastRedeemed__c</fieldItem>
                <identifier>Recordflmas_MembershipLastRedeemed_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.IsMembership__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-fb668bb0-0fe3-4cd2-8bec-29a286126476</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TixTrackMemberLevel__c</fieldItem>
                <identifier>Recordtixtrack_TixTrackMemberLevel_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.IsMembership__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TixTrackMembershipEndDate__c</fieldItem>
                <identifier>Recordtixtrack_TixTrackMembershipEndDate_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.IsMembership__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.MembershipChangeType__c</fieldItem>
                <identifier>Recordflmas_MembershipChangeType_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.IsMembership__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.NextMembership__c</fieldItem>
                <identifier>Recordflmas_NextMembership_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.IsMembership__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.IsMembershipChangedMidCycle__c</fieldItem>
                <identifier>Recordflmas_IsMembershipChangedMidCycle_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-545b72fe-764e-405a-bd99-c26b1946bc1a</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-fb668bb0-0fe3-4cd2-8bec-29a286126476</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column6</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-545b72fe-764e-405a-bd99-c26b1946bc1a</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column7</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-d56d7ab7-02fc-49b1-be13-fd4ce7ef5de7</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureGiftMembershipPurchase__c</fieldItem>
                <identifier>Recordtixtrack_TicketureGiftMembershipPurchase_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.IsMembership__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-5a22adca-0a06-4824-a5df-2f897b2941e5</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureGiver__c</fieldItem>
                <identifier>Recordtixtrack_TicketureGiver_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.IsMembership__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-bde59742-68d2-4e2f-ab60-0adf27c5bcfc</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-5a22adca-0a06-4824-a5df-2f897b2941e5</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column18</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-bde59742-68d2-4e2f-ab60-0adf27c5bcfc</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column19</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-a804d101-a48a-434c-8890-9bfe5239b568</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureEnrolledInAutoRenew__c</fieldItem>
                <identifier>Recordtixtrack_TicketureEnrolledInAutoRenew_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.IsMembership__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-b09000e0-d2d8-4d72-b494-cef4414a4781</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureScheduledAutoRenewDateTime__c</fieldItem>
                <identifier>Recordtixtrack_TicketureScheduledAutoRenewDateTime_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.tixtrack__TicketureEnrolledInAutoRenew__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureRenewalMembership__c</fieldItem>
                <identifier>Recordtixtrack_TicketureRenewalMembership_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.tixtrack__TicketureEnrolledInAutoRenew__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-fb42c6ce-5004-4874-b546-6188cf611759</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-b09000e0-d2d8-4d72-b494-cef4414a4781</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column8</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-fb42c6ce-5004-4874-b546-6188cf611759</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column9</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-8f238cdc-d23b-4fcc-a890-86c9ecc2e48c</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TixTrackPrimaryContact__c</fieldItem>
                <identifier>Recordtixtrack_TixTrackPrimaryContact_cField2</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.IsMembership__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureNamedMember2__c</fieldItem>
                <identifier>Recordtixtrack_TicketureNamedMember2_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.IsMembership__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureNamedMember3__c</fieldItem>
                <identifier>Recordtixtrack_TicketureNamedMember3_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.IsMembership__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureNamedMember4__c</fieldItem>
                <identifier>Recordtixtrack_TicketureNamedMember4_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.IsMembership__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureNamedMember5__c</fieldItem>
                <identifier>Recordtixtrack_TicketureNamedMember5_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.IsMembership__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureNamedMember6__c</fieldItem>
                <identifier>Recordtixtrack_TicketureNamedMember6_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.IsMembership__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-a2f25cc3-c1c6-41a2-9b79-cb4ea369c59f</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketurePrimaryMemberType__c</fieldItem>
                <identifier>Recordtixtrack_TicketurePrimaryMemberType_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.IsMembership__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureNamedMember2Type__c</fieldItem>
                <identifier>Recordtixtrack_TicketureNamedMember2Type_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.IsMembership__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureNamedMember3Type__c</fieldItem>
                <identifier>Recordtixtrack_TicketureNamedMember3Type_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.IsMembership__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureNamedMember4Type__c</fieldItem>
                <identifier>Recordtixtrack_TicketureNamedMember4Type_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.IsMembership__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureNamedMember5Type__c</fieldItem>
                <identifier>Recordtixtrack_TicketureNamedMember5Type_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.IsMembership__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureNamedMember6Type__c</fieldItem>
                <identifier>Recordtixtrack_TicketureNamedMember6Type_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.IsMembership__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-4f1721dd-ce50-4511-8eb9-e75ecb477768</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-a2f25cc3-c1c6-41a2-9b79-cb4ea369c59f</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column10</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-4f1721dd-ce50-4511-8eb9-e75ecb477768</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column11</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-3fa87fe9-3cc1-4f4a-9190-76b8317c49b3</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketurePrimaryMemberPrice__c</fieldItem>
                <identifier>Recordtixtrack_TicketurePrimaryMemberPrice_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureNamedMember2Price__c</fieldItem>
                <identifier>Recordtixtrack_TicketureNamedMember2Price_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureNamedMember3Price__c</fieldItem>
                <identifier>Recordtixtrack_TicketureNamedMember3Price_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureNamedMember4Price__c</fieldItem>
                <identifier>Recordtixtrack_TicketureNamedMember4Price_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureNamedMember5Price__c</fieldItem>
                <identifier>Recordtixtrack_TicketureNamedMember5Price_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureNamedMember6Price__c</fieldItem>
                <identifier>Recordtixtrack_TicketureNamedMember6Price_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>flexipage:blankSpace</componentName>
                <identifier>flexipage_blankSpace12</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureMigrationTicketType1__c</fieldItem>
                <identifier>Recordtixtrack_TicketureMigrationTicketType1_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>flexipage:blankSpace</componentName>
                <identifier>flexipage_blankSpace2</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureMigrationTicketType2__c</fieldItem>
                <identifier>Recordtixtrack_TicketureMigrationTicketType2_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>flexipage:blankSpace</componentName>
                <identifier>flexipage_blankSpace3</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureMigrationTicketType3__c</fieldItem>
                <identifier>Recordtixtrack_TicketureMigrationTicketType3_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>flexipage:blankSpace</componentName>
                <identifier>flexipage_blankSpace4</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureMigrationTicketType4__c</fieldItem>
                <identifier>Recordtixtrack_TicketureMigrationTicketType4_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>flexipage:blankSpace</componentName>
                <identifier>flexipage_blankSpace5</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureMigrationTicketType5__c</fieldItem>
                <identifier>Recordtixtrack_TicketureMigrationTicketType5_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-90c4d423-a102-4656-a68d-315af58057fd</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentName>flexipage:blankSpace</componentName>
                <identifier>flexipage_blankSpace6</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>flexipage:blankSpace</componentName>
                <identifier>flexipage_blankSpace7</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>flexipage:blankSpace</componentName>
                <identifier>flexipage_blankSpace8</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>flexipage:blankSpace</componentName>
                <identifier>flexipage_blankSpace9</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>flexipage:blankSpace</componentName>
                <identifier>flexipage_blankSpace10</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>flexipage:blankSpace</componentName>
                <identifier>flexipage_blankSpace11</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>flexipage:blankSpace</componentName>
                <identifier>flexipage_blankSpace13</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureMigrationTicketType1Qty__c</fieldItem>
                <identifier>Recordtixtrack_TicketureMigrationTicketType1Qty_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureMigrationTicketType1Price__c</fieldItem>
                <identifier>Recordtixtrack_TicketureMigrationTicketType1Price_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureMigrationTicketType2Qty__c</fieldItem>
                <identifier>Recordtixtrack_TicketureMigrationTicketType2Qty_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureMigrationTicketType2Price__c</fieldItem>
                <identifier>Recordtixtrack_TicketureMigrationTicketType2Price_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureMigrationTicketType3Qty__c</fieldItem>
                <identifier>Recordtixtrack_TicketureMigrationTicketType3Qty_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureMigrationTicketType3Price__c</fieldItem>
                <identifier>Recordtixtrack_TicketureMigrationTicketType3Price_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureMigrationTicketType4Qty__c</fieldItem>
                <identifier>Recordtixtrack_TicketureMigrationTicketType4Qty_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureMigrationTicketType4Price__c</fieldItem>
                <identifier>Recordtixtrack_TicketureMigrationTicketType4Price_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureMigrationTicketType5Qty__c</fieldItem>
                <identifier>Recordtixtrack_TicketureMigrationTicketType5Qty_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureMigrationTicketType5Price__c</fieldItem>
                <identifier>Recordtixtrack_TicketureMigrationTicketType5Price_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-4d94a067-1082-4e6d-91f0-65274daba8e7</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-90c4d423-a102-4656-a68d-315af58057fd</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column12</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-4d94a067-1082-4e6d-91f0-65274daba8e7</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column13</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-e4c3f15c-a9a8-4616-a00f-3a151fb3650d</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-d56d7ab7-02fc-49b1-be13-fd4ce7ef5de7</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Membership Information</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection4</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-a804d101-a48a-434c-8890-9bfe5239b568</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Gift of Membership</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection10</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-8f238cdc-d23b-4fcc-a890-86c9ecc2e48c</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Auto-Renewal Information</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection5</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-3fa87fe9-3cc1-4f4a-9190-76b8317c49b3</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Members</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection6</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-e4c3f15c-a9a8-4616-a00f-3a151fb3650d</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Data Migration</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection7</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.IsMembership__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <name>Facet-1b6d253c-bc56-4efc-a117-1e21a9a86ffe</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureGiftedMembership__c</fieldItem>
                <identifier>Recordtixtrack_TicketureGiftedMembership_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-634df9fb-c005-40e6-a025-fede8c59f6c4</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureRecipient__c</fieldItem>
                <identifier>Recordtixtrack_TicketureRecipient_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-dff693e2-1d9d-438e-95da-0c5b894e560f</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-634df9fb-c005-40e6-a025-fede8c59f6c4</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column16</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-dff693e2-1d9d-438e-95da-0c5b894e560f</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column17</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-83ffc92f-29cf-417a-8be3-0426d40aa733</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TotalPurchasesAmount__c</fieldItem>
                <identifier>Recordflmas_TotalPurchasesAmount_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TotalMembershipAmount__c</fieldItem>
                <identifier>Recordflmas_TotalMembershipAmount_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TotalGiftsAmount__c</fieldItem>
                <identifier>Recordflmas_TotalGiftsDonationsAmount_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureSeller__c</fieldItem>
                <identifier>Recordtixtrack_TicketureSeller_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureCartNumber__c</fieldItem>
                <identifier>Recordtixtrack_TicketureCartNumber_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureOrderNumber__c</fieldItem>
                <identifier>Recordtixtrack_TicketureOrderNumber_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureStaffName__c</fieldItem>
                <identifier>Recordtixtrack_TicketureStaffName_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureStaffEmail__c</fieldItem>
                <identifier>Recordtixtrack_TicketureStaffEmail_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-989c879f-57d7-42cf-a104-7516869057cf</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentName>flexipage:blankSpace</componentName>
                <identifier>flexipage_blankSpace15</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>flexipage:blankSpace</componentName>
                <identifier>flexipage_blankSpace16</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>flexipage:blankSpace</componentName>
                <identifier>flexipage_blankSpace17</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureChannel__c</fieldItem>
                <identifier>Recordtixtrack_TicketureChannel_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureCartId__c</fieldItem>
                <identifier>Recordtixtrack_TicketureCartId_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>flexipage:blankSpace</componentName>
                <identifier>flexipage_blankSpace14</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureStaffId__c</fieldItem>
                <identifier>Recordtixtrack_TicketureStaffId_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.tixtrack__TicketureSyncOptions__c</fieldItem>
                <identifier>Recordtixtrack_TicketureSyncOptions_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-6fc70b89-7a41-46c4-abc0-a64cdd3dbd33</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-989c879f-57d7-42cf-a104-7516869057cf</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column14</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-6fc70b89-7a41-46c4-abc0-a64cdd3dbd33</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column15</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-07707a9c-0392-45fb-8452-1162df300a26</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-83ffc92f-29cf-417a-8be3-0426d40aa733</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Gifted Membership</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection9</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-07707a9c-0392-45fb-8452-1162df300a26</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Ticketure Information</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection8</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-t9h1jibiqq</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-9088b939-c7e8-4c05-968f-977cb3133840</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Standard.Tab.detail</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab2</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-1b6d253c-bc56-4efc-a117-1e21a9a86ffe</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Membership</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-t9h1jibiqq</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Ticketure</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab3</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.tixtrack__TicketureCartId__c}</leftValue>
                        <operator>NE</operator>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <name>Facet-20d62f7a-05c6-4a50-9280-94ba4de62615</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>tabs</name>
                    <value>Facet-20d62f7a-05c6-4a50-9280-94ba4de62615</value>
                </componentInstanceProperties>
                <componentName>flexipage:tabset</componentName>
                <identifier>flexipage_tabset</identifier>
            </componentInstance>
        </itemInstances>
        <name>main</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentName>forceChatter:recordFeedContainer</componentName>
                <identifier>forceChatter_recordFeedContainer</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-b0799c95-531b-402c-8744-fda871ab0a32</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>showLegacyActivityComposer</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentName>runtime_sales_activities:activityPanel</componentName>
                <identifier>runtime_sales_activities_activityPanel</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-ea535d27-20fc-41db-a611-4c38ffe90f40</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>active</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-b0799c95-531b-402c-8744-fda871ab0a32</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Standard.Tab.collaborate</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab4</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-ea535d27-20fc-41db-a611-4c38ffe90f40</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Standard.Tab.activity</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab5</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-69915155-031d-4fc4-977b-57522971a6fc</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>tabs</name>
                    <value>Facet-69915155-031d-4fc4-977b-57522971a6fc</value>
                </componentInstanceProperties>
                <componentName>flexipage:tabset</componentName>
                <identifier>flexipage_tabset2</identifier>
            </componentInstance>
        </itemInstances>
        <name>sidebar</name>
        <type>Region</type>
    </flexiPageRegions>
    <masterLabel>A&amp;C Opportunity Record Page</masterLabel>
    <sobjectType>Opportunity</sobjectType>
    <template>
        <name>flexipage:recordHomeTemplateDesktop</name>
        <properties>
            <name>enablePageActionConfig</name>
            <value>false</value>
        </properties>
    </template>
    <type>RecordPage</type>
</FlexiPage>
