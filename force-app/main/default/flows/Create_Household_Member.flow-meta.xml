<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>61.0</apiVersion>
    <choices>
        <name>Other</name>
        <choiceText>Other</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Other</stringValue>
        </value>
    </choices>
    <choices>
        <name>Primary</name>
        <choiceText>Primary</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Primary</stringValue>
        </value>
    </choices>
    <choices>
        <name>Secondary</name>
        <choiceText>Secondary</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Secondary</stringValue>
        </value>
    </choices>
    <decisions>
        <name>New_Household_Member_Is_Primary</name>
        <label>New Household Member Is Primary</label>
        <locationX>248</locationX>
        <locationY>566</locationY>
        <defaultConnector>
            <targetReference>New_Household_Member_Is_Secondary</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Member_Type</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>Primary</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Primary_Lookup_on_Household</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>New_Household_Member_Is_Secondary</name>
        <label>New Household Member Is Secondary</label>
        <locationX>446</locationX>
        <locationY>674</locationY>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Member_Type</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>Secondary</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Secondary_Lookup_on_Household</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>Create Household Member {!$Flow.CurrentDateTime}</interviewLabel>
    <isOverridable>true</isOverridable>
    <label>Create Household Member</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <name>Create_Person_Account</name>
        <label>Create Person Account</label>
        <locationX>248</locationX>
        <locationY>350</locationY>
        <connector>
            <targetReference>Get_the_New_Person_Account</targetReference>
        </connector>
        <inputAssignments>
            <field>ExcludefromHouseholdNaming__pc</field>
            <value>
                <elementReference>Exclude_from_Houshold_Naming</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>FirstName</field>
            <value>
                <elementReference>First_Name</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>LastName</field>
            <value>
                <elementReference>Last_Name</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>PrimaryHousehold__pc</field>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>getPersonRTId.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <name>Get_the_New_Person_Account</name>
        <label>Get the New Person Account</label>
        <locationX>248</locationX>
        <locationY>458</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>New_Household_Member_Is_Primary</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Create_Person_Account</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getPersonRTId</name>
        <label>getPersonRTId</label>
        <locationX>248</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Create_Person_Account</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Person</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Primary_Lookup_on_Household</name>
        <label>Update Primary Lookup on Household</label>
        <locationX>50</locationX>
        <locationY>674</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>PrimaryContact__c</field>
            <value>
                <elementReference>Get_the_New_Person_Account.PersonContactId</elementReference>
            </value>
        </inputAssignments>
        <object>Account</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Secondary_Lookup_on_Household</name>
        <label>Update Secondary Lookup on Household</label>
        <locationX>314</locationX>
        <locationY>782</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>SecondaryContact__c</field>
            <value>
                <elementReference>Get_the_New_Person_Account.PersonContactId</elementReference>
            </value>
        </inputAssignments>
        <object>Account</object>
    </recordUpdates>
    <screens>
        <name>Create_Household_Member</name>
        <label>Create Household Member</label>
        <locationX>248</locationX>
        <locationY>134</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>getPersonRTId</targetReference>
        </connector>
        <fields>
            <name>Create_Household_Member_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Create_Household_Member_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>First_Name</name>
                    <dataType>String</dataType>
                    <fieldText>First Name</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Create_Household_Member_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Last_Name</name>
                    <dataType>String</dataType>
                    <fieldText>Last Name</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <fields>
            <name>Create_Household_Member_Section2</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Create_Household_Member_Section2_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Member_Type</name>
                    <choiceReferences>Primary</choiceReferences>
                    <choiceReferences>Secondary</choiceReferences>
                    <choiceReferences>Other</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>Member Type</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Create_Household_Member_Section2_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Exclude_from_Houshold_Naming</name>
                    <dataType>Boolean</dataType>
                    <fieldText>Exclude from Household Naming</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>Member_Type</leftValueReference>
                            <operator>NotEqualTo</operator>
                            <rightValue>
                                <elementReference>Primary</elementReference>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>122</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Create_Household_Member</targetReference>
        </connector>
    </start>
    <status>Draft</status>
    <variables>
        <name>FirstName</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>HouseholdAccountId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>LastName</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
