/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 9/10/24.
 */

public without sharing class TRG_Benefit_Dates extends Domain {
    public static final Id RT_APPLIEDBENEFIT = SObjectUtils.getRecordTypeIdForObjectAndName(Benefit__c.SObjectType, 'AppliedBenefit');

    public TRG_Benefit_Dates (){}
    public TRG_Benefit_Dates (List<Benefit__c> records) {
        super(records);
    }

    public override void doBeforeInsert() {
        defaults((List<Benefit__c>)this.triggerRecords, null);
    }

    public override void doBeforeUpdate(Map<Id, SObject> oldRecordsMap) {
        defaults((List<Benefit__c>)this.triggerRecords, oldRecordsMap);
    }

    public void defaults(List<Benefit__c> recordsNew, Map<Id, SObject> recordsOld) {
        for (Benefit__c recordNew : recordsNew) {
            Benefit__c recordOld = (Benefit__c)recordsOld?.get(recordNew.Id);
            if (recordNew.RecordTypeId == RT_APPLIEDBENEFIT && recordNew.EffectiveDate__c != null) {
                if (recordNew.ExpirationDate__c == null && recordNew.TermDays__c != null) {
                    recordNew.ExpirationDate__c = recordNew.EffectiveDate__c.addDays(recordNew.TermDays__c.intValue());
                } else if (recordNew.ExpirationDate__c != null && recordNew.TermDays__c == null) {
                    recordNew.TermDays__c = recordNew.EffectiveDate__c.daysBetween(recordNew.ExpirationDate__c);
                }
            }
        }
    }
}