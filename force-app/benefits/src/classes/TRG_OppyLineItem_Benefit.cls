/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 8/11/24.
 */

public without sharing class TRG_OppyLineItem_Benefit extends Domain {
    public static final Id RT_APPLIEDBENEFIT = SObjectUtils.getRecordTypeIdForObjectAndName(Benefit__c.SObjectType, 'AppliedBenefit');

    public TRG_OppyLineItem_Benefit (){}
    public TRG_OppyLineItem_Benefit (List<OpportunityLineItem> records) {
        super(records);
    }

    public override void doAfterInsert() {
        copyBenefits((List<OpportunityLineItem>)this.triggerRecords);
    }

    public override void doAfterUpdate(Map<Id, SObject> oldRecordsMap) {
        copyBenefits((List<OpportunityLineItem>)this.triggerRecords);
    }

    public void copyBenefits (List<OpportunityLineItem> oppLines) {
        Map<Id, Set<Id>> parentOppExistingBenefitProducts = new Map<Id, Set<Id>>(); // This ensures we don't duplicate benefits in an opp
        Map<Id, Product2> productMap = new Map<Id, Product2>();
        Map<Id, Opportunity> parentOppMap = new Map<Id, Opportunity>();

        // Get all the product IDs referenced in the OLIs
        for (OpportunityLineItem oli : oppLines) {
            if (oli.TotalPrice <= 0) {
                continue;
            }
            parentOppExistingBenefitProducts.put(oli.OpportunityId, new Set<Id>());
            productMap.put(oli.Product2Id, null);
            productMap.put(oli.TicketureTicketGroupProduct__c, null);
            productMap.put(oli.TicketureTicketTypeProduct__c, null);
            parentOppMap.put(oli.OpportunityId, null);
        }
        System.debug('Product KeySet: ' + productMap.keySet());

        // Find all existing Benefits for the parent Opportunity
        for (Opportunity opp : [
                SELECT Id, AccountId,
                        //tixtrack__TixTrackMemberLevel__c,
                        CloseDate, (
                        SELECT Id,
                                Name,
                                BenefitTemplate__c
                        FROM Benefits__r
                )
                FROM Opportunity
                WHERE Id IN :parentOppExistingBenefitProducts.keySet()
        ]) {
            parentOppMap.put(opp.Id, opp);
            Set<Id> innerSet = parentOppExistingBenefitProducts.get(opp.Id);
            for (Benefit__c oppBenefit : opp.Benefits__r) {
                innerSet.add(oppBenefit.Id);
            }
        }

        Id templateBenefitRecordTypeId = SObjectUtils.getRecordTypeIdForObjectAndName(Benefit__c.SObjectType, 'BenefitTemplate');

        // Get all the benefits for all the products referenced by OLIs
        productMap = new Map<Id, Product2>([
                SELECT Id, (
                        SELECT Id,
                                Name,
                                QuantityEntitled__c,
                                Description__c,
                                Product__c,
                                Product__r.Name,
                                TermDays__c,
                                Campaign__c,
                                Category__c
                        FROM Benefits__r
                        WHERE RecordTypeId = :templateBenefitRecordTypeId AND IsTemplateActive__c = TRUE
                )
                FROM Product2
                WHERE Id IN :productMap.keySet()
        ]);


        List<Benefit__c> benefitsToInsert = new List<Benefit__c>();

        // Create the AppliedBenefits, but only if the opportunity doesn't already have them
        for (OpportunityLineItem oli : oppLines) {
            System.debug(oli);
            if (oli.TotalPrice <= 0 && (oli.Quantity * oli.UnitPrice) <= 0) {
                continue; // No benefits for $0 items
            }

            Set<Id> thisParentOppBenefits = parentOppExistingBenefitProducts.get(oli.OpportunityId);
            Opportunity thisParentOpp = parentOppMap.get(oli.OpportunityId);

            benefitsToInsert.addAll(buildAppliedBenefitRecords(productMap.get(oli.Product2Id), thisParentOpp, oli, thisParentOppBenefits));
            benefitsToInsert.addAll(buildAppliedBenefitRecords(productMap.get(oli.TicketureTicketGroupProduct__c), thisParentOpp, oli, thisParentOppBenefits));
            benefitsToInsert.addAll(buildAppliedBenefitRecords(productMap.get(oli.TicketureTicketTypeProduct__c), thisParentOpp, oli, thisParentOppBenefits));
        }

        if (!benefitsToInsert.isEmpty()) {
            insert benefitsToInsert;
        }
    }

    public static List<Benefit__c> buildAppliedBenefitRecords(Product2 prdWithBenefits, Opportunity theOpp, OpportunityLineItem theOLI, Set<Id> oppsExistingBenefits) {
        List<Benefit__c> benefitsToInsert = new List<Benefit__c>();
        if (prdWithBenefits != null) {
            for (Benefit__c templateBenefit : prdWithBenefits.Benefits__r) {
                if (!oppsExistingBenefits.contains(templateBenefit.Id)) {
                    benefitsToInsert.add(new Benefit__c(
                            RecordTypeId = RT_APPLIEDBENEFIT,
                            Name = templateBenefit.Name,
                            BenefitTemplate__c  = templateBenefit.Id,
                            Description__c = templateBenefit.Description__c,
                            QuantityEntitled__c = templateBenefit.QuantityEntitled__c,
                            AppliedProduct__c = prdWithBenefits.Id,
                            //Product__c = templateBenefit.Product__c,
                            Campaign__c = templateBenefit.Campaign__c,
                            Account__c = theOpp.AccountId,
                            Opportunity__c = theOpp.Id,
                            OpportunityProduct__c = theOLI.Id,
                            Category__c = templateBenefit.Category__c,
                            TermDays__c = templateBenefit.TermDays__c//,
                            //EffectiveDate__c = thisParentOpp.CloseDate,
                            //ExpirationDate__c = thisParentOpp.CloseDate.addDays(templateBenefit.TermDays__c == null ? 0 : templateBenefit.TermDays__c.intValue())
                    ));
                    oppsExistingBenefits.add(templateBenefit.Id);
                }
            }
        }

        return benefitsToInsert;
    }
}