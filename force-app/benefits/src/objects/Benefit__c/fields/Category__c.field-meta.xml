<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>Category__c</fullName>
    <label>Category</label>
    <required>false</required>
    <trackTrending>false</trackTrending>
    <type>Picklist</type>
    <valueSet>
        <restricted>true</restricted>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>Marketing/Branding</fullName>
                <default>false</default>
                <label>Marketing/Branding</label>
            </value>
            <value>
                <fullName>Entertainment/Hospitality</fullName>
                <default>false</default>
                <label>Entertainment/Hospitality</label>
            </value>
            <value>
                <fullName>Promotional/Special Opportunities</fullName>
                <default>false</default>
                <label>Promotional/Special Opportunities</label>
            </value>
            <value>
                <fullName>Ticketing/Vouchers</fullName>
                <default>false</default>
                <label>Ticketing/Vouchers</label>
            </value>
            <value>
                <fullName>Other</fullName>
                <default>false</default>
                <label>Other</label>
            </value>
            <value>
                <fullName>Corporate Family Day</fullName>
                <default>false</default>
                <isActive>false</isActive>
                <label>Corporate Family Day</label>
            </value>
            <value>
                <fullName>Free Daily Admission</fullName>
                <default>false</default>
                <isActive>false</isActive>
                <label>Free Daily Admission</label>
            </value>
            <value>
                <fullName>Museum Passes</fullName>
                <default>false</default>
                <isActive>false</isActive>
                <label>Museum Passes</label>
            </value>
            <value>
                <fullName>Social Media Marketing</fullName>
                <default>false</default>
                <isActive>false</isActive>
                <label>Social Media Marketing</label>
            </value>
        </valueSetDefinition>
    </valueSet>
</CustomField>
