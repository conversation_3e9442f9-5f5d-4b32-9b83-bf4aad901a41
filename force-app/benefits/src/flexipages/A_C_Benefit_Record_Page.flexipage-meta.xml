<?xml version="1.0" encoding="UTF-8"?>
<FlexiPage xmlns="http://soap.sforce.com/2006/04/metadata">
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>collapsed</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>enableActionsConfiguration</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>enableActionsInNative</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>hideChatterActions</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>numVisibleActions</name>
                    <value>3</value>
                </componentInstanceProperties>
                <componentName>force:highlightsPanel</componentName>
                <identifier>force_highlightsPanel</identifier>
            </componentInstance>
        </itemInstances>
        <name>header</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Name</fieldItem>
                <identifier>RecordNameField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.BenefitTemplate__c</fieldItem>
                <identifier>Recordflmas__BenefitTemplate__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-3b13f32f-6b14-46fb-acb4-f0c31e914754</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Category__c</fieldItem>
                <identifier>Recordflmas_Category_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.IsTemplateActive__c</fieldItem>
                <identifier>Recordflmas_IsTemplateActive_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordType.DeveloperName}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>BenefitTemplate</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.AppliedProduct__c</fieldItem>
                <identifier>Recordflmas_AppliedProduct_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordType.DeveloperName}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>AppliedBenefit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Product__c</fieldItem>
                <identifier>Recordflmas_Product_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordType.DeveloperName}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>BenefitTemplate</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Account__c</fieldItem>
                <identifier>Recordflmas_Account_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordType.DeveloperName}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>AppliedBenefit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Opportunity__c</fieldItem>
                <identifier>Recordflmas_Opportunity_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordType.DeveloperName}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>AppliedBenefit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.OpportunityProduct__c</fieldItem>
                <identifier>Recordflmas_OpportunityProduct_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordType.DeveloperName}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>AppliedBenefit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Campaign__c</fieldItem>
                <identifier>Recordflmas__Campaign__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-d2013e08-3988-4bcf-b4ce-d18cedb67c4e</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-3b13f32f-6b14-46fb-acb4-f0c31e914754</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-d2013e08-3988-4bcf-b4ce-d18cedb67c4e</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column2</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-715a6fc4-facb-4d4c-b1b5-02160e742e4f</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.EffectiveDate__c</fieldItem>
                <identifier>Recordflmas_EffectiveDate_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordType.DeveloperName}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>AppliedBenefit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.QuantityEntitled__c</fieldItem>
                <identifier>Recordflmas_QuantityEntitled_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.QuantityIssued__c</fieldItem>
                <identifier>Recordflmas_QuantityUsed_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordType.DeveloperName}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>AppliedBenefit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.QuantityPurchased__c</fieldItem>
                <identifier>Recordflmas_QuantityPurchased_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordType.DeveloperName}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>AppliedBenefit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.QuantityRedeemed__c</fieldItem>
                <identifier>Recordflmas_QuantityRedeemed_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordType.DeveloperName}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>AppliedBenefit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-2938c8ef-4754-4555-948d-5db6506fb742</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ExpirationDate__c</fieldItem>
                <identifier>Recordflmas_ExpirationDate_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordType.DeveloperName}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>AppliedBenefit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.TermDays__c</fieldItem>
                <identifier>Recordflmas_TermDays_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.QuantityRemaining__c</fieldItem>
                <identifier>Recordflmas_QuantityRemaining_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordType.DeveloperName}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>AppliedBenefit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.UtilizationPct__c</fieldItem>
                <identifier>Recordflmas_UtilizationPct_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordType.DeveloperName}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>AppliedBenefit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.IsAvailable__c</fieldItem>
                <identifier>Recordflmas_IsAvailable_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordType.DeveloperName}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>AppliedBenefit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-0af16934-5e13-4a86-9a8e-2595a01a0f9c</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-2938c8ef-4754-4555-948d-5db6506fb742</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column6</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-0af16934-5e13-4a86-9a8e-2595a01a0f9c</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column7</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-98cd2e1c-a4c0-46ba-a960-b6c010a6e801</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Description__c</fieldItem>
                <identifier>Recordflmas_Description_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-1d4d015b-440d-4eb4-9f68-caded21f41e8</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-1d4d015b-440d-4eb4-9f68-caded21f41e8</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column3</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-95794795-2846-484d-beb8-153782e3747b</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.CreatedById</fieldItem>
                <identifier>RecordCreatedByIdField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.OwnerId</fieldItem>
                <identifier>RecordOwnerIdField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-9caea82d-e3c7-4185-b9c7-1d1cd229ad1f</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LastModifiedById</fieldItem>
                <identifier>RecordLastModifiedByIdField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.RecordTypeId</fieldItem>
                <identifier>RecordRecordTypeIdField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-87d973a4-a1dd-4e2e-8eb6-9a4faf1f1041</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-9caea82d-e3c7-4185-b9c7-1d1cd229ad1f</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column4</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-87d973a4-a1dd-4e2e-8eb6-9a4faf1f1041</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column5</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-a1814693-24d1-4bc0-8ad7-79a118361324</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-715a6fc4-facb-4d4c-b1b5-02160e742e4f</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Information</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-98cd2e1c-a4c0-46ba-a960-b6c010a6e801</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Quantities</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection4</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-95794795-2846-484d-beb8-153782e3747b</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Description</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection2</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-a1814693-24d1-4bc0-8ad7-79a118361324</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>System Information</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection3</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>MassChangeOwner</value>
                        </valueListItems>
                        <valueListItems>
                            <value>New</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>maxRecordsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Benefit__c.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>AppliedBenefits__r</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListDisplayType</name>
                    <value>ADVGRID</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListFieldAliases</name>
                    <valueList>
                        <valueListItems>
                            <value>NAME</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Opportunity__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Account__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>EffectiveDate__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>QuantityEntitled__c</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListLabel</name>
                    <value>Applied Benefits</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldAlias</name>
                    <value>EffectiveDate__c</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldOrder</name>
                    <value>Descending</value>
                </componentInstanceProperties>
                <componentName>lst:dynamicRelatedList</componentName>
                <identifier>lst_dynamicRelatedList</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordType.Name}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Benefit Template</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>force:recordDetailPanelMobile</componentName>
                <identifier>force_recordDetailPanelMobile</identifier>
            </componentInstance>
        </itemInstances>
        <name>main</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>relatedListComponentOverride</name>
                    <value>NONE</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>rowsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentName>force:relatedListContainer</componentName>
                <identifier>force_relatedListContainer</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordType.Name}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Applied Benefit</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <name>sidebar</name>
        <type>Region</type>
    </flexiPageRegions>
    <masterLabel>A&amp;C Benefit Record Page</masterLabel>
    <sobjectType>Benefit__c</sobjectType>
    <template>
        <name>flexipage:recordHomeTemplateDesktop</name>
    </template>
    <type>RecordPage</type>
</FlexiPage>
