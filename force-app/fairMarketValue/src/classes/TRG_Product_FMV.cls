/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 9/28/23.
 */

public without sharing class TRG_Product_FMV extends Domain {

    public TRG_Product_FMV (){}
    public TRG_Product_FMV (List<Product2> records) {
        super(records);
    }

    public override void doAfterInsert() {
        // Create a default FMV of 100%
        List<FairMarketValue__c> fmvs = new List<FairMarketValue__c>();
        for (Product2 p : (List<Product2>)this.triggerRecords) {
            fmvs.add(
                    new FairMarketValue__c(
                            IsEnabled__c            = true,
                            EffectiveDate__c        = Date.today(),
                            Product__c              = p.Id,
                            FairMarketValuePct__c   = 100
                    )
            );
        }
        insert fmvs;
    }
}