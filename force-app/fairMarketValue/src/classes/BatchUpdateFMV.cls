/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 6/13/23.
 *
 * Triggers an update on all OppyLineItem records that reference a Product with a FairMarketValue definition.
 * This effectively triggers an FMV re-calculation and is useful after FMV templates have changed.
 */

public without sharing class BatchUpdateFMV  implements Database.Batchable<SObject>, Database.Stateful {

    public Iterable<SObject> start (Database.BatchableContext bc) {
        return [
                SELECT Id,
                        OpportunityId
                  FROM OpportunityLineItem
                 WHERE Product2Id IN (
                            SELECT flmas__Product__c
                              FROM flmas__FairMarketValue__c
                        )
        ];
    }

    public void execute (Database.BatchableContext bc, List<OpportunityLineItem> oppLines) {
        update oppLines;
    }

    public void finish (Database.BatchableContext bc) {
    }
}