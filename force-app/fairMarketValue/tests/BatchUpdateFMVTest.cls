/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 6/13/23.
 */

@IsTest
private class BatchUpdateFMVTest {
    @IsTest
    static void testBehavior () {
        BatchUpdateFMV b = new BatchUpdateFMV();
        b.start(null);
        b.finish(null);
        Id householdRecordTypeId = SObjectUtils.getRecordTypeIdForObjectAndName(Account.SObjectType, 'Business_Account');

        /*List<String> names = new List<String>();
        for (RecordType rt : [
                SELECT Id, Name
                FROM RecordType
                WHERE SobjectType = 'Account'
        ]) {
            names.add(rt.Name);
        }*/
        //throw new DataProviderUtility.DataProviderUtilityException('Account Record Types: ' + String.join(names, ', '));

        Account testHousehold = new Account(
                Name = 'test',
                RecordTypeId = householdRecordTypeId
        );
        insert testHousehold;
        Contact testContact = new Contact(
                FirstName = 'first',
                LastName = 'last',
                AccountId = testHousehold.Id
        );
        insert testContact;
        Product2 testProduct = new Product2(
                Name = 'test product',
                IsActive = true,
                CanUseRevenueSchedule = true
        );
        insert testProduct;
        PricebookEntry testPricebookEntry = new PricebookEntry(
                Product2Id = testProduct.Id,
                Pricebook2Id = Test.getStandardPricebookId(),
                IsActive = true,
                UnitPrice = 100
        );
        insert testPricebookEntry;
        flmas__FairMarketValue__c testFairMarketValue = new flmas__FairMarketValue__c(
                flmas__MaxFairMarketValue__c = 10,
                flmas__FairMarketValuePct__c = 100.0,
                flmas__EffectiveDate__c = Date.today().addDays(-1),
                flmas__Product__c = testProduct.Id
        );
        insert testFairMarketValue;
        Opportunity testOpportunity = new Opportunity(
                AccountId = testHousehold.Id,
                Name = 'test opp',
                Amount = 100,
                StageName = 'Closed Won',
                CloseDate = Date.today()
        );
        insert testOpportunity;
        OpportunityLineItem testOLI = new OpportunityLineItem(
                OpportunityId = testOpportunity.Id,
                Quantity = 1,
                UnitPrice = 100,
                PricebookEntryId = testPricebookEntry.Id
        );
        insert testOLI;

        Test.startTest();
        b.execute(null, new List<OpportunityLineItem>{testOLI});
        Test.stopTest();
    }
}