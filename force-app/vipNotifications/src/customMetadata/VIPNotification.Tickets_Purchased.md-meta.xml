<?xml version="1.0" encoding="UTF-8"?>
<CustomMetadata xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <label>Tickets Purchased</label>
    <protected>false</protected>
    <values>
        <field>BodyTemplate__c</field>
        <value xsi:type="xsd:string">{!Opportunity.Account.Name} purchased {!Opportunity.TotalOpportunityQuantity} Tickets for ${!Opportunity.Amount} on {!tixtrack__TicketurePurchasedDateTime__c} for use on {!tixtrack__TicketureValidFrom__c}</value>
    </values>
    <values>
        <field>FilterCriteria__c</field>
        <value xsi:type="xsd:string">tixtrack__TicketureStatus__c = &apos;purchased&apos;</value>
    </values>
    <values>
        <field>IsActive__c</field>
        <value xsi:type="xsd:boolean">true</value>
    </values>
    <values>
        <field>NotificationTarget__c</field>
        <value xsi:type="xsd:string">OpportunityId</value>
    </values>
    <values>
        <field>SourceTriggerObject__c</field>
        <value xsi:type="xsd:string">OpportunityLineItem</value>
    </values>
    <values>
        <field>SubscribersTarget__c</field>
        <value xsi:type="xsd:string">Opportunity.AccountId</value>
    </values>
    <values>
        <field>TitleTemplate__c</field>
        <value xsi:type="xsd:string">{!Opportunity.Account.Name} Purchased Tickets</value>
    </values>
</CustomMetadata>
