/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 5/8/25.
 */

public without sharing class TRG_Account_EmailDomain extends Domain {

    public TRG_Account_EmailDomain() {
    }
    public TRG_Account_EmailDomain(List<Account> records) {
        super(records);
    }

    /*public override void doBeforeInsert () {
        findAndAttachEmailDomain(this.triggerRecords, null);
    }

    public override void doBeforeUpdate (Map<Id, SObject> oldRecordsMap) {
        findAndAttachEmailDomain(this.triggerRecords, (Map<Id, Account>) oldRecordsMap);
    }

    private static void findAndAttachEmailDomain (List<Account> paRecords, Map<Id, Account> oldPaRecords) {
        Map<String, EmailDomain__c> emailDomainRecordsByDomain = new Map<String, EmailDomain__c>();
        for (Account paRecord : paRecords) {
            if (paRecord.RecordTypeId == RecordTypeSettings.personAccountId) {
                if (paRecord.Id == null || paRecord.EmailDomain__pc == null) {
                    // This is an insert, or the contact has no associated domain
                    emailDomainRecordsByDomain.put(paRecord.PersonEmail?.substringAfter('@')?.toLowerCase(), null);
                } else {
                    Account oldPa = oldPaRecords.get(paRecord.Id);
                    if (oldPa.PersonEmail != paRecord.PersonEmail) {
                        emailDomainRecordsByDomain.put(paRecord.PersonEmail?.substringAfter('@')?.toLowerCase(), null);
                    }
                }
            }
        }

        TRG_Contact_EmailDomain.findDomains(emailDomainRecordsByDomain);

        for (Account paRecord : paRecords) {
            paRecord.EmailDomain__pc = emailDomainRecordsByDomain.get(paRecord.PersonEmail?.substringAfter('@')?.toLowerCase())?.Id;
        }
    }*/
}