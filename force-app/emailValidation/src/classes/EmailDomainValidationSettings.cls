/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by triestelaporte on 5/14/2024.
 */

public inherited sharing class EmailDomainValidationSettings {

	public Integer validationExpirationDays;
	public Integer batchSize;
	private static EmailDomainValidationSettings settingsCache;

	private EmailDomainValidationSettings () {
	}

	public static EmailDomainValidationSettings getSettings () {
		if (settingsCache == null) {
			settingsCache = new EmailDomainValidationSettings();

			EmailValidationSettings__c theSettings = EmailValidationSettings__c.getInstance();

			settingsCache.validationExpirationDays = Integer.valueOf(theSettings.ValidationExpirationDays__c ?? 150);
			settingsCache.batchSize = Integer.valueOf(theSettings.BatchSize__c ?? 10);
		}
		return settingsCache;
	}
}