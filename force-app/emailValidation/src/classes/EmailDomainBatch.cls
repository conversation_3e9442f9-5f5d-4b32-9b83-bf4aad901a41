/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 5/12/25.
 */

global with sharing class EmailDomainBatch implements Database.Batchable<SObject>, Database.Stateful, Database.AllowsCallouts {

    public Database.QueryLocator start (Database.BatchableContext context) {
        return Database.getQueryLocator([
                SELECT Id,
                        EmailAddress
                FROM ContactPointEmail
                WHERE EmailAddress != NULL
                AND EmailDomainRecord__c = NULL
        ]);
    }
    public void execute (Database.BatchableContext context, List<SObject> records) {
        findAndAttachEmailDomain(records);
    }

    public void finish (Database.BatchableContext context) {
    }

    global static void run() {
        Database.executeBatch(new EmailDomainBatch());
    }

    private static void findAndAttachEmailDomain (List<ContactPointEmail> records) {
        Map<String, EmailDomain__c> emailDomainRecordsByDomain = new Map<String, EmailDomain__c>();
        for (ContactPointEmail cpe : records) {
            emailDomainRecordsByDomain.put(cpe.EmailAddress?.substringAfter('@')?.toLowerCase(), null);
        }

        emailDomainRecordsByDomain.remove(null);

        if (!emailDomainRecordsByDomain.isEmpty()) {
            for (EmailDomain__c ed : [
                    SELECT Id,
                            Name
                    FROM EmailDomain__c
                    WHERE Name IN :emailDomainRecordsByDomain.keySet()
            ]) {
                emailDomainRecordsByDomain.put(ed.Name.toLowerCase(), ed);
            }

            for (String emailDomainName : emailDomainRecordsByDomain.keySet()) {
                EmailDomain__c ed = emailDomainRecordsByDomain.get(emailDomainName);
                if (ed == null) {
                    emailDomainRecordsByDomain.put(emailDomainName, new EmailDomain__c(Name = emailDomainName));
                }
            }

            upsert emailDomainRecordsByDomain.values() Name;

            for (ContactPointEmail cpe : records) {
                cpe.EmailDomainRecord__c = emailDomainRecordsByDomain.get(cpe.EmailAddress?.substringAfter('@')?.toLowerCase())?.Id;
            }

            Domain.suppressAll();
            update records;
            Domain.allowAll();
        }
    }
}