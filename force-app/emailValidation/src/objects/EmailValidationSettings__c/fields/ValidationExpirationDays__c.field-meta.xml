<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>ValidationExpirationDays__c</fullName>
    <defaultValue>150</defaultValue>
    <description>Positive value:  Number of days before an email domain is revalidated during a contact or email domain update operation.</description>
    <externalId>false</externalId>
    <inlineHelpText>Positive value:  Number of days before an email domain is revalidated during a contact or email domain update operation.</inlineHelpText>
    <label>Validation Expiration Days</label>
    <precision>3</precision>
    <required>true</required>
    <scale>0</scale>
    <trackTrending>false</trackTrending>
    <type>Number</type>
    <unique>false</unique>
</CustomField>
