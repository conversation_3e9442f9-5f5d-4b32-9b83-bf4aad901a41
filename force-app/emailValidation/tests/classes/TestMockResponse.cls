/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by arbel on 5/15/2024.
 */

@IsTest(SeeAllData=false)
global class TestMockResponse implements HttpCalloutMock {
    private Map<String, String> urlToBodyMap;
    private Map<String, Integer> urlToCodeMap;

    global TestMockResponse () {
        urlToBodyMap = new Map<String, String>();
        urlToCodeMap = new Map<String, Integer>();
    }

    global void configureResponse (String url, String body, Integer responseCode) {
        urlToBodyMap.put(url, body);
        urlToCodeMap.put(url, responseCode);
    }

    global HttpResponse respond (HttpRequest req) {
        HttpResponse theResponse = new HttpResponse();
        theResponse.setHeader('Content-Type', 'application/json');

        if (!urlToBodyMap.containsKey(req.getEndpoint())) {
            throw new TestMockResponseException('No mock found for url:  ' + req.getEndpoint());
        }

        String theBody = urlToBodyMap.get(req.getEndpoint());
        Integer theStatusCode = urlToCodeMap.get(req.getEndpoint());

        theResponse.setBody(theBody);
        theResponse.setStatusCode(theStatusCode);
        return theResponse;
    }

    private class TestMockResponseException extends Exception {
    }
}