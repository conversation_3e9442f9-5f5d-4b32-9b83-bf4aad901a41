/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by triestelaporte on 5/15/2024.
 */

@IsTest
private class ValidateEmailDomainJobTest {


    @IsTest
    static void testValidation () {

        TestMockResponse mocker = new TestMockResponse();
        mocker.configureResponse('https://dns.google/resolve?name=awesome.com&type=mx&do=1', '{"Status": 0,"TC": false,"RD": true,"RA": true,"AD": true,"CD": false,"Question":[ {"name": "example.com.","type": 1}],"Answer":[ {"name": "example.com.","type": 15,"TTL": 10443,"data": "93.184.216.34"},{"name": "example.com.","type": 46,"TTL": 10443,"data": "a 8 2 86400 1559899303 1558087103 23689 example.com. IfelQcO5NqQIX7ZNKI245KLfdRCKBaj2gKhZkJawtJbo/do+A0aUvoDM5A7EZKcF/j8SdtyfYWj/8g91B2/m/WOo7KyZxIC918R1/jvBRYQGreDL+yutb1ReGc6eUHX+NKJIYqzfal+PY7tGotS1Srn9WhBspXq8/0rNsEnsSoA="}],"Additional":[]}', 200);
        Test.setMock(HttpCalloutMock.class, mocker);

        EmailDomain__c testEmailDomain = new EmailDomain__c(
                Name = 'awesome.com'
        );
        insert testEmailDomain;

        Test.startTest();
        ValidateEmailDomainJob.runNow();
        Test.stopTest();

        testEmailDomain = [
                SELECT Id,
                        ValidationError__c,
                        IsValid__c,
                        LastCheckedDatetime__c
                FROM EmailDomain__c
                WHERE Id = :testEmailDomain.Id
        ];
        System.assertEquals(null, testEmailDomain.ValidationError__c);
        System.assertEquals(true, testEmailDomain.IsValid__c);
        System.assertNotEquals(null, testEmailDomain.LastCheckedDatetime__c);

    }
}