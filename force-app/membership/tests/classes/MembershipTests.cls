/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 6/28/24.
 */

@IsTest
private class MembershipTests {

    @TestSetup
    static void setup() {
        // Test Membership Levels
        insert new List<Product2>{
                new Product2(Name = 'Test 1', TicketureSeller__c = 'Seller1', MembershipRoleRank__c = 1, TicketureHierarchy__c = 'TicketGroup', tixtrack__TicketureEventTemplateId__c = '123'),
                new Product2(Name = 'Test 2', TicketureSeller__c = 'Seller1', MembershipRoleRank__c = 2, TicketureHierarchy__c = 'TicketGroup', tixtrack__TicketureEventTemplateId__c = '124'),
                new Product2(Name = 'Test 3', TicketureSeller__c = 'Seller1', MembershipRoleRank__c = 3, TicketureHierarchy__c = 'TicketGroup', tixtrack__TicketureEventTemplateId__c = '125'),
                new Product2(Name = 'Test 1', TicketureSeller__c = 'Seller2', MembershipRoleRank__c = 1, TicketureHierarchy__c = 'TicketGroup', tixtrack__TicketureEventTemplateId__c = '126'),
                new Product2(Name = 'Test 2', TicketureSeller__c = 'Seller2', MembershipRoleRank__c = 2, TicketureHierarchy__c = 'TicketGroup', tixtrack__TicketureEventTemplateId__c = '127'),
                new Product2(Name = 'Test 3', TicketureSeller__c = 'Seller2', MembershipRoleRank__c = 3, TicketureHierarchy__c = 'TicketGroup', tixtrack__TicketureEventTemplateId__c = '128')
        };

        // Test Membership Product & PBE
        Product2 testProduct = new Product2(
                Name                    = 'Membership',
                IsActive                = true,
                CanUseRevenueSchedule   = true
        );
        insert testProduct;

        PricebookEntry testPricebookEntry = new PricebookEntry(
                Product2Id = testProduct.Id,
                Pricebook2Id = Test.getStandardPricebookId(),
                IsActive = true,
                UnitPrice = 120
        );
        insert testPricebookEntry;
    }

    @IsTest
    static void testSettings() {
        MembershipSettings ms = MembershipSettings.getSettings();

        MembershipSettings.MembershipLevel ml1 = ms.getMemberLevel('Seller1', 'Test 1');
        System.Assert.areNotEqual(null, ml1);
        MembershipSettings.MembershipLevel ml2 = ms.getMemberLevel('Seller1', 'Test 2');
        System.Assert.areNotEqual(null, ml2);
        MembershipSettings.MembershipLevel ml3 = ms.getMemberLevel('Seller1', 'Test 3');
        System.Assert.areNotEqual(null, ml3);
        MembershipSettings.MembershipLevel ml4 = ms.getMemberLevel('Seller2', 'Test 1');
        System.Assert.areNotEqual(null, ml4);
        MembershipSettings.MembershipLevel ml5 = ms.getMemberLevel('Seller2', 'Test 2');
        System.Assert.areNotEqual(null, ml5);
        MembershipSettings.MembershipLevel ml6 = ms.getMemberLevel('Seller2', 'Test 3');
        System.Assert.areNotEqual(null, ml6);
    }

    @IsTest
    public static void testUpgrade () {
        Account pa = AccountTest.createPersonAccount();

        Opportunity testOpportunity = new Opportunity(
                AccountId = pa.Id,
                Name = 'test opp',
                Amount = 100,
                StageName = 'Closed Won',
                CloseDate = Date.today().addYears(-1),
                tixtrack__TixTrackPrimaryContact__c = pa.PersonContactId,
                tixtrack__TicketureSeller__c = 'Seller1',
                tixtrack__TixTrackMemberLevel__c = 'Test 1', // matches the test levels in MembershipSettings
                tixtrack__TixTrackMembershipStartDate__c = Date.today().addYears(-1),
                tixtrack__TixTrackMembershipEndDate__c = Date.today()
        );
        insert testOpportunity;

        Opportunity newOpportunity = new Opportunity(
                AccountId = pa.Id,
                Name = 'test opp',
                Amount = 100,
                StageName = 'Closed Won',
                CloseDate = Date.today(),
                tixtrack__TicketureSeller__c = 'Seller1',
                tixtrack__TixTrackMemberLevel__c = 'Test 2', // matches the test levels in MembershipSettings
                tixtrack__TixTrackMembershipStartDate__c = Date.today(),
                tixtrack__TixTrackMembershipEndDate__c = Date.today().addYears(1)
        );


        //================================================================================================================
        Test.startTest();
        insert newOpportunity;
        Test.stopTest();
        //================================================================================================================


        tixtrack__TixTrackPayment__c pymt = new tixtrack__TixTrackPayment__c();
        pymt.tixtrack__Opportunity__c   = newOpportunity.Id;
        pymt.tixtrack__PaymentAmount__c = 100;
        pymt.tixtrack__PaymentDate__c   = Date.today();
        pymt.tixtrack__System__c        = 'Ticketure';
        insert pymt;

        Opportunity oppAfterTest = [
                SELECT Id,
                        MembershipOrigin__c,
                        flmas__MembershipChangeType__c,
                        PriorMembership__c,
                        NextMembership__c
                FROM Opportunity
                WHERE Id = :newOpportunity.Id
        ];
        System.assertEquals(MembershipSettings.MembershipChangeType.Upgrade.name(), oppAfterTest.flmas__MembershipChangeType__c);
        System.assertEquals('Renewed', oppAfterTest.MembershipOrigin__c);
    }

    @IsTest
    public static void testDowngrade () {
        Account pa = AccountTest.createPersonAccount();

        Opportunity testOpportunity = new Opportunity(
                AccountId = pa.Id,
                Name = 'test opp',
                Amount = 100,
                StageName = 'Closed Won',
                CloseDate = Date.today().addYears(-1),
                tixtrack__TicketureSeller__c = 'Seller1',
                tixtrack__TixTrackMemberLevel__c = 'Test 2', // matches the test levels in MembershipSettings
                tixtrack__TixTrackMembershipStartDate__c = Date.today().addYears(-1),
                tixtrack__TixTrackMembershipEndDate__c = Date.today()
        );
        insert testOpportunity;

        Opportunity newOpportunity = new Opportunity(
                AccountId = pa.Id,
                Name = 'test opp',
                Amount = 100,
                StageName = 'Closed Won',
                CloseDate = Date.today(),
                tixtrack__TicketureSeller__c = 'Seller1',
                tixtrack__TixTrackMemberLevel__c = 'Test 1', // matches the test levels in MembershipSettings
                tixtrack__TixTrackMembershipStartDate__c = Date.today(),
                tixtrack__TixTrackMembershipEndDate__c = Date.today().addYears(1)
        );


        //================================================================================================================
        Test.startTest();
        insert newOpportunity;
        Test.stopTest();
        //================================================================================================================

        Opportunity oppAfterTest = [
                SELECT Id,
                        MembershipOrigin__c,
                        flmas__MembershipChangeType__c,
                        PriorMembership__c,
                        NextMembership__c
                FROM Opportunity
                WHERE Id = :newOpportunity.Id
        ];
        System.assertEquals(MembershipSettings.MembershipChangeType.Downgrade.name(), oppAfterTest.flmas__MembershipChangeType__c);
        System.assertEquals('Renewed', oppAfterTest.MembershipOrigin__c);
    }

    @IsTest
    public static void testRenewed_NoChange_True () {
        Account pa = AccountTest.createPersonAccount();

        Opportunity testOpportunity = new Opportunity(
                AccountId = pa.Id,
                Name = 'test opp',
                Amount = 100,
                StageName = 'Closed Won',
                CloseDate = Date.today().addYears(-1).addDays(-30),
                tixtrack__TicketureSeller__c = 'Seller1',
                tixtrack__TixTrackMemberLevel__c = 'Test 2', // matches the test levels in MembershipSettings
                tixtrack__TixTrackMembershipStartDate__c = Date.today().addYears(-1).addDays(-30),
                tixtrack__TixTrackMembershipEndDate__c = Date.today().addDays(-30)
        );
        insert testOpportunity;

        Opportunity newOpportunity = new Opportunity(
                AccountId = pa.Id,
                Name = 'test opp',
                Amount = 100,
                StageName = 'Closed Won',
                CloseDate = Date.today(),
                tixtrack__TicketureSeller__c = 'Seller1',
                tixtrack__TixTrackMemberLevel__c = 'Test 2', // matches the test levels in MembershipSettings
                tixtrack__TixTrackMembershipStartDate__c = Date.today(),
                tixtrack__TixTrackMembershipEndDate__c = Date.today().addYears(1)
        );


        //================================================================================================================
        Test.startTest();
        insert newOpportunity;
        Test.stopTest();
        //================================================================================================================

        Opportunity oppAfterTest = [
                SELECT Id,
                        MembershipOrigin__c,
                        flmas__MembershipChangeType__c,
                        PriorMembership__c,
                        NextMembership__c,
                        DaysBetweenPriorMembershipEnd__c
                FROM Opportunity
                WHERE Id = :newOpportunity.Id
        ];
        System.assertEquals(MembershipSettings.MembershipChangeType.NoChange.name(), oppAfterTest.flmas__MembershipChangeType__c);
        System.assertEquals('Renewed', oppAfterTest.MembershipOrigin__c);
        System.assertEquals(30, oppAfterTest.DaysBetweenPriorMembershipEnd__c);
        System.assertNotEquals(null, oppAfterTest.PriorMembership__c);
    }

    @IsTest
    public static void testReacquired_NoChange_False () {
        Account pa = AccountTest.createPersonAccount();

        Opportunity testOpportunity = new Opportunity(
                AccountId = pa.Id,
                Name = 'test opp',
                Amount = 100,
                StageName = 'Closed Won',
                CloseDate = Date.today().addYears(-1).addDays(-91),
                tixtrack__TicketureSeller__c = 'Seller1',
                tixtrack__TixTrackMemberLevel__c = 'Test 2', // matches the test levels in MembershipSettings
                tixtrack__TixTrackMembershipStartDate__c = Date.today().addYears(-1).addDays(-91),
                tixtrack__TixTrackMembershipEndDate__c = Date.today().addDays(-91)
        );
        insert testOpportunity;

        Opportunity newOpportunity = new Opportunity(
                AccountId = pa.Id,
                Name = 'test opp',
                Amount = 100,
                StageName = 'Closed Won',
                CloseDate = Date.today(),
                tixtrack__TicketureSeller__c = 'Seller1',
                tixtrack__TixTrackMemberLevel__c = 'Test 2', // matches the test levels in MembershipSettings
                tixtrack__TixTrackMembershipStartDate__c = Date.today(),
                tixtrack__TixTrackMembershipEndDate__c = Date.today().addYears(1)
        );


        //================================================================================================================
        Test.startTest();
        insert newOpportunity;
        Test.stopTest();
        //================================================================================================================

        Opportunity oppAfterTest = [
                SELECT Id,
                        MembershipOrigin__c,
                        flmas__MembershipChangeType__c,
                        PriorMembership__c,
                        NextMembership__c
                FROM Opportunity
                WHERE Id = :newOpportunity.Id
        ];
        System.assertEquals(MembershipSettings.MembershipChangeType.NoChange.name(), oppAfterTest.flmas__MembershipChangeType__c);
        System.assertEquals('Reacquired', oppAfterTest.MembershipOrigin__c);
    }

    @IsTest
    static void overlappingMemberships() {

        Account pa = AccountTest.createPersonAccount();

        Opportunity testMembership1 = new Opportunity(
                AccountId = pa.Id,
                Name = 'test opp',
                Amount = 100,
                StageName = 'Closed Won',
                CloseDate = Date.today().addYears(-1).addDays(-91),
                tixtrack__TicketureSeller__c = 'Seller1',
                tixtrack__TixTrackMemberLevel__c = 'Test 2', // matches the test levels in MembershipSettings
                tixtrack__TixTrackMembershipStartDate__c = Date.newInstance(2024,10,23),
                tixtrack__TixTrackMembershipEndDate__c = Date.newInstance(2024,10,24)
        );
        insert testMembership1;

        Opportunity testMembership2 = new Opportunity(
                AccountId = pa.Id,
                Name = 'test opp',
                Amount = 100,
                StageName = 'Closed Won',
                CloseDate = Date.today(),
                tixtrack__TicketureSeller__c = 'Seller1',
                tixtrack__TixTrackMemberLevel__c = 'Test 2', // matches the test levels in MembershipSettings
                tixtrack__TixTrackMembershipStartDate__c = Date.newInstance(2024,10,23),
                tixtrack__TixTrackMembershipEndDate__c = Date.newInstance(2026,10,31)
        );
        insert testMembership2;

        testMembership2.tixtrack__TixTrackMembershipStartDate__c = Date.newInstance(2025,10,23);
        Test.startTest();
        update testMembership2;
        Test.stopTest();
    }

    @IsTest
    static void testName() {
        Account primary     = AccountTest.buildPersonAccount();
        Account secondary   = AccountTest.buildPersonAccount();
        secondary.FirstName = 'Pepper';
        Account tertiary    = AccountTest.buildPersonAccount();
        tertiary.LastName   = 'Morgan';

        List<Account> accounts = new List<Account>{primary,secondary,tertiary};

        insert accounts;

        accounts = [SELECT Id, PersonContactId FROM Account WHERE Id IN :accounts];

        Opportunity testMembership2 = new Opportunity(
                AccountId = primary.Id,
                Name = 'test opp',
                Amount = 100,
                StageName = 'Closed Won',
                CloseDate = Date.today(),
                tixtrack__TicketureSeller__c = 'Seller1',
                tixtrack__TixTrackMemberLevel__c = 'Test 2', // matches the test levels in MembershipSettings
                tixtrack__TixTrackMembershipStartDate__c = Date.newInstance(2024,10,23),
                tixtrack__TixTrackMembershipEndDate__c = Date.newInstance(2026,10,31),
                tixtrack__TixTrackPrimaryContact__c = accounts.get(0).PersonContactId,
                tixtrack__TicketureNamedMember2__c = accounts.get(1).PersonContactId,
                tixtrack__TicketureNamedMember3__c = accounts.get(2).PersonContactId
        );
        insert testMembership2;

        testMembership2 = [SELECT Id, MembershipGreeting__c, MembershipAddressee__c FROM Opportunity WHERE Id = :testMembership2.Id];

        System.Assert.areEqual('Tony & Pepper Stark', testMembership2.MembershipGreeting__c);
        System.Assert.areEqual('Tony & Pepper Stark', testMembership2.MembershipAddressee__c);
    }

    /*@IsTest
    static void testSchedules() {
        Account pa = AccountTest.createPersonAccount();

        Opportunity testOpportunity = new Opportunity(
                AccountId = pa.Id,
                Name = 'test opp',
                Amount = 100,
                StageName = 'Closed Won',
                CloseDate = Date.today().addYears(-1).addDays(-91),
                tixtrack__TicketureSeller__c = 'Seller1',
                tixtrack__TixTrackMemberLevel__c = 'Test 2', // matches the test levels in MembershipSettings
                tixtrack__TixTrackMembershipStartDate__c = Date.today().addYears(-1).addDays(-91),
                tixtrack__TixTrackMembershipEndDate__c = Date.today().addDays(-91)
        );
        insert testOpportunity;

        OpportunityLineItem oli = new OpportunityLineItem();
        oli.OpportunityId       = testOpportunity.Id;
        oli.Quantity            = 1;
        oli.UnitPrice           = 120;
        oli.PricebookEntryId    = getMembershipPBEId();
        insert oli;

        OpportunityLineItemSchedule olis = new OpportunityLineItemSchedule();
        olis.Type                       = 'Revenue';
        olis.OpportunityLineItemId      = oli.Id;
        olis.Revenue                    = 120;
        olis.ScheduleDate               = Date.today();
        insert olis;
    }*/


    static Id getMembershipPBEId() {
        return [SELECT Id FROM PricebookEntry WHERE Product2.Name = 'Membership'].Id;
    }
}