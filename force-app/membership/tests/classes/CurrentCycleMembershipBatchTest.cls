/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 9/27/24.
 */

@IsTest
public with sharing class CurrentCycleMembershipBatchTest {
    @TestSetup
    static void setup() {
        // create account and membership oppy
        Account pa1 = AccountTest.createPersonAccount();

        // A membership that ended yesterday
        Opportunity membership = new Opportunity();
        membership.CloseDate = Date.today();
        membership.Name = 'Test Membership';
        membership.StageName = 'Closed Won';
        membership.Amount = 0;
        membership.AccountId = pa1.Id;
        membership.tixtrack__TixTrackPrimaryContact__c      = pa1.PersonContactId;
        membership.tixtrack__TixTrackMemberLevel__c         = 'Individual';
        membership.tixtrack__TixTrackMembershipStartDate__c = Date.today().addYears(-1);
        membership.tixtrack__TixTrackMembershipEndDate__c   = Date.today().addDays(-1);

        Domain.suppressAll();
        insert membership;

        // Set the account's current cycle mbership to the one that ended yesterday. This sets the condition for the tests
        Account a = new Account();
        a.Id = pa1.Id;
        a.CurrentCycleMembership__c = membership.Id;
        a.MostRecentMembership__c   = membership.Id;
        update a;
    }

    @IsTest
    static void test() {
        CurrentCycleMembershipBatch.schedule();
        Test.startTest();
        Database.executeBatch(new CurrentCycleMembershipBatch());
        Test.stopTest();
        new CurrentCycleMembershipBatch().execute(null);
    }
}