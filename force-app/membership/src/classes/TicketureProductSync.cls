/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 8/29/24.
 */

global without sharing class TicketureProductSync implements Database.AllowsCallouts, Schedulable, System.Queueable  {
    private static final String CRON_SCHEDULE   = '0 0 * * * ?'; // Run hourly
    private static final String CRON_NAME       = 'Arts & Culture: Ticketure Product Sync';

    private static final String GET_EVENTS_PATH                 = 'staff/events/available';
    private static final Map<String,String> GET_EVENTS_ARGS     = new Map<String,String>{
            //'_seller' => '',
            '_embed' => 'ticket_group,ticket_type,seller,ticket_group.role'
    };

    private static final PicklistField MEMBER_LEVEL_FIELD = new PicklistField('TixTrack Member Level', 'Opportunity.tixtrack__TixTrackMemberLevel__c', 'Used to store Member Level from membership tickets.');
    private static final List<PicklistField> MEMBER_TYPE_FIELDS = new List<PicklistField>{
            new PicklistField('Ticketure Primary Member Type', 'Opportunity.tixtrack__TicketurePrimaryMemberType__c', 'This is the Party Role value from the Membership Rule for this Ticket Group (Member Level)'),
            new PicklistField('Ticketure Named Member 2 Type', 'Opportunity.tixtrack__TicketureNamedMember2Type__c', 'This is the Party Role value from the Membership Rule for this Ticket Group (Member Level)'),
            new PicklistField('Ticketure Named Member 3 Type', 'Opportunity.tixtrack__TicketureNamedMember3Type__c', 'This is the Party Role value from the Membership Rule for this Ticket Group (Member Level)'),
            new PicklistField('Ticketure Named Member 4 Type', 'Opportunity.tixtrack__TicketureNamedMember4Type__c', 'This is the Party Role value from the Membership Rule for this Ticket Group (Member Level)'),
            new PicklistField('Ticketure Named Member 5 Type', 'Opportunity.tixtrack__TicketureNamedMember5Type__c', 'This is the Party Role value from the Membership Rule for this Ticket Group (Member Level)'),
            new PicklistField('Ticketure Named Member 6 Type', 'Opportunity.tixtrack__TicketureNamedMember6Type__c', 'This is the Party Role value from the Membership Rule for this Ticket Group (Member Level)')
    };
    private static final List<PicklistField> MIGRATION_TYPE_FIELDS = new List<PicklistField>{
            new PicklistField('Ticketure Migration Ticket Type 1', 'Opportunity.tixtrack__TicketureMigrationTicketType1__c', 'Used to specify additional tickets to be sent to Ticketure (such as dependents, children, add-on guests, etc.)'),
            new PicklistField('Ticketure Migration Ticket Type 2', 'Opportunity.tixtrack__TicketureMigrationTicketType2__c', 'Used to specify additional tickets to be sent to Ticketure (such as dependents, children, add-on guests, etc.)'),
            new PicklistField('Ticketure Migration Ticket Type 3', 'Opportunity.tixtrack__TicketureMigrationTicketType3__c', 'Used to specify additional tickets to be sent to Ticketure (such as dependents, children, add-on guests, etc.)'),
            new PicklistField('Ticketure Migration Ticket Type 4', 'Opportunity.tixtrack__TicketureMigrationTicketType4__c', 'Used to specify additional tickets to be sent to Ticketure (such as dependents, children, add-on guests, etc.)'),
            new PicklistField('Ticketure Migration Ticket Type 5', 'Opportunity.tixtrack__TicketureMigrationTicketType5__c', 'Used to specify additional tickets to be sent to Ticketure (such as dependents, children, add-on guests, etc.)')
    };

    /*private static final String GET_MEMBERSHIPRULES_PATH                 = 'membership_rules';
    private static final Map<String,String> GET_MEMBERSHIPRULES_ARGS     = new Map<String,String>{
            //'_seller' => '',
            '_embed' => 'ticket_group.role'
    };*/

    global static void schedule() {
        CronUtils.schedule(CRON_NAME, CRON_SCHEDULE, new TicketureProductSync());
    }

    global void execute(SchedulableContext context) {
        futureRun();
    }

    global void execute(QueueableContext context) {
        run(true);
    }

    @Future(Callout=true)
    public static void futureRun() {
        run(true);
    }

    global static void run(Boolean updatePicklistMetadata) {
        // Get all product data from Ticketure
        EventData ed = getEventData();

        if (updatePicklistMetadata) {
            // Update Member-related Picklists & Dependencies
            updateMetadata(ed);
        }

        // Create Products
        List<Product2> products = ed.toProductRecords(false);
        upsert products Product2.tixtrack__TicketureEventTemplateId__c;

        // Now update parent references Products
        products = ed.toProductRecords(true);
        upsert products Product2.tixtrack__TicketureEventTemplateId__c;

        // Get the Pricebook
        List<Pricebook2> pbs = [SELECT Id FROM Pricebook2 WHERE IsStandard = TRUE AND IsActive = TRUE LIMIT 1];
        if (pbs.size() > 0) {
            Pricebook2 pb =  pbs.get(0);
            List<PricebookEntry> pbEntries = new List<PricebookEntry>();
            for (Product2 p : products) {
                pbEntries.add(
                        new PricebookEntry(Product2Id = p.Id, Pricebook2Id = pb.Id, UnitPrice = 0.0, IsActive = true, tixtrack__TicketureEventTemplateId__c = p.tixtrack__TicketureEventTemplateId__c)
                );
            }
            upsert pbEntries tixtrack__TicketureEventTemplateId__c;
        }
    }

    /*
     Updates the Seller Global Value Set and all MemberLevel, Ticketure Named Member X Type, and Ticketure Migration
     Ticket Type X fields.
     */
    public static void updateMetadata(EventData ed) {
        // A container that allows us to deploy all metadata together in a single API call
        List<MetadataService.Metadata> allMetadataToSend = new List<MetadataService.Metadata>();

        // Seller Global Value Set
        Map<String,MetadataService.CustomValue> sellerPLValues = ed?.getSellersPicklistValuesMetadata();
        if (sellerPLValues != null && sellerPLValues.size() > 0) {
            MetadataService.GlobalValueSet sellersValueSet = new MetadataService.GlobalValueSet();
            sellersValueSet.fullName    = 'flmas__TicketureSeller__gvs';
            sellersValueSet.masterLabel = 'Ticketure Seller';
            sellersValueSet.sorted      = false;
            sellersValueSet.customValue = sellerPLValues.values();
            allMetadataToSend.add(sellersValueSet);
        }

        // All Ticket Types
        Map<String,MetadataService.CustomValue> ticketTypesPLValues = ed?.getTicketTypePicklistValuesMetadata();

        // Migration Ticket Type Global Value Set
        MetadataService.GlobalValueSet migrationTicketTypesValueSet = new MetadataService.GlobalValueSet();
        migrationTicketTypesValueSet.fullName    = 'tixtrack__Ticketure_Migration_Ticket_Types__gvs';
        migrationTicketTypesValueSet.masterLabel = 'Ticketure Migration Ticket Types';
        migrationTicketTypesValueSet.sorted      = false;
        migrationTicketTypesValueSet.customValue = ticketTypesPLValues.values();
        allMetadataToSend.add(migrationTicketTypesValueSet);

        // Build Picklist Dependencies. Key = picklist value, Value = controlling values
        Map<String,Set<String>> memberTypeDeps = new Map<String,Set<String>>();
        for (MetadataService.CustomValue cv : ticketTypesPLValues.values()) {
            memberTypeDeps.put(cv.fullName, new Set<String>());
        }
        ed.buildMemberPicklistDependencies(memberTypeDeps);

        // TixTrack Member Level Picklist
        // todo if this code is ported to NPSP don't forget the npe01__Member_Level__c field
        Map<String,MetadataService.CustomValue> memberLevelPLValues = ed?.getMemberPicklistValuesMetadata();
        if (memberLevelPLValues != null && memberLevelPLValues.size() > 0) {
            allMetadataToSend.add(MEMBER_LEVEL_FIELD.toMetadata(null, memberLevelPLValues.values(), null));
        }

        // Primary and Named Member Type 2-6 Picklists
        for (PicklistField field : MEMBER_TYPE_FIELDS) {
            allMetadataToSend.add(field.toMetadata(null, ticketTypesPLValues.values(), memberTypeDeps));
        }
        // Migration Ticket Type 1-5 Picklists
        for (PicklistField field : MIGRATION_TYPE_FIELDS) {
            allMetadataToSend.add(field.toMetadata(migrationTicketTypesValueSet, null, memberTypeDeps));
        }

        // Metadata service supports only 10 records per call. Send them in batches
        while(allMetadataToSend.size() > 0) {
            List<MetadataService.Metadata> batch = new List<MetadataService.Metadata>();
            for (Integer i = 0; i < 10 && allMetadataToSend.size() > 0; i++) {
                batch.add(allMetadataToSend.remove(0));
            }

            // Deploy
            MetadataService.MetadataPort service = new MetadataService.MetadataPort();
            service.SessionHeader = new MetadataService.SessionHeader_element();
            service.SessionHeader.sessionId = UserInfo.getSessionId();
            List<MetadataService.UpsertResult> results1 = service.upsertMetadata(batch);
            System.debug('Batch Results: ' + results1);
        }

        // todo Update Opportunity Record Types
    }

    public static EventData getEventData() {
        tixtrack.TixSyncAPI api = tixtrack.TixSyncAPI.setupAPI(true);
        EventData ed = parseJson(api.getPath(GET_EVENTS_PATH, GET_EVENTS_ARGS));

        return ed;
    }

    public class EventData {
        public Integer x_total { get; set; }
        public SellerData seller { get; set; }
        public RoleData role { get; set; }
        public EventTemplate event_template { get; set; }
        public TicketGroupData ticket_group { get; set; }
        public TicketTypeData ticket_type { get; set; }

        public List<Product2> toProductRecords(Boolean refParents) {
            List<Product2> products = new List<Product2>();
            for (Seller s : seller.x_data) {
                for (Event e : s.events.values()) {
                    products.addAll(e.toProductRecords(refParents));
                }
            }
            return products;
        }

        public Map<String,MetadataService.CustomValue> getSellersPicklistValuesMetadata() {
            Map<String,MetadataService.CustomValue> customValues = new Map<String,MetadataService.CustomValue>();
            if (seller != null && seller.x_data != null && seller.x_data.size() > 0) {
                for (Seller s : this.seller.x_data) {
                    MetadataService.CustomValue cv = s.getMetadata();
                    customValues.put(cv.fullName?.toLowerCase(), cv);
                }
            }
            return customValues;
        }
        public Map<String,MetadataService.CustomValue> getMemberPicklistValuesMetadata() {
            Map<String,MetadataService.CustomValue> customValues = new Map<String,MetadataService.CustomValue>();
            if (seller != null && seller.x_data != null && seller.x_data.size() > 0) {
                for (Seller s : this.seller.x_data) {
                    customValues.putAll(s.getMemberPicklistValuesMetadata());
                }
            }
            return customValues;
        }
        public Map<String,MetadataService.CustomValue> getTicketTypePicklistValuesMetadata() {
            Map<String,MetadataService.CustomValue> customValues = new Map<String,MetadataService.CustomValue>(); // key=name, value=PicklistValue
            if (seller != null && seller.x_data != null && seller.x_data.size() > 0) {
                for (Seller s : this.seller.x_data) {
                    customValues.putAll(s.getTicketTypePicklistValuesMetadata());
                }
            }
            return customValues;
        }
        public void buildMemberPicklistDependencies(Map<String,Set<String>> deps) {
            if (seller != null && seller.x_data != null && seller.x_data.size() > 0) {
                for (Seller s : this.seller.x_data) {
                    s.buildMemberPicklistDependencies(deps);
                }
            }
        }
    }

    public virtual class TicketureRecord {
        public String id;
        public String name;
        public String description;

        public virtual Product2 toProductRecord(Product2 parentProduct) {
            Product2 p                              = new Product2();
            p.Name                                  = this.name;
            p.tixtrack__TicketureEventTemplateId__c = this.id;
            p.Description                           = this.description;
            p.IsActive                              = true;
            p.CanUseRevenueSchedule                 = true;

            if (parentProduct != null) {
                p.ParentProduct__r = new Product2(tixtrack__TicketureEventTemplateId__c = parentProduct.tixtrack__TicketureEventTemplateId__c);
                p.TicketureSeller__c = parentProduct.TicketureSeller__c;
            }

            return p;
        }
    }

    public class SellerData {
        public Integer x_count { get; set; }
        public List<Seller> x_data { get; set; }
        public Integer x_total { get; set; }
    }

    public class Seller extends TicketureRecord {
        public String portal_id { get; set; }
        public String slug { get; set; }
        public String ticket_template_id { get; set; }
        public String timezone { get; set; }

        public Map<String, Event> events;

        public Map<String,MetadataService.CustomValue> getMemberPicklistValuesMetadata() {
            Map<String,MetadataService.CustomValue> customValues = new Map<String,MetadataService.CustomValue>();
            if (events != null) {
                for (Event e : events.values()) {
                    customValues.putAll(e.getMemberPicklistValuesMetadata());
                }
            }
            return customValues;
        }
        public Map<String,MetadataService.CustomValue> getTicketTypePicklistValuesMetadata() {
            Map<String,MetadataService.CustomValue> customValues = new Map<String,MetadataService.CustomValue>(); // key=name, value=PicklistValue
            if (events != null) {
                for (Event e : events.values()) {
                    customValues.putAll(e.getTicketTypePicklistValuesMetadata());
                }
            }
            return customValues;
        }
        public void buildMemberPicklistDependencies(Map<String,Set<String>> deps) {
            if (events != null) {
                for (Event e : events.values()) {
                    e.buildMemberPicklistDependencies(deps);
                }
            }
        }

        public MetadataService.CustomValue getMetadata() {
            MetadataService.CustomValue cv = new MetadataService.CustomValue();
            cv.isActive = true;
            cv.label = this.name;
            cv.fullName = this.id;
            cv.default_x = false;
            return cv;
        }
    }

    public class EventTemplate {
        public Integer x_count { get; set; }
        public List<Event> x_data { get; set; }
        public Integer x_total { get; set; }
    }

    public class Event extends TicketureRecord {
        public Integer x_rank { get; set; }
        public Integer capacity { get; set; }
        public String category { get; set; }
        public Boolean dedicated_order { get; set; }
        public String event_type { get; set; }
        public String hidden_type { get; set; }
        public Integer oversell_capacity { get; set; }
        public String portal_id { get; set; }
        public String release_sessions_until { get; set; }
        public String scan_code_type { get; set; }
        public String seller_id { get; set; }
        public Integer stage_size { get; set; }
        public String subtitle { get; set; }
        public String summary { get; set; }
        public String ticket_template_id { get; set; }
        public String venue_id { get; set; }

        public Map<String, TicketGroup> ticketGroups;

        public override Product2 toProductRecord(Product2 parentProduct) {
            Product2 p              = super.toProductRecord(parentProduct);
            p.TicketureHierarchy__c = 'Event';
            p.TicketureSeller__c    = this.seller_id;
            p.Family                = this.category;

            return p;
        }

        public List<Product2> toProductRecords(Boolean refParents) {
            List<Product2> products = new List<Product2>();
            Product2 thisProduct = this.toProductRecord(null);
            products.add(thisProduct);
            for (TicketGroup tg : this.ticketGroups.values()) {
                products.addAll(tg.toProductRecords(refParents ? thisProduct : null, refParents));
            }
            return products;
        }

        public Map<String,MetadataService.CustomValue> getMemberPicklistValuesMetadata() {
            List<TicketGroup> groups = ticketGroups.values();
            groups.sort(new MemberLevelComparator());

            Map<String,MetadataService.CustomValue> customValues = new Map<String,MetadataService.CustomValue>();
            for (TicketGroup e : groups) {
                if ('Membership'.equalsIgnoreCase(e.handler)) {
                    MetadataService.CustomValue cv = e.getPicklistValueMetadata();
                    customValues.put(cv.fullName?.toLowerCase(), cv);
                }
            }
            return customValues;
        }
        public Map<String,MetadataService.CustomValue> getTicketTypePicklistValuesMetadata() {
            Map<String,MetadataService.CustomValue> customValues = new Map<String,MetadataService.CustomValue>();
            for (TicketGroup e : ticketGroups.values()) {
                if ('Membership'.equalsIgnoreCase(e.handler)) {
                    customValues.putAll(e.getTicketTypePicklistValuesMetadata());
                }
            }
            return customValues;
        }
        public void buildMemberPicklistDependencies(Map<String,Set<String>> deps) {
            if (ticketGroups != null) {
                for (TicketGroup g : ticketGroups.values()) {
                    g.buildMemberPicklistDependencies(deps);
                }
            }
        }
    }

    public class TicketGroupData {
        public Integer x_count { get; set; }
        public List<TicketGroup> x_data { get; set; }
        public Integer x_total { get; set; }
    }

    public class TicketGroup extends TicketureRecord {
        public Integer x_rank { get; set; }
        public AdditionalInfoSpec additional_info_spec { get; set; }
        public String admission_end_offset { get; set; }
        public String admission_start_offset { get; set; }
        public Integer capacity { get; set; }
        public String duration { get; set; }
        public String duration_new { get; set; }
        public String event_template_id { get; set; }
        public Boolean exclude_from_event_capacity { get; set; }
        public String handler { get; set; }
        public String hidden_type { get; set; }
        public Integer max_redemptions { get; set; }
        public Integer max_tickets_per_order { get; set; }
        public String member_role_id { get; set; }
        public Integer min_tickets_per_order { get; set; }
        public Integer oversell_capacity { get; set; }
        public String portal_id { get; set; }
        public String sales_end_offset { get; set; }
        public String sales_start_offset { get; set; }
        public Boolean sells_out_event { get; set; }
        public String start_policy { get; set; }
        public String summary { get; set; }

        public Role x_role;

        public Map<String, TicketType> ticketTypes;

        public override Product2 toProductRecord(Product2 parentProduct) {
            Product2 p                          = super.toProductRecord(parentProduct);
            p.TicketureHierarchy__c             = 'TicketGroup';
            p.TicketureTicketGroupHandler__c    = this.handler;
            p.MembershipRoleRank__c             = this.x_role?.x_rank;
            return p;
        }

        public List<Product2> toProductRecords(Product2 parentProduct, Boolean refParents) {
            List<Product2> products = new List<Product2>();
            Product2 thisProduct = this.toProductRecord(refParents ? parentProduct : null);
            products.add(thisProduct);
            for (TicketType tt : this.ticketTypes.values()) {
                products.add(tt.toProductRecord(refParents ? thisProduct : null));
            }
            return products;
        }

        /**
         * @return This member level as a picklist option (CustomValue)
         */
        public MetadataService.CustomValue getPicklistValueMetadata() {
            MetadataService.CustomValue cv = new MetadataService.CustomValue();
            cv.isActive = true;
            cv.label    = this.name;
            cv.fullName = this.name;
            cv.default_x = false;
            return cv;
        }

        /**
         * @return All child ticket types as map of picklist options
         */
        public Map<String,MetadataService.CustomValue> getTicketTypePicklistValuesMetadata() {
            Map<String,MetadataService.CustomValue> customValues = new Map<String,MetadataService.CustomValue>();
            for (TicketType e : ticketTypes.values()) {
                MetadataService.CustomValue cv = e.getMetadata();
                customValues.put(cv.fullName?.toLowerCase(), cv);
            }
            return customValues;
        }
        public void buildMemberPicklistDependencies(Map<String,Set<String>> deps) {
            if (this.ticketTypes != null && 'Membership'.equalsIgnoreCase(this.handler)) {
                for (TicketType tt : this.ticketTypes.values()) {
                    Set<String> memberLevels = deps.get(tt.name);
                    if (memberLevels == null) {
                        memberLevels = new Set<String>();
                        deps.put(tt.name, memberLevels);
                    }
                    memberLevels.add(this.name);
                }
            }
        }
    }

    public class TicketTypeData {
        public Integer x_count { get; set; }
        public List<TicketType> x_data { get; set; }
        public Integer x_total { get; set; }
    }

    public class TicketType  extends TicketureRecord {
        public Integer x_rank { get; set; }
        public String currency_amount { get; set; }
        public String currency_amount_max { get; set; }
        public String currency_amount_min { get; set; }
        public String currency_code { get; set; }
        public String gl_code { get; set; }
        public String portal_id { get; set; }
        public String price_library_config { get; set; }
        public String price_library_row_id { get; set; }
        public String price_strategy { get; set; }
        public String scan_code_type { get; set; }
        public String summary { get; set; }
        public String ticket_group_id { get; set; }

        public override Product2 toProductRecord(Product2 parentProduct) {
            Product2 p              = super.toProductRecord(parentProduct);
            p.TicketureHierarchy__c = 'TicketType';
            return p;
        }

        public MetadataService.CustomValue getMetadata() {
            MetadataService.CustomValue cv = new MetadataService.CustomValue();
            cv.isActive     = true;
            cv.label        = this.name;
            cv.fullName     = this.name;
            cv.default_x    = false;
            return cv;
        }
    }

    public class AdditionalInfoSpec {
        public Boolean additionalProperties;
        public Map<String,AdditionalInfoProperty> properties;
        public List<String> required; // the property names that are required
    }

    public class AdditionalInfoProperty {
        public String description;
        public Integer propertyOrder;
        public String stringContentType;
        public String title;
        public String type; // string, boolean, integer, number, enum
    }

    /*public class MembershipRulesResponse {
        public Integer x_total;
        public MembershipRulesData membership_rules;
        public RoleData role;
    }
    public class MembershipRulesData {
        public Integer x_count;
        public List<MembershipRule> x_data;
    }
    public class MembershipRule {
        public NamedEmailed emailed;
        public String id;
        public Integer max;
        public Integer min;
        public NamedEmailed named;
        public String ticket_type_id;
        public String party_role;
    }
    public enum NamedEmailed {
        yes,no,optional
    }*/
    public class RoleData {
        public Integer x_count;
        public List<Role> x_data;
    }
    public class Role {
        public Integer x_rank;
        public String id;
    }


    // Helper method to parse JSON and group data
    public static EventData parseJson(String jsonResponse) {
        EventData eventData = (EventData) JSON.deserialize(jsonResponse, EventData.class);

        // Create a map to store roles by IDs
        Map<String, Role> roleMap = new Map<String, Role>();
        for (Role r : eventData.role.x_data) {
            roleMap.put(r.id, r);
        }

        // Create a map to store sellers by their IDs
        Map<String, Seller> sellerMap = new Map<String, Seller>();
        for (Seller s : eventData.seller.x_data) {
            s.events = new Map<String, Event>();
            sellerMap.put(s.id, s);
        }

        // Create a map to store events by their IDs
        Map<String, Event> eventMap = new Map<String, Event>();
        for (Event e : eventData.event_template.x_data) {
            e.ticketGroups = new Map<String, TicketGroup>();
            eventMap.put(e.id, e);
            sellerMap.get(e.seller_id).events.put(e.id, e);
        }

        // Create a map to store ticket groups by their IDs
        Map<String, TicketGroup> ticketGroupMap = new Map<String, TicketGroup>();
        for (TicketGroup tg : eventData.ticket_group.x_data) {
            tg.ticketTypes = new Map<String, TicketType>();
            ticketGroupMap.put(tg.id, tg);
            tg.x_role = RoleMap.get(tg.member_role_id);
            eventMap.get(tg.event_template_id).ticketGroups.put(tg.id, tg);
        }

        // Now associate ticket types to their ticket groups
        for (TicketType tt : eventData.ticket_type.x_data) {
            ticketGroupMap.get(tt.ticket_group_id).ticketTypes.put(tt.id, tt);
        }

        return eventData;
    }



    public class MemberLevelComparator implements Comparator<TicketGroup> {
        public Integer compare(TicketGroup param1, TicketGroup param2) {
            return param1.x_rank == param2.x_rank ? 0 : param1.x_rank < param2.x_rank ? -1 : 1;
        }
    }

    public class PicklistField {
        public String fullName;
        public String label;
        public String description;
        public String inlineHelpText;

        public PicklistField(String label, String fullName, String description) {
            this.label = label;
            this.fullName = fullName;
            this.description = description;
            this.inlineHelpText = description;
        }

        //public MetadataService.CustomField toMetadata(List<MetadataService.CustomValue> values) {
        public MetadataService.CustomField toMetadata(MetadataService.GlobalValueSet gvs, List<MetadataService.CustomValue> values, Map<String,Set<String>> dependencies) {
            MetadataService.CustomField picklistField = new MetadataService.CustomField();
            picklistField.fullName = this.fullName;
            picklistField.deprecated = false;
            picklistField.description = this.description;
            picklistField.inlineHelpText = this.inlineHelpText;
            picklistField.label = label;
            picklistField.required  = false;
            picklistField.trackFeedHistory = false;
            picklistField.trackTrending = false;
            picklistField.type_x = 'Picklist';
            picklistField.valueSet = new MetadataService.ValueSet();

            if (values != null) {
                picklistField.valueSet.valueSetDefinition = new MetadataService.ValueSetValuesDefinition();
                picklistField.valueSet.valueSetDefinition.sorted = false;
                picklistField.valueSet.valueSetDefinition.value = values;
            } else if (gvs != null) {
                picklistField.valueSet.restricted = true;
                picklistField.valueSet.valueSetName = gvs.fullName;
            } else {
                throw new MemberLevelSyncException('One of GlobalValueSet or Values is required for a picklist.');
            }

            if (dependencies != null) {
                picklistField.valueSet.controllingField = 'tixtrack__TixTrackMemberLevel__c';
                picklistField.valueSet.valueSettings = new List<MetadataService.ValueSettings>();

                for (String value : dependencies.keySet()) {
                    MetadataService.ValueSettings vs = new MetadataService.ValueSettings();
                    vs.controllingFieldValue = new List<String>(dependencies.get(value));
                    vs.valueName = value;

                    picklistField.valueSet.valueSettings.add(vs);
                }
            }

            return picklistField;
        }
    }

    public class MemberLevelSyncException extends Exception {}
}