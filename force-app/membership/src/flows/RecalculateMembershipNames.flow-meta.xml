<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>62.0</apiVersion>
    <assignments>
        <description>Sets the Membership Addressee field to &quot;REPLACE&quot; which causes the system to recompute their names.</description>
        <name>Set_Addressee</name>
        <label>Set Addressee</label>
        <locationX>50</locationX>
        <locationY>350</locationY>
        <assignmentItems>
            <assignToReference>Get_Opportunity.MembershipAddressee__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>REPLACE</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Greeting_Override</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Sets the Membership Greeting field to &quot;REPLACE&quot; which causes the system to recompute the field.</description>
        <name>Set_Greeting</name>
        <label>Set Greeting</label>
        <locationX>50</locationX>
        <locationY>650</locationY>
        <assignmentItems>
            <assignToReference>Get_Opportunity.MembershipGreeting__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>REPLACE</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Oppy</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Addressee_Override</name>
        <label>Addressee Override?</label>
        <locationX>182</locationX>
        <locationY>242</locationY>
        <defaultConnector>
            <targetReference>Greeting_Override</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>No_AddresseeOverride</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Opportunity.OverrideAddresseeAndGreeting__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Addressee</targetReference>
            </connector>
            <label>No-AddresseeOverride</label>
        </rules>
    </decisions>
    <decisions>
        <name>Greeting_Override</name>
        <label>Greeting Override?</label>
        <locationX>182</locationX>
        <locationY>542</locationY>
        <defaultConnector>
            <targetReference>Update_Oppy</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>No_Greeting_Override</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Opportunity.OverrideAddresseeAndGreeting__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Greeting</targetReference>
            </connector>
            <label>No-Greeting Override</label>
        </rules>
    </decisions>
    <description>Recalculates the Membership Addressee &amp; Membership Greeting fields if not overriden.</description>
    <environments>Default</environments>
    <interviewLabel>Recalculate Names {!$Flow.CurrentDateTime}</interviewLabel>
    <isOverridable>true</isOverridable>
    <label>Recalculate Membership Names</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>Get_Opportunity</name>
        <label>Get Opportunity</label>
        <locationX>182</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Addressee_Override</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Oppy</name>
        <label>Update Oppy</label>
        <locationX>182</locationX>
        <locationY>842</locationY>
        <connector>
            <targetReference>DoneScreen</targetReference>
        </connector>
        <inputReference>Get_Opportunity</inputReference>
    </recordUpdates>
    <runInMode>SystemModeWithSharing</runInMode>
    <screens>
        <name>DoneScreen</name>
        <label>Done</label>
        <locationX>182</locationX>
        <locationY>950</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>Done</name>
            <fieldText>&lt;p&gt;Done.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Opportunity</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
