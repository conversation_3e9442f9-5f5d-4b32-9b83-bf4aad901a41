<!--
  ~ Copyright (c) 2024-2025. Foglight Solutions, Inc. All Rights Reserved.
  ~
  ~ The code below is part of the Foglight Arts & Culture package and is not
  ~ to be redistributed without express written consent by Foglight.
  -->

<!--
 - Created by edwardblazer on 5/31/23.
 -->

<!-- Recalculate Rollups -->
<template>
    <lightning-quick-action-panel header="Recalculating Rollups">
        <div slot="footer">
            <lightning-button variant="brand" label="Ok" onclick={handleClose} class="slds-m-left_x-small" aria-haspopup="modal" disabled={saveDisabled}></lightning-button>
        </div>
        <template if:true={hasRenderedCallback}>
            <div style="display: none">{recordId}</div>

            <template if:true={doneLoading}>
                <template if:true={message.isSuccess}>
                    <div class="slds-text-heading_small">Rollups are being recalculated and will be completed shortly. You may safely close this dialog.</div>
                </template>
                <template if:false={message.isSuccess}>
                    <div class="slds-text-heading_small slds-p-around_xxx-small">Errors while recalculating rollups:</div>
                    <ul class="slds-list_dotted slds-p-around_xxx-small">
                        <template iterator:it={message.messages}><li key={it.value}>{it.value.msgText}</li></template>
                    </ul>
                </template>
            </template>
            <template if:false={doneLoading}>
                <lightning-spinner alternative-text="Loading..." variant="brand" size="medium" class="slds-is-static"></lightning-spinner>
            </template>
        </template>
    </lightning-quick-action-panel>
</template>