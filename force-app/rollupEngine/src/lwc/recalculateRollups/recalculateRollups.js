/*
 * Copyright (c) 2024-2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 5/31/23.
 */

import {api, LightningElement} from 'lwc';
import runNow from "@salesforce/apex/BatchFLRollup.runNow";
import {CloseActionScreenEvent} from "lightning/actions";
import {handleError} from "c/flModalBase";

export default class RecalculateRollups extends LightningElement {
    _recordId;

    hasRenderedCallback = false;
    doneLoading = false;
    closeTimer;

    message;
    set recordId(value) {
        if (value && this._recordId == null && this.hasRenderedCallback) {
            console.log("Got record id: " + value);
            this._recordId = value;

            // invoke the server-side
            runNow({
                targetId: this._recordId
            }).then(result => {
                console.log(result);
                this.message = {
                    isSuccess: true
                };

                this.doneLoading = true;
                const parentThis = this;

                //if (this.message.isSuccess) {
                    this.closeTimer = setInterval(function () {
                        clearInterval(parentThis.closeTimer);
                        parentThis.closeTimer = undefined;
                        parentThis.handleClose();
                    }, 2500);
                //}
            }).catch(err => {
                handleError(err);
                this.doneLoading = true;
            });
        }
    }
    @api get recordId() {
        return this._recordId;
    }

    connectedCallback() {
        console.log('connectedCallback');
    }

    renderedCallback() {
        console.log('renderedCallback');
        if (!this.hasRenderedCallback) {
            this.hasRenderedCallback = true;
        }
    }

    handleClose() {
        this.dispatchEvent(new CloseActionScreenEvent({bubbles: true, composed: true}));
    }
}