<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>MasterLabel</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>DeveloperName</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Description__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Operation__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>FLRollupFilter__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>TimeFrame__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <emptySpace>true</emptySpace>
            </layoutItems>
            <layoutItems>
                <emptySpace>true</emptySpace>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsActive__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Mode__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TimeFrameNumber__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Rollup Details</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>TargetObject__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>RelationshipPathToTarget__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>DetailObject__c</field>
            </layoutItems>
            <layoutItems>
                <emptySpace>true</emptySpace>
            </layoutItems>
            <layoutItems>
                <emptySpace>true</emptySpace>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsDistinctAggregates__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ParentObject__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>RelationshipPathToParent__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>TargetField__c</field>
            </layoutItems>
            <layoutItems>
                <emptySpace>true</emptySpace>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>FieldToRollUp__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>AmountField__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>DateField__c</field>
            </layoutItems>
            <layoutItems>
                <emptySpace>true</emptySpace>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ParentAmountField__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>NamespacePrefix</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsProtected</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00h8B000001KEMq</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>0</sizeY>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
