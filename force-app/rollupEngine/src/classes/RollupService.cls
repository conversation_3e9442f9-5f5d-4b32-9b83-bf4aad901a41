/**
 * Created by edwardblazer on 6/30/23.
 */

public inherited sharing class RollupService {
    private static final Map<Id,SOQL.WhereStmt> filters  = new Map<Id,SOQL.WhereStmt>();

    @TestVisible
    private static Type stateClass_Test;

    private final List<Rollup> rollups;
    private final Set<String> modes;
    private final String detailObject;
    private final String targetObject;
    private final String stateId;
    private final Type stateClass;
    private final Map<Id,RollupService.TargetRecord> targetRecords;

    public RollupService(String detailObject, Set<String> modes, String stateId) {
        this.detailObject   = detailObject;
        this.rollups        = getRollups(detailObject, modes);
        this.modes          = modes;
        this.stateId        = stateId;
        this.targetRecords  = new Map<Id,RollupService.TargetRecord>();

        if (stateClass_Test == null) {
            Request rq = Request.getCurrent();
            switch on (rq.getQuiddity()) {
                when BATCH_ACS, BATCH_APEX, BATCH_CHUNK_PARALLEL, BATCH_CHUNK_SERIAL {
                    this.stateClass = BatchChainState.class;
                }
                when else {
                    this.stateClass = TxState.class;
                }
            }
        } else {
            this.stateClass = stateClass_Test;
        }
    }

    public RollupService(String detailObject, Set<Id> rollups, Set<String> modes, String stateId) {
        this.detailObject   = detailObject;
        this.rollups        = getRollups(rollups, modes);
        this.modes          = modes;
        this.stateId        = stateId;
        this.targetRecords  = new Map<Id,RollupService.TargetRecord>();

        if (stateClass_Test == null) {
            Request rq = Request.getCurrent();
            System.debug('Quiddity: ' + rq.getQuiddity());
            switch on (rq.getQuiddity()) {
                when BATCH_ACS, BATCH_APEX, BATCH_CHUNK_PARALLEL, BATCH_CHUNK_SERIAL {
                    this.stateClass = BatchChainState.class;
                }
                when else {
                    this.stateClass = TxState.class;
                }
            }
        } else {
            this.stateClass = stateClass_Test;
        }
    }

    public List<Rollup> getRollups() {
        return this.rollups;
    }

    public static List<Rollup> getRollups(String detailObject, Set<String> modes) {
        List<Rollup> rollups            = new List<Rollup>();
        System.debug('detailObject: ' + detailObject);
        System.debug('modes: ' + modes);
        for (Rollup rollup : getAllActiveRollups(modes)) {
            if (rollup.record.DetailObject__r.QualifiedApiName?.equalsIgnoreCase(detailObject)) {
                rollups.add(rollup);
            }
        }
        return rollups;
    }

    public static List<Rollup> getRollupsForTarget(String targetObject, Set<String> modes) {
        List<Rollup> rollups            = new List<Rollup>();
        System.debug('targetObject: ' + targetObject);
        System.debug('modes: ' + modes);
        for (Rollup rollup : getAllActiveRollups(modes)) {
            if (rollup.record.TargetObject__r?.QualifiedApiName?.equalsIgnoreCase(targetObject) == true) {
                rollups.add(rollup);
            }
        }
        return rollups;
    }

    public static List<Rollup> getRollups(Set<Id> rollupIds, Set<String> modes) {
        List<Rollup> rollups            = new List<Rollup>();
        System.debug('RollupIds: ' + rollupIds);
        System.debug('modes: ' + modes);
        for (Rollup record : getAllActiveRollups(modes)) {
            if (rollupIds?.contains(record.record.Id) == true) {
                rollups.add(record);
            }
        }
        return rollups;
    }

    public static Map<String,Rollup> getAllActiveRollupsByName(Set<String> modes) {
        Map<String,Rollup> rollups            = new Map<String,Rollup>();
        for (Rollup rlp : getAllActiveRollups(modes)) {
            rollups.put(rlp.record.DeveloperName, rlp);
        }
        return rollups;
    }

    public static List<Rollup> getAllActiveRollups(Set<String> modes) {
        List<Rollup> rollups            = new List<Rollup>();
        System.debug('modes: ' + modes);
        for (FLRollup__mdt record : [
                SELECT Id, Label, DeveloperName, QualifiedApiName

                        , IsDistinctAggregates__c, RelationshipPathToParent__c
                        , TimeFrame__c, TimeFrameNumber__c
                        , Operation__c
                        , RelationshipPathToTarget__c

                        , FLRollupFilter__c, FLRollupFilter__r.FilterObject__c, FLRollupFilter__r.FilterCriteria__c

                        , TargetObject__c, TargetObject__r.QualifiedApiName
                        , DetailObject__c, DetailObject__r.QualifiedApiName
                        , ParentObject__c, ParentObject__r.QualifiedApiName

                        , ParentAmountField__c, ParentAmountField__r.QualifiedApiName, ParentAmountField__r.EntityDefinition.QualifiedApiName
                        , AmountField__c, AmountField__r.QualifiedApiName, AmountField__r.EntityDefinition.QualifiedApiName
                        , DateField__c, DateField__r.QualifiedApiName, DateField__r.EntityDefinition.QualifiedApiName
                        , FieldToRollUp__c, FieldToRollUp__r.QualifiedApiName, FieldToRollUp__r.EntityDefinition.QualifiedApiName
                        , TargetField__c, TargetField__r.QualifiedApiName, TargetField__r.EntityDefinition.QualifiedApiName
                FROM FLRollup__mdt
                WHERE Mode__c IN :modes
                AND IsActive__c = TRUE
                ORDER BY DetailObject__c
        ]) {
            rollups.add(getRollup(record));
        }
        System.debug('rollups: ' + rollups);
        return rollups;
    }

    public static Rollup getRollup(FLRollup__mdt record) {
        SOQL.WhereStmt ws = filters.get(record.FLRollupFilter__c);
        if (ws == null && !String.isBlank(record.FLRollupFilter__r?.FilterCriteria__c)) {
            SOQL.ScannerImpl si = new SOQL.ScannerImpl(record.FLRollupFilter__r.FilterCriteria__c);
            ws                  = new SOQL.WhereStmt(null);
            ws.parse(si);
            //System.debug(LoggingLevel.FINEST, 'WhereStmt: ' + ws?.);
            filters.put(record.FLRollupFilter__c, ws);
        }
        return new Rollup(record, ws);
    }

    private RollupService.TargetRecord getTarget(SObject record) {
        RollupService.TargetRecord tr;
        if (record != null) {
            tr = this.targetRecords.get(record.Id);
            if (tr == null) {
                System.debug(LoggingLevel.FINEST, 'New TargetRecord for: ' + record.Id);
                tr = new RollupService.TargetRecord(record, this.stateClass, this.stateId);
                this.targetRecords.put(record.Id, tr);
            }
        }
        return tr;
    }

    public void zeroTargets(Set<Id> targetIds) {
        for (Id targetId : targetIds) {
            SObject newRec = targetId.getSobjectType().newSObject(targetId);
            System.debug('newRec: ' + newRec);
            TargetRecord tr = getTarget(newRec);
            System.debug('TargetRecord: ' + tr);
            for (Rollup r : this.rollups) {
                tr.zeroTargetField(r);
            }
        }
    }

    public void rollupRecords(DB.Query qry) {
        System.debug('rollupRecords: ' + qry?.toString());

        // Summarize the records
        for (SObject so : qry.go()) {
            RollupService.DetailRecord dr = new RollupService.DetailRecord(so); // perhaps this isnt' needed?

            for (RollupService.Rollup rollup : this.rollups) {
                System.debug('Evaluating rollup "' + rollup.record.DeveloperName + '" for detail record: ' + so.Id);

                SObject targetSObjectRec = rollup.getTargetRecord(so);
                if (targetSObjectRec != null) {
                    RollupService.TargetRecord tr = getTarget(targetSObjectRec);

                    tr.zeroTargetField(rollup);

                    if (rollup.qualify(dr)) {
                        rollup.doOperation(so, tr);
                    }
                }
            }
        }

        // Build the targetRecord collection
        Map<SObjectType,Map<Id,SObject>> targetsToUpdate = new Map<SObjectType,Map<Id,SObject>>();
        for (RollupService.TargetRecord tr : targetRecords.values()) {
            // Serialize the state
            tr.serializeState(this.stateId);

            SObjectType sot = tr.record.getSObjectType();
            Map<Id,SObject> targets = targetsToUpdate.get(sot);
            if (targets == null) {
                targets = new Map<Id,SObject>();
                targetsToUpdate.put(sot, targets);
            }
            targets.put(tr.record.Id, tr.record);
        }

        if (targetsToUpdate.size() > 0) {
            // Bypass FLS & NPSP triggers
            Domain.suppressAll();
            NPSPUtils.disableTriggers();


            // Update the Target records
            List<String> errors = new List<String>();
            for (Map<Id, SObject> lst : targetsToUpdate.values()) {
                Database.DMLOptions dmlOpts = new Database.DMLOptions();
                dmlOpts.optAllOrNone = false;
                dmlOpts.allowFieldTruncation = true;
                dmlOpts.duplicateRuleHeader = new Database.DMLOptions.DuplicateRuleHeader();
                dmlOpts.duplicateRuleHeader.allowSave = true;

                List<SObject> recordsToUpdate = lst.values();
                Database.SaveResult[] srs = Database.update(recordsToUpdate, dmlOpts);
                for (Integer i = 0, j = srs.size(); i < j; i++) {
                    Database.SaveResult sr = srs.get(i);
                    if (!sr.success) {
                        SObject record = recordsToUpdate.get(i);
                        StringUtils.StringFormatter errFmt = StringUtils.format('{targetRecordId} ({targetRecordName}): {errorMessages}');
                        errFmt.set('{targetRecordId}', record.Id);
                        errFmt.set('{targetRecordName}', record.get('Name'));
                        errFmt.set('{errorMessages}', JSON.serialize(sr));
                        errors.add(errFmt.toString());
                    }
                }
            }

            // Log Errors to NPSP's standard error object todo log these somewhere, anywhere, Bueller?
            if (errors.size() > 0) {
                /*npsp__Error__c error        = new npsp__Error__c();
                error.npsp__Context_Type__c = 'flmas.BatchFLRollup';
                error.npsp__Datetime__c     = Datetime.now();
                error.npsp__Email_Sent__c   = false;
                error.npsp__Error_Type__c   = 'Rollup (' + String.join(new List<String>(this.modes), ',') + ')';
                error.npsp__Full_Message__c = String.join(errors, '\n\n');
                error.npsp__Object_Type__c  = this.detailObject;
                insert error;*/
                System.debug(LoggingLevel.ERROR, JSON.serializePretty(errors));
                throw new FLRollupException('Errors while saving target records. See debug log.');
            }
        }
    }

    public class Rollup {
        public final FLRollup__mdt record {get; private set;}
        public final SOQL.WhereStmt whereStmt {get; private set;}

        public final SObjectField targetField {get; private set;}
        public final Schema.DisplayType targetType {get; private set;}

        public final SObjectField amountField {get; private set;}
        public final Schema.DisplayType amountType {get; private set;}

        public final SObjectField dateField {get; private set;}
        public final Schema.DisplayType dateType {get; private set;}

        public final SObjectField rollupField {get; private set;}
        public final Schema.DisplayType rollupFieldType {get; private set;}

        public final SObjectField parentAmountField {get; private set;}
        public final Schema.DisplayType parentAmountType {get; private set;}

        private final RollupOperation operation;
        private final TimeFrame tFrame;
        private final Integer tFrameNumber;

        public Rollup(FLRollup__mdt record, SOQL.WhereStmt ws) {
            this.record             = record;
            this.whereStmt          = ws;

            this.targetField        = SObjectUtils.getSObjectField(record.TargetField__r);
            this.targetType         = this.targetField.getDescribe().getType();

            this.amountField        = SObjectUtils.getSObjectField(record.AmountField__r);
            this.amountType         = this.amountField?.getDescribe().getType();

            this.dateField          = SObjectUtils.getSObjectField(record.DateField__r);
            this.dateType           = this.dateField?.getDescribe().getType();

            this.rollupField        = SObjectUtils.getSObjectField(record.FieldToRollUp__r);
            this.rollupFieldType    = this.rollupField?.getDescribe().getType();

            this.parentAmountField  = SObjectUtils.getSObjectField(record.ParentAmountField__r);
            this.parentAmountType   = this.parentAmountField?.getDescribe().getType();

            String opClass = 'RollupService.Op' + record.Operation__c;
            Type opClassType = Type.forName('flmas', opClass);

            this.operation = (RollupOperation)opClassType?.newInstance();

            if (record.TimeFrameNumber__c == null) {
                this.tFrameNumber   = 0;
            } else {
                this.tFrameNumber   = record.TimeFrameNumber__c.intValue();
            }

            this.tFrame = TimeFrame.valueOf(record.TimeFrame__c);
        }

        public Set<String> getFieldsToQuery() {
            Set<String> fields = new Set<String>();

            fields.add(record.AmountField__r?.QualifiedApiName);
            fields.add(record.DateField__r?.QualifiedApiName);
            fields.add(record.FieldToRollUp__r?.QualifiedApiName);
            if (record.IsDistinctAggregates__c) {
                fields.add(record.RelationshipPathToParent__c + '.Id');
                fields.add(record.RelationshipPathToParent__c + '.' + record.ParentAmountField__r?.QualifiedApiName);
            }
            fields.add(record.RelationshipPathToTarget__c + '.Id');
            //fields.add(record.RelationshipPathToTarget__c + '.Name');
            fields.add(record.RelationshipPathToTarget__c + '.RollupState__c');
            fields.add(record.RelationshipPathToTarget__c + '.' + record.TargetField__r.QualifiedApiName);
            if (whereStmt != null) {
                fields.addAll(whereStmt.getAllFieldPaths());
            }

            return fields;
        }

        public String getRelationshipPath() {
            return this.record.RelationshipPathToTarget__c;
        }

        public String getRelationshipPathToParent() {
            return this.record.RelationshipPathToParent__c;
        }

        public String getTargetObject() {
            return this.record.TargetObject__c;
        }

        /**
         * @param rec An SObject record to test
         *
         * @return true if the supplied SObject is not null and passes the conditions of this rollup's filter AND timeframe
         */
        public Boolean qualify(DetailRecord rec) {
            Boolean ret = false;
            if (rec != null) {
                System.debug('Qualifying record for rollup "' + this.record.DeveloperName + '": ' + rec);

                Boolean qualified = rec.qualifiedFilters.get(this.record.FLRollupFilter__c);
                if (qualified == null) {
                    qualified = this.whereStmt?.interpret(rec.record, null) ?? true;
                    rec.qualifiedFilters.put(this.record.FLRollupFilter__c, qualified);
                }

                if (qualified) {
                    System.debug(LoggingLevel.FINEST, 'Time Frame: ' + this.tFrame?.name());
                    if (this.tFrame == TimeFrame.AllTime) {
                        ret = true;
                    } else {
                        Date d = (Date) rec.record.get(this.dateField);
                        Date startDate;
                        Date endDate;

                        switch on this.tFrame {
                            when YearsAgo {
                                startDate  = Date.newInstance(Date.today().year(), 1, 1).addYears(this.tFrameNumber * -1);
                                endDate    = startDate.addYears(1).addDays(-1);
                            }
                            when FiscalYearsAgo {
                                startDate  = DateTimeUtils.getCurrentFiscalYearStartDate().addYears(this.tFrameNumber * -1);
                                endDate    = startDate.addYears(1).addDays(-1);
                            }
                            when DaysAgo {
                                startDate      = Date.today().addDays(this.tFrameNumber * -1);
                                endDate        = Date.today();
                            }
                        }

                        ret = startDate <=d && d <= endDate;

                        System.debug(LoggingLevel.FINEST, startDate + ' <= ' + d + ' <= ' + endDate + ': ' + ret);
                    }
                }
            }

            System.debug('Record qualified for rollup "' + this.record.DeveloperName + '": ' + ret);
            return ret;
        }

        public SObject getTargetRecord(SObject rec) {
            return (SObject)SObjectUtils.getValueForPath(rec, record.RelationshipPathToTarget__c);
        }

        public void zeroTargetField(SObject rec) {
            if (rec != null) {
                System.debug('Zeroing target field "' + this.targetField + '" on "' + this.getTargetObject());
                switch on (this.targetType) {
                    when CURRENCY,DOUBLE,INTEGER,LONG,PERCENT {
                        rec.put(this.targetField, 0);
                    }
                    when else {
                        rec.put(this.targetField, null);
                    }
                }
            }
        }

        public void doOperation(SObject detailRecord, TargetRecord tr) {
            System.debug('doing operation: ' + this.operation.toString());
            System.debug('Rollup: ' + this);
            this.operation?.doRollup(detailRecord, tr, this);
        }

        /**
         *
         * if first, last, largest, or smallest AND
         * @return one of:
         *   sum/largest/smallest: decimal
         *   first/last: date
         */
        public Type getStateType() {
            if (this.operation instanceof OpFirst || this.operation instanceof OpLast) {
                return Date.class;
            }
            return Decimal.class;
        }
    }

    public class DetailRecord {
        private final SObject record;
        private final Map<Id,Boolean> qualifiedFilters;

        public DetailRecord(SObject record) {
            this.record = record;
            this.qualifiedFilters   = new Map<Id,Boolean>();
        }
    }

    public class TargetRecord {
        public final SObject record {get; private set;}

        @TestVisible
        private final RollupState state;

        public TargetRecord(SObject record, Type stateClass, String stateId) {
            this.record = record;

            state   = (RollupState)stateClass.newInstance();
            state.init(this, stateId);
        }

        public void zeroTargetField(Rollup r) {
            System.debug(LoggingLevel.FINEST, 'Zeroing target field? "' + r.targetField + '" on "' + r.getTargetObject());
            if (!this.state.isRollupZeroed(r)) {
                r.zeroTargetField(this.record);
                this.state.zeroRollup(r);
            }
        }

        public void serializeState(String stateId) {
            if (stateId != null) {
                System.debug(((BatchChainState)this.state)?.serialize());
                this.record.put('RollupState__c', ((BatchChainState)this.state)?.serialize());
            }
        }
    }

    public interface RollupOperation {
        void doRollup(SObject detail, TargetRecord target, Rollup rlup);
    }

    public class OpCount implements RollupOperation {
        public void doRollup(SObject detail, TargetRecord target, Rollup rlup) {
            Double l;
            switch on (rlup.targetType) {
                when CURRENCY,DOUBLE,PERCENT,LONG,INTEGER {
                    l = (Double)target.record.get(rlup.targetField) ?? 0;
                    l+=1;
                    //target.record.put(rlup.targetField, l);
                }
            }

            if (rlup.record.IsDistinctAggregates__c) {
                SObject parent = (SObject)SObjectUtils.getValueForPath(detail, rlup.getRelationshipPathToParent());

                if (target.state.hasIntermediate(rlup, parent.Id)) {
                    return;
                }
                target.state.addIntermediate(rlup, parent.Id, 1);
            }

            target.record.put(rlup.targetField, l);
        }
    }

    public class OpSum implements RollupOperation {
        public void doRollup(SObject detail, TargetRecord target, Rollup rlup) {
            System.debug(LoggingLevel.FINEST, rlup.record.Label);
            Object total = target.record.get(rlup.targetField) ?? 0;
            System.debug(LoggingLevel.FINEST, 'total: ' + total);       // 1432.95
            switch on (rlup.targetType) {
                when CURRENCY,DOUBLE,PERCENT,LONG,INTEGER {
                    Decimal amountToRollup = (Decimal) detail.get(rlup.rollupField) ?? 0.0;
                    System.debug(LoggingLevel.FINEST, 'amountToRollup1: ' + amountToRollup);        // 2242.35

                    if (rlup.record.IsDistinctAggregates__c) {
                        // Determine the amount to rollup, ensuring the result doesn't exceed the parent's value

                        SObject parent          = (SObject) SObjectUtils.getValueForPath(detail, rlup.getRelationshipPathToParent());

                        System.debug(rlup.record.ParentAmountField__r.QualifiedApiName);
                        Decimal parentAmount    = (Decimal) SObjectUtils.getValueForPath(parent, rlup.record.ParentAmountField__r.QualifiedApiName);
                        System.debug(LoggingLevel.FINEST, 'Parent Id: ' + parent.Id);
                        System.debug(LoggingLevel.FINEST, 'parentAmount: ' + parentAmount);         // 4602.56

                        // The amount already rolled-up for this parent
                        System.debug(LoggingLevel.FINEST, 'State Intermediate Id: ' + target.state.getIntermediateId(rlup));
                        Decimal rolledupAmount    = ((Decimal) target.state.getIntermediateValue(rlup, parent.Id)) ?? 0.0;
                        System.debug(LoggingLevel.FINEST, 'State Intermediate Value (already rolled-up): ' + rolledupAmount);   // 1432.95

                        rolledupAmount += amountToRollup;
                        System.debug(LoggingLevel.FINEST, 'rolledUpAmount2: ' + rolledupAmount);
                        if (rolledupAmount > parentAmount) {
                            amountToRollup -= rolledupAmount - parentAmount;
                            rolledupAmount = parentAmount;
                        }
                        System.debug(LoggingLevel.FINEST, 'rolledUpAmount3: ' + rolledupAmount);

                        /*if (rolledupAmount < parentAmount) {
                            rolledupAmount += amountToRollup; // 1432.95 += 2242.35 = 3675.30
                            System.debug(LoggingLevel.FINEST, 'rolledUpAmount2: ' + rolledupAmount); // 3675.30
                            amountToRollup = Math.min(rolledupAmount, parentAmount);
                            *//*if (rolledupAmount > parentAmount) {
                                amountToRollup = rolledupAmount - parentAmount;
                            }*//*
                            System.debug(LoggingLevel.FINEST, 'amountToRollup2: ' + amountToRollup); // 3675.30
                        } else {
                            amountToRollup = 0;
                        }*/
                        target.state.addIntermediate(rlup, parent.Id, rolledupAmount);
                    }

                    System.debug('total: ' + total); // 1432.95
                    System.debug('amountToRollup: ' + amountToRollup); // 3675.30

                    total = ((Decimal)total) + amountToRollup;
                    System.debug(LoggingLevel.FINEST, 'total2: ' + total); // 5108.25
                }
            /*when INTEGER,LONG {
                Long d = (Long)val;
                d += (Long)detail.get(rlup.rollupField);
                val = d;
            }*/
            }

            target.record.put(rlup.targetField, total);
        }
    }

    /*
     This can't be done via triggers unless the trigger looks at ALL detail records which can likely
     exceed the row limit.

     Instead, use two rollups and a flow or formula:
      - 1 rollup for sum
      - 1 for count
      - A flow/formula to calculate: sum / count

    public class OpAverage implements RollupOperation {
        public void doRollup(SObject detail, TargetRecord target, Rollup rlup) {
            Long count = (Long)target.state.getState(rlup);
            if (count == null) {
                count = 0;
            }
            count++;
            target.state.addState(rlup, count);

            Object val = target.record.get(rlup.targetField);
            switch on (rlup.targetType) {
                when DOUBLE {
                    Double d = (Double)val;
                    d += (Double)detail.get(rlup.rollupField);
                    d /= count;
                    val = d;
                }
                when CURRENCY,PERCENT {
                    Decimal d = (Decimal)val;
                    d += (Decimal)detail.get(rlup.rollupField);
                    d /= count;
                    val = d;
                }
                when INTEGER,LONG {
                    Long d = (Long)val;
                    d += (Long)detail.get(rlup.rollupField);
                    d /= count;
                    val = d;
                }
            }

            target.record.put(rlup.targetField, val);
        }
    } */

    public class OpLargest implements RollupOperation {
        public void doRollup(SObject detail, TargetRecord target, Rollup rlup) {

            Object valToRollup  = target.record.get(rlup.targetField); // The current value on the target record or 0
            Object detailAmt    = detail.get(rlup.amountField);
            Object currentSmallest;
            Boolean useState    = false;
            if (rlup.amountField == rlup.rollupField) {
                /*
                 If the rollupField and the amountField are the same, don't use state.
                 Instead, compare the details' value of rollupField to the target's
                 current value of targetField
                 */
                currentSmallest = valToRollup;
            } else {
                // Use State
                useState = true;
                currentSmallest = target.state.getState(rlup);
            }

            switch on (rlup.amountType) {
                when DOUBLE,LONG,INTEGER {
                    Double c = (Double)currentSmallest;
                    Double v = (Double)detailAmt;
                    if (c == null || v >= c) {
                        if (useState) {
                            target.state.addState(rlup, v);
                        }
                        valToRollup = detail.get(rlup.rollupField);
                    }
                }
                when CURRENCY,PERCENT {
                    Decimal v = (Decimal)detailAmt;
                    Decimal c = (Decimal)currentSmallest;
                    if (c == null || v >= c) {
                        if (useState) {
                            target.state.addState(rlup, v);
                        }
                        valToRollup = detail.get(rlup.rollupField);
                    }
                }
            }

            target.record.put(rlup.targetField, valToRollup);
        }
    }

    public class OpSmallest implements RollupOperation {
        public void doRollup(SObject detail, TargetRecord target, Rollup rlup) {

            Object valToRollup  = target.record.get(rlup.targetField); // The current value on the target record or 0
            System.debug(LoggingLevel.FINEST, 'OpSmallest - valToRollup: ' + valToRollup);

            Object detailAmt    = detail.get(rlup.amountField);
            Object currentSmallest;
            Boolean useState    = false;
            if (rlup.amountField == rlup.rollupField) {
                /*
                 If the rollupField and the amountField are the same, don't use state.
                 Instead, compare the details' value of rollupField to the target's
                 current value of targetField
                 */
                currentSmallest = valToRollup;
            } else {
                // Use State
                useState = true;
                currentSmallest = target.state.getState(rlup);
            }

            System.debug(LoggingLevel.FINEST, 'OpSmallest - currentSmallest: ' + currentSmallest);
            System.debug(LoggingLevel.FINEST, 'OpSmallest - useState: ' + useState);

            switch on (rlup.amountType) {
                when DOUBLE,LONG,INTEGER {
                    Double c = (Double)currentSmallest;
                    Double v = (Double)detailAmt;
                    if (c == null || v <= c) {
                        if (useState) {
                            target.state.addState(rlup, v);
                        }
                        valToRollup = detail.get(rlup.rollupField);
                    }
                }
                when CURRENCY,PERCENT {
                    Decimal v = (Decimal)detailAmt;
                    Decimal c = (Decimal)(currentSmallest == 0 ? null : currentSmallest);
                    System.debug('OpSmallest - c: ' + c);
                    System.debug('OpSmallest - v: ' + v);

                    if (c == null || v <= c) {
                        if (useState) {
                            target.state.addState(rlup, v);
                        }
                        valToRollup = detail.get(rlup.rollupField);
                    }
                }
            }

            System.debug(LoggingLevel.FINEST, 'OpSmallest - valToRollup: ' + valToRollup);

            target.record.put(rlup.targetField, valToRollup);
        }
    }

    public class OpFirst implements RollupOperation {
        public void doRollup(SObject detail, TargetRecord target, Rollup rlup) {

            Object valToRollup  = target.record.get(rlup.targetField);
            Object detailDate   = detail.get(rlup.dateField);
            Object currentFirst;
            Boolean useState    = false;
            if (rlup.dateField == rlup.rollupField) {
                /*
                 If the rollupField and the dateField are the same, don't use state.
                 Instead, compare the details' value of rollupField to the target's
                 current value of targetField
                 */
                currentFirst = valToRollup;
            } else {
                // Use State
                currentFirst = target.state.getState(rlup);
                useState = true;
            }

            System.debug('rlup.dateField == rlup.rollupField: ' + (rlup.dateField == rlup.rollupField));
            System.debug('valToRollup: ' + valToRollup);
            System.debug('currentFirst: ' + currentFirst);

            switch on (rlup.dateType) {
                when DATE {
                    Date c = (Date)currentFirst;
                    Date v = (Date)detailDate;
                    if (c == null || v <= c) {
                        if (useState) {
                            target.state.addState(rlup, v);
                        }
                        valToRollup = detail.get(rlup.rollupField);
                    }
                }
                when DATETIME {
                    Datetime v = (Datetime)currentFirst;
                    Datetime c = (Datetime)detailDate;
                    if (c == null || v <= c) {
                        if (useState) {
                            target.state.addState(rlup, v);
                        }
                        valToRollup = detail.get(rlup.rollupField);
                    }
                }
            }

            target.record.put(rlup.targetField, valToRollup);
        }
    }

    public class OpLast implements RollupOperation {
        public void doRollup(SObject detail, TargetRecord target, Rollup rlup) {

            Object valToRollup  = target.record.get(rlup.targetField);
            Object detailDate   = detail.get(rlup.dateField);
            Object currentLast;
            Boolean useState    = false;
            if (rlup.dateField == rlup.rollupField) {
                /*
                 If the rollupField and the dateField are the same, don't use state.
                 Instead, compare the details' value of rollupField to the target's
                 current value of targetField
                 */
                currentLast = valToRollup;
            } else {
                // Use State
                currentLast = target.state.getState(rlup);
                useState = true;
            }

            switch on (rlup.dateType) {
                when DATE {
                    Date c = (Date)currentLast;
                    Date v = (Date)detailDate;
                    if (c == null || v >= c) {
                        if (useState) {
                            target.state.addState(rlup, v);
                        }
                        valToRollup = detail.get(rlup.rollupField);
                    }
                }
                when DATETIME {
                    Datetime v = (Datetime)currentLast;
                    Datetime c = (Datetime)detailDate;
                    if (c == null || v >= c) {
                        if (useState) {
                            target.state.addState(rlup, v);
                        }
                        valToRollup = detail.get(rlup.rollupField);
                    }
                }
            }

            target.record.put(rlup.targetField, valToRollup);
        }
    }


    public interface RollupState {
        void init(TargetRecord record, String stateId);
        Boolean isValid(String stateId);
        Boolean isRollupZeroed(Rollup r);
        void zeroRollup(Rollup r);
        void addState(Rollup rlup, Object val);
        Object getState(Rollup rlup);
        Boolean hasIntermediate(Rollup rlup, Id parent);
        void addIntermediate(Rollup rlup, Id Parent, Object value);
        Object getIntermediateValue(Rollup rlup, Id parent);
        Id getIntermediateId(Rollup rlup);
    }

    public class TxState implements RollupState {
        private transient final Set<Id> zeroedRollups = new Set<Id>();
        private transient final Map<Id,Map<Id,Object>> uniqueIntermediate    = new Map<Id,Map<Id,Object>>();

        public void init(TargetRecord record, String stateId) {
        }

        public Boolean isValid(String stateId) {
            return true;
        }

        public Boolean isRollupZeroed(Rollup r) {
            return this.zeroedRollups.contains(r.record.Id);
        }

        public void zeroRollup(Rollup r) {
            this.zeroedRollups.add(r.record.Id);
            uniqueIntermediate.put(r.record.Id, new Map<Id,Object>());
        }

        public void addState(Rollup rlup, Object val) {
        }

        public Object getState(Rollup rlup) {
            return null;
        }

        public Boolean hasIntermediate(Rollup rlup, Id parent) {
            Map<Id,Object> intermediates = getIntermediate(rlup);
            return intermediates.containsKey(parent);
        }
        public void addIntermediate(Rollup rlup, Id parent, Object value) {
            Map<Id,Object> intermediates = getIntermediate(rlup);
            intermediates.put(parent, value);
        }
        public Object getIntermediateValue(Rollup rlup, Id parent) {
            return getIntermediate(rlup).get(parent);
        }
        public Map<Id,Object> getIntermediate(Rollup rlup) {
            Map<Id,Object> intermediates = uniqueIntermediate.get(rlup.record.Id);
            if (intermediates == null) {
                intermediates = new Map<Id,Object>();
                uniqueIntermediate.put(rlup.record.Id, intermediates);
            }
            return intermediates;
        }

        public Id getIntermediateId(Rollup rlup) {
            return null;
        }
    }

    public class BatchChainState implements RollupState {
        private transient String i;               // The state identifier. The id of the current batch state

        @TestVisible
        private Map<String,RollupFieldState> rv;   // Rollup Values

        public BatchChainState() {
            this.rv             = new Map<String,RollupFieldState>();
        }

        public void init(TargetRecord targetRecord, String stateId) {
            String jsonStr = (String)targetRecord.record.get('RollupState__c');
            if (!String.isBlank(jsonStr)) {
                try {
                    BatchChainState bcs = (BatchChainState) JSON.deserialize(jsonStr, BatchChainState.class);

                    System.debug(LoggingLevel.FINEST, 'current bcs: ' + bcs);

                    this.rv = bcs.rv ?? new Map<String,RollupFieldState>();
                } catch (System.Exception je) {
                    System.debug('Couldn\'t deserialize Batch Chain State: ' + je);
                    System.debug(jsonStr);
                }
            }
            this.i = stateId;
        }

        public Boolean isValid(String stateId) {
            return this.i == null || this.i.equalsIgnoreCase(stateId);
        }

        public void addState(Rollup rlup, Object val) {
            getFieldState(rlup).setState(val);
        }
        public Object getState(Rollup rlup) {
            return getFieldState(rlup).getState();
        }

        public Boolean isRollupZeroed(Rollup r) {
            System.debug(this.i);
            RollupFieldState rfs = getFieldState(r);
            return rfs.s != null && rfs.s.equals(this.i);
        }

        public void zeroRollup(Rollup r) {
            getFieldState(r).setBatchStateId(this.i);
        }

        public Boolean hasIntermediate(Rollup rlup, Id parent) {
            return parent?.equals(this.rv?.get(truncatedRollupId(rlup)).i);
        }
        public void addIntermediate(Rollup rlup, Id parent, Object value) {
            RollupFieldState rfs = getFieldState(rlup);
            rfs.setIntermediate(parent);
            rfs.setIntermediateValue((Decimal)value);
        }
        public Id getIntermediateId(Rollup rlup) {
            return getFieldState(rlup).i;
        }
        public Object getIntermediateValue(Rollup rlup, Id parent) {
            RollupFieldState rfs = getFieldState(rlup);
            return rfs.i == parent ? rfs.iv : null;
        }
        public RollupFieldState getFieldState(Rollup rlup) {
            String s = truncatedRollupId(rlup);
            RollupFieldState rfs = this.rv.get(s);
            if (rfs == null) {
                rfs = new RollupFieldState();
                this.rv.put(s, rfs);
            }
            return rfs;
        }

        public String serialize() {
            if (this.rv?.size() == 0) {
                this.rv = null;
            }
            return JSON.serialize(this, true);
        }
    }

    @TestVisible
    private class RollupFieldState {
        @TestVisible
        private String s;   // The batch id that last computed this field state
        @TestVisible
        private Date vd;    // The current rollup value state if a date
        @TestVisible
        private Decimal vi; // The current rollup value state if a decimal
        @TestVisible
        private String i;   // The Id of the current intermediate record;
        @TestVisible
        private Decimal iv; // The current rolled-up value for the intermediate record;

        private RollupFieldState() {
            System.debug('RollupFieldState:init');
            this.iv = 0.0;
        }
        private RollupFieldState(Object v) {
            this();
            setState(v);
        }

        public void setState(Object v) {
            if (v instanceof Date) {
                this.vd = (Date)v;
            } else if (v instanceof Decimal) {
                this.vi = (Decimal)v;
            }
        }
        private Object getState() {
            if (vd != null) {
                return vd;
            }
            return vi;
        }

        private void setBatchStateId(String s) {
            this.s  = s;
            this.iv = 0;
            this.i  = null;
        }
        private void setIntermediateValue(Decimal iv) {
            System.debug('setIntermediateValue: ' + iv + ' for: ' + this.i);
            this.iv = iv;
        }
        private void setIntermediate(Id i) {
            System.debug('setIntermediate: ' + i);
            this.i = i;
        }
    }

    @TestVisible
    private static String truncatedRollupId(Rollup rlup) {
        return truncatedRollupId(rlup.record.Id);
    }
    @TestVisible
    private static String truncatedRollupId(Id recordId) {
        return ((String)recordId).substring(9,15);
    }

    private enum TimeFrame {
        AllTime
        , YearsAgo
        , FiscalYearsAgo
        , DaysAgo
    }

    public class FLRollupException extends Exception {}
}