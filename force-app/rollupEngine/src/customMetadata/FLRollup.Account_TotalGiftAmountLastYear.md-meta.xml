<?xml version="1.0" encoding="UTF-8"?>
<CustomMetadata xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <label>Account: Total Gift Amount: Last Year</label>
    <protected>false</protected>
    <values>
        <field>AmountField__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>DateField__c</field>
        <value xsi:type="xsd:string">TransactionDate</value>
    </values>
    <values>
        <field>Description__c</field>
        <value xsi:type="xsd:string">Calculates the total gifts for the donor (Hard Credit only) for last year.</value>
    </values>
    <values>
        <field>DetailObject__c</field>
        <value xsi:type="xsd:string">GiftTransaction</value>
    </values>
    <values>
        <field>FLRollupFilter__c</field>
        <value xsi:type="xsd:string">GiftTx_Paid</value>
    </values>
    <values>
        <field>FieldToRollUp__c</field>
        <value xsi:type="xsd:string">CurrentAmount</value>
    </values>
    <values>
        <field>IsActive__c</field>
        <value xsi:type="xsd:boolean">true</value>
    </values>
    <values>
        <field>IsDistinctAggregates__c</field>
        <value xsi:type="xsd:boolean">false</value>
    </values>
    <values>
        <field>Mode__c</field>
        <value xsi:type="xsd:string">Both</value>
    </values>
    <values>
        <field>Operation__c</field>
        <value xsi:type="xsd:string">Sum</value>
    </values>
    <values>
        <field>ParentAmountField__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>ParentObject__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>RelationshipPathToParent__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>RelationshipPathToTarget__c</field>
        <value xsi:type="xsd:string">Donor</value>
    </values>
    <values>
        <field>TargetField__c</field>
        <value xsi:type="xsd:string">TotalGiftAmountLastYear__c</value>
    </values>
    <values>
        <field>TargetObject__c</field>
        <value xsi:type="xsd:string">Account</value>
    </values>
    <values>
        <field>TimeFrameNumber__c</field>
        <value xsi:type="xsd:double">1</value>
    </values>
    <values>
        <field>TimeFrame__c</field>
        <value xsi:type="xsd:string">YearsAgo</value>
    </values>
</CustomMetadata>