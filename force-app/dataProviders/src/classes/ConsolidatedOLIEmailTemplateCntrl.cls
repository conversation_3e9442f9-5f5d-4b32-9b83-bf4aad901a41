/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by arbel on 11/21/2023.
 */

public with sharing class ConsolidatedOLIEmailTemplateCntrl {
	public Id opportunityId { get; set; }
	public Boolean showPlainTable { get; set; }
	public Integer richStyle { get; set; }
	public String stylingOverride { get; set; }
	public String configName { get; set; }

	public PageData pageData {
		get {

			PageData returnPageData = new PageData();

			DataProviderUtility.GroupedDataResponse response = DataProviderUtility.getGroupedData(configName, opportunityId);

			System.debug('response in UT:  ' + JSON.serializePretty(response));

			List<Map<String, Object>> results = response.aggregateData;

			/*List<OpportunityLineItem> returnLineItems = new List<OpportunityLineItem>();
			for (String compositeKey : results.keySet()) {
				Map<String, Object> thisResult = results.get(compositeKey);

				System.debug('Working with ThisResult:  ' + JSON.serializePretty(thisResult));

				OpportunityLineItem compositeOppLine = new OpportunityLineItem();

				compositeOppLine.Quantity = Integer.valueOf(thisResult.get('totalQuantity'));
				compositeOppLine.UnitPrice = (Decimal) thisResult.get('UnitPrice');
				compositeOppLine.tixtrack__TicketureEventSessionDateTime__c = (Datetime) thisResult.get('tixtrack__TicketureEventSessionDateTime__c');
				compositeOppLine.TotalPrice = (Decimal) thisResult.get('totalPrice');
				Product2 parentProduct = new Product2(
						Id = (Id) thisResult.get('Product2Id'),
						Name = (String) thisResult.get('Product2.Name'),
						ProductCode = (String) thisResult.get('Product2.ProductCode')
				);
				compositeOppLine.Product2 = parentProduct;
				compositeOppLine.Description = (String) thisResult.get('Description');

				returnLineItems.add(compositeOppLine);
			}*/

			returnPageData.aggregateOppLines = results;
			System.debug('returnPageData.aggregateOppLines:' + JSON.serializePretty(returnPageData.aggregateOppLines));
			for (Map<String, Object> recordAsMap : returnPageData.aggregateOppLines) {
				for (String fieldName : recordAsMap.keySet()) {
					Object fieldValue = recordAsMap.get(fieldName);
					// VF Component hates rendering nulls and throws errors, let's turn them into empty strings.
					//recordAsMap.put(fieldName, (fieldValue != null) ? fieldValue : '');
					recordAsMap.put(fieldName, Utils.nullValue(fieldValue, ''));
				}
			}

			for (DataProviderConfigurationField__mdt dpcf : response.orderedOutputFields) {
				returnPageData.fieldNamesAndLabels.put((dpcf.Alias__c != null) ? dpcf.Alias__c : dpcf.TargetFieldPath__c, dpcf.OutputLabel__c);
			}
			return returnPageData;
		}
		set;
	}

	public class PageData {
		public List<Map<String, Object>> aggregateOppLines { get; set; }
		public Map<String, String> fieldNamesAndLabels { get; set; }

		public PageData () {
			aggregateOppLines = new List<Map<String, Object>>();
			fieldNamesAndLabels = new Map<String, String>();
		}
	}
}