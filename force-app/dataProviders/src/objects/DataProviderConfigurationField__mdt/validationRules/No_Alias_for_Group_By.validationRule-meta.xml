<?xml version="1.0" encoding="UTF-8"?>
<ValidationRule xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>No_Alias_for_Group_By</fullName>
    <active>false</active>
    <errorConditionFormula>AND(
    NOT(ISBLANK(Alias__c))
				,
				ISPICKVAL(Operation__c, &apos;GROUP BY&apos;)
)</errorConditionFormula>
    <errorDisplayField>Alias__c</errorDisplayField>
    <errorMessage><PERSON><PERSON> is not allowed when using &apos;GROUP BY&apos; operator.</errorMessage>
</ValidationRule>
