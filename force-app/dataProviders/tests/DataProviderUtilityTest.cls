/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by arbel on 11/21/2023.
 */

@IsTest
public class DataProviderUtilityTest {
	static final Integer testBatchSize = 10;
	static final Decimal testUnitPrice = 100.00;
	static final Id householdRecordTypeId = SObjectUtils.getRecordTypeIdForObjectAndName(Account.SObjectType, 'Business_Account');

	@TestSetup
	public static void setup () {
		Account testHousehold = new Account(
				Name = 'test',
				RecordTypeId = householdRecordTypeId
		);
		insert testHousehold;
		Contact testContact = new Contact(
				FirstName = 'old',
				LastName = 'contact',
				AccountId = testHousehold.Id,
				tixtrack__TicketureIdentityId__c = '12345',
				tixtrack__TicketureStaffId__c = '12345',
				Email = '<EMAIL>'
		);
		insert testContact;
		Product2 testProduct = new Product2(
				Name = 'test product',
				IsActive = true,
				CanUseRevenueSchedule = true,
				ProductCode = '1234'
		);
		insert testProduct;
		PricebookEntry testPricebookEntry = new PricebookEntry(
				Product2Id = testProduct.Id,
				Pricebook2Id = Test.getStandardPricebookId(),
				IsActive = true,
				UnitPrice = 100
		);
		insert testPricebookEntry;

		Opportunity testOpportunity = new Opportunity(
				AccountId = testHousehold.Id,
				Name = 'test opp',
				Amount = 100,
				StageName = 'Closed Won',
				CloseDate = Date.today().addYears(-1),
				tixtrack__TixTrackPrimaryContact__c = testContact.Id,
				tixtrack__TicketureCartId__c = '213456'
		);
		insert testOpportunity;

		List<OpportunityLineItem> testOpportunityProducts = new List<OpportunityLineItem>();
		for (Integer i = 0; i < testBatchSize; i++) {
			testOpportunityProducts.add(new OpportunityLineItem(
					OpportunityId = testOpportunity.Id,
					Quantity = 1,
					UnitPrice = testUnitPrice,
					PricebookEntryId = testPricebookEntry.Id
			));
		}
		insert testOpportunityProducts;
	}

	@IsTest
	static void testSettingOfSessionStartDateString () {
		Datetime testDatetime = Datetime.now().addDays(1);

		List<OpportunityLineItem> olisToTest = [
				SELECT Id
				FROM OpportunityLineItem
		];
		for (OpportunityLineItem oli : olisToTest) {
			oli.tixtrack__TicketureEventSessionDateTime__c = testDatetime;
		}
		System.assertEquals(testBatchSize, olisToTest.size());

		Test.startTest();
		update olisToTest;
		Test.stopTest();

		List<OpportunityLineItem> olisAfterTest = [
				SELECT Id,
						SessionStartDateTime__c
				FROM OpportunityLineItem
		];
		System.assertEquals(testBatchSize, olisAfterTest.size());
		for (OpportunityLineItem oli : olisAfterTest) {
			System.assertEquals(testDatetime.format(OpportunityLineItemDomain.FORMAT_STRING_DATETIME), oli.SessionStartDateTime__c);
		}
	}

	//@IsTest
	static void testDataProviderNewWay () {
		List<OpportunityLineItem> testOppLines = [
				SELECT Id,
						OpportunityId
				FROM OpportunityLineItem
				LIMIT :testBatchSize / 2
		];

		Id testOppId;
		Datetime testDatetime = Datetime.now();
		for (OpportunityLineItem oli : testOppLines) {
			oli.tixtrack__TicketureEventSessionDateTime__c = testDatetime;
			testOppId = oli.OpportunityId;
		}

		Integer countToChange = Integer.valueOf(testBatchSize / 2 / 2);
		List<OpportunityLineItemSchedule> schedulesToUpdate = new List<OpportunityLineItemSchedule>();
		for (OpportunityLineItemSchedule olis : [
				SELECT Id,
						Revenue
				FROM OpportunityLineItemSchedule
				WHERE OpportunityLineItemId IN :testOppLines
				ORDER BY OpportunityLineItemId
		]) {
			System.debug('olis.Revenue:  ' + olis.Revenue);
			System.debug('testUnitPrice:  ' + testUnitPrice);
			System.debug('countToChange:  ' + countToChange);
			if (countToChange > 0 && olis.Revenue == testUnitPrice) {
				olis.Revenue = testUnitPrice / 2;
				countToChange--;
				schedulesToUpdate.add(olis);
			}
		}
		System.debug('Updating Schedules:  ' + schedulesToUpdate);
		update schedulesToUpdate;

		System.debug('testOppLines:  ' + [
				SELECT Id,
						UnitPrice
				FROM OpportunityLineItem
				WHERE Id IN :testOppLines
		]);

		update testOppLines;

		Test.startTest();
		DataProviderUtility.GroupedDataResponse response = DataProviderUtility.getGroupedData('Opp_Lines_by_Arrival', testOppId);
		List<Map<String, Object>> results = response.aggregateData;
		Test.stopTest();

		System.debug('Got results:  ' + JSON.serializePretty(results));

		System.assertEquals(3, results.size(), 'Should be 3 total lines due to different start date times and unit prices.');

		System.debug('response.orderedOutputFields:  ' + JSON.serializePretty(response.orderedOutputFields));
	}

	@IsTest
	static void testDataProviderNewWay_SObjects () {
		List<OpportunityLineItem> testOppLines = [
				SELECT Id,
						OpportunityId
				FROM OpportunityLineItem
				LIMIT :testBatchSize / 2
		];

		Id testOppId;
		Datetime testDatetime = Datetime.now();
		for (OpportunityLineItem oli : testOppLines) {
			oli.tixtrack__TicketureEventSessionDateTime__c = testDatetime;
			testOppId = oli.OpportunityId;
		}

		Integer countToChange = Integer.valueOf(testBatchSize / 2 / 2);
		List<OpportunityLineItemSchedule> schedulesToUpdate = new List<OpportunityLineItemSchedule>();
		for (OpportunityLineItemSchedule olis : [
				SELECT Id,
						Revenue
				FROM OpportunityLineItemSchedule
				WHERE OpportunityLineItemId IN :testOppLines
				ORDER BY OpportunityLineItemId
		]) {
			System.debug('olis.Revenue:  ' + olis.Revenue);
			System.debug('testUnitPrice:  ' + testUnitPrice);
			System.debug('countToChange:  ' + countToChange);
			if (countToChange > 0 && olis.Revenue == testUnitPrice) {
				olis.Revenue = testUnitPrice / 2;
				countToChange--;
				schedulesToUpdate.add(olis);
			}
		}
		System.debug('Updating Schedules:  ' + schedulesToUpdate);
		update schedulesToUpdate;

		System.debug('testOppLines:  ' + [
				SELECT Id,
						UnitPrice
				FROM OpportunityLineItem
				WHERE Id IN :testOppLines
		]);

		update testOppLines;

		Test.startTest();
		List<SObject> response = DataProviderUtility.getGroupedDataAsSobjects('Opp_Lines_by_Arrival', testOppId);
		Test.stopTest();

		System.debug('Got results:  ' + JSON.serializePretty(response));

		System.assertEquals(2, response.size());//, 'Should be 3 total lines due to different start date times and unit prices.');

		for (OpportunityLineItem oli : (List<OpportunityLineItem>) response) {
			// let's just make sure the parents are built properly, then call it good,
			System.assertEquals('test product', oli.Product2.Name);
			System.assertEquals('1234', oli.Product2.ProductCode);
			// the group by fields all have different values, so we check for null
			System.assertNotEquals(null, oli.Quantity);
			System.assertNotEquals(null, oli.UnitPrice);
			System.assertNotEquals(null, oli.TotalPrice);
		}
	}
}