/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * MembershipController
 * @description: 
 * @author: <PERSON><PERSON>
 * @date: 1/17/25
 */

global inherited sharing class MembershipController {

    /**
     * Retrieves data from the Ticketure API using a specified path and query parameters.
     *
     * @param path The API endpoint path to retrieve data from.
     * @param args A map of query parameters to include in the request.
     * @return A JSON-formatted string containing the API response.
     */
    @AuraEnabled
    public static String ticketureGet(String path, Map<String, Object> args) {
        tixtrack.TixSyncAPI api = tixtrack.TixSyncAPI.setupAPI(true);
        return api.getPath(path, args);
    }

    /**
     * Retrieves the latest Ticketure settings.
     *
     * @return An instance of TixSettings containing configuration details.
     * @throws AuraHandledException If an error occurs while fetching the settings.
     */
    @AuraEnabled(Cacheable=false)
    public static tixtrack.TixSettings getTheSettings() {
        try {
            return tixtrack.TixSettings.getFreshSettings();
        } catch (Exception e) {
            System.debug(e);
            System.debug(e.getStackTraceString());
            throw new AuraHandledException(e.getMessage());
        }
    }

    /**
     * Processes membership input data and creates related records in Salesforce.
     * This method handles inserting Opportunities, Opportunity Line Items, Payments, and Gift Transactions.
     *
     * @param inputs A list of MembershipInputContainer objects containing membership details in JSON format.
     * @return The same list of MembershipInputContainer objects.
     */
    @InvocableMethod(Label='Process Membership' Description='Processes a MembershipInputContainer to create memberships in Ticketure')
    global static List<MembershipInputContainer> processMembership(List<MembershipInputContainer> inputs) {
        UnitOfWork uow = new UnitOfWork(DB.init(false, false, false));

        Set<String> eventTemplateIds = new Set<String>();
        Set<Id> contactIds = new Set<Id>();
        Map<String, Id> eventTemplateToGiftDesignationId = new Map<String, Id>();

        for (MembershipInputContainer input : inputs) {
            if(String.isBlank(input.jsonData)) {
                continue;
            }
            MembershipInfo membershipInfo = (MembershipInfo) JSON.deserialize(input.jsonData, MembershipInfo.class);
            for (Ticket ticket : membershipInfo.tickets) {
                eventTemplateIds.add(ticket.event_template_id);

                if(String.isNotBlank(ticket.contactId)) {
                    contactIds.add(ticket.contactId);
                }
            }
        }

        Map<Id, Contact> contactMap = new Map<Id, Contact>([SELECT Id, Name, FirstName, LastName FROM Contact WHERE Id IN : contactIds]);

        for(Product2 product: [SELECT Id, tixtrack__TicketureEventTemplateId__c, tixtrack__GiftDesignation__c FROM Product2 WHERE tixtrack__TicketureEventTemplateId__c IN: eventTemplateIds]) {
            eventTemplateToGiftDesignationId.put(product.tixtrack__TicketureEventTemplateId__c, product.tixtrack__GiftDesignation__c);
        }

        for (MembershipInputContainer input : inputs) {
            if(String.isBlank(input.jsonData)) {
                continue;
            }

            MembershipInfo membershipInfo = (MembershipInfo) JSON.deserialize(input.jsonData, MembershipInfo.class);

            Opportunity opp = membershipInfo.toOpportunityRecord();
            UnitOfWork.RecordTx oppRTX = uow.addInsert(opp);

            for(OpportunityLineItem oli : membershipInfo.toOpportunityLineItemRecords(eventTemplateToGiftDesignationId, contactMap)) {
                oppRTX.addChildRecord(oli, OpportunityLineItem.OpportunityId);
                uow.addInsert(oli);
            }

            tixtrack__TixTrackPayment__c payment = membershipInfo.toPayment();
            oppRTX.addChildRecord(payment, tixtrack__TixTrackPayment__c.tixtrack__Opportunity__c);
            UnitOfWork.RecordTx paymentRTX = uow.addInsert(payment);

            if(String.isNotBlank(input.giftTransactionId)) {
                GiftTransaction giftTx = membershipInfo.toGiftTransaction(input.giftTransactionId);
                oppRTX.addChildRecord(giftTx, GiftTransaction.tixtrack__TixTrackPurchase__c);
                paymentRTX.addChildRecord(giftTx, GiftTransaction.tixtrack__TixTrackPayment__c);
                uow.addUpdate(giftTx);
            }

        }
        uow.checkpoint();
        uow.commitWork();

        if(inputs.get(0).isDryRun ?? false) {
            uow.rollback();
        }
        return inputs;
    }

    /**
     * Represents input data for processing a membership transaction.
     */
    global class MembershipInputContainer {
        @InvocableVariable(Label='Gift Transaction Id' Description='The Record Id of the GiftTransaction record that should')
        global Id giftTransactionId;

        @InvocableVariable(Label='Membership Info JSON Data' Required=true)
        global String jsonData;

        @InvocableVariable(Label='Is Dry Run' Description='Determines whether or not the transaction will be rolled back upon success.')
        global Boolean isDryRun;

        global MembershipInputContainer() {}
    }

    /**
     * Represents a membership with associated ticket and payment information.
     */
    public class MembershipInfo {
        public List<Ticket> tickets;
        public Id primaryContactId;
        public Id donorId;
        public String sellerId;
        public String eventTemplateId;
        public String memberLevel;
        public Date giftReceivedDate;
        public Datetime validFrom;
        public Datetime validTo;
        public Decimal subtotal;

        public MembershipInfo() {}

        /**
         * Converts this membership information into an Opportunity record.
         *
         * @return An Opportunity object populated with membership data.
         */
        public Opportunity toOpportunityRecord() {
            tixtrack.TixSettings theSettings = tixtrack.TixSettings.getSettings();

            Opportunity opp = new Opportunity();
            opp.AccountId = this.donorId;
            opp.RecordTypeId = theSettings.ticketAuditMembershipRTId;
            opp.Name = 'Ticketure Cart ' + System.now().getTime();
            opp.StageName = 'Closed Won';
            opp.CloseDate = this.giftReceivedDate != null ? this.giftReceivedDate : Date.today();
            opp.tixtrack__TicketureSellerId__c = this.sellerId;
            opp.tixtrack__TicketureSeller__c = this.sellerId;
            opp.tixtrack__TixTrackPrimaryContact__c = this.primaryContactId;

            if (String.isNotBlank(this.memberLevel)) {
                opp.tixtrack__TixTrackMemberLevel__c = this.memberLevel;
                opp.tixtrack__TixTrackMembershipStartDate__c = this.validFrom != null ? this.validFrom.date() : null;
                opp.tixtrack__TixTrackMembershipEndDate__c = this.validTo != null ? this.validTo.date() : null;
            }
            opp.tixtrack__PushToTicketure__c = true;

            return opp;
        }

        /**
         * Converts this membership into a payment record.
         *
         * @return A TixTrackPayment object with the payment details.
         */
        public tixtrack__TixTrackPayment__c toPayment() {
            tixtrack__TixTrackPayment__c payment = new tixtrack__TixTrackPayment__c(
                tixtrack__Paid__c = true,
                tixtrack__PaymentDate__c = this.giftReceivedDate != null ? this.giftReceivedDate : null,
                tixtrack__System__c = 'Ticketure',
                tixtrack__PaymentAmount__c = this.subtotal
            );
            return payment;
        }

        /**
         * Links this membership to a Gift Transaction record.
         *
         * @param giftTransactionId The ID of the associated Gift Transaction.
         * @return A GiftTransaction object.
         */
        public GiftTransaction toGiftTransaction(Id giftTransactionId) {
            GiftTransaction payment = new GiftTransaction(
                Id = giftTransactionId
            );
            return payment;
        }

        /**
         * Converts ticket information into a list of OpportunityLineItem records.
         *
         * @param eventTemplateToGiftDesignationId A map linking event templates to gift designations.
         * @return A list of OpportunityLineItem objects.
         */
        public List<OpportunityLineItem> toOpportunityLineItemRecords(Map<String, Id> eventTemplateToGiftDesignationId, Map<Id, Contact> contactMap) {
            List<OpportunityLineItem> opportunityLineItems = new List<OpportunityLineItem>();

            for (Ticket ticket : this.tickets) {
                Contact admitContact = String.isNotBlank(ticket.contactId) ? contactMap.get(ticket.contactId) : null;
                opportunityLineItems.add(ticket.toOpportunityLineItemRecord(eventTemplateToGiftDesignationId.get(ticket.event_template_id), admitContact));
            }
            return opportunityLineItems;
        }
    }

    /**
     * Represents ticket information used in membership processing.
     */
    public class Ticket {
        public String groupName;
        public String typeName;
        public String typeId;
        public String contactId;
        public String contactName;
        public String contactEmail;
        public Datetime validFrom;
        public Datetime validTo;
        public Decimal price;
        public String event_template_id;
        public String scan_code;
        public String eventName;
        public String ticket_group_id;
        public String ticket_type_id;
        public String admit_name;

        public Ticket() {}

        /**
         * Converts a Ticket object into an OpportunityLineItem record.
         *
         * @param giftDesignationId The gift designation ID associated with the ticket.
         * @return The corresponding OpportunityLineItem record.
         */
        public OpportunityLineItem toOpportunityLineItemRecord(Id giftDesignationId, Contact admitContact) {

            OpportunityLineItem oli = new OpportunityLineItem();
            oli.PricebookEntry = new PricebookEntry(tixtrack__TicketureEventTemplateId__c = this.event_template_id);
            oli.UnitPrice = this.price;
            oli.Quantity = 1;
            oli.tixtrack__TicketureTicketTypeId__c = this.ticket_type_id;
            oli.tixtrack__TicketureTicketType__c = this.typeName;
            oli.tixtrack__TicketureValidFrom__c = this.validFrom != null ? this.validFrom : null;
            oli.tixtrack__TicketureValidTo__c = this.validTo != null ? this.validTo : null;
            oli.tixtrack__GiftDesignation__c = giftDesignationId;
            oli.tixtrack__TicketureAdmitName__c = admitContact != null ? admitContact.FirstName + '\u00A0' + admitContact.LastName : this.contactName;
            oli.tixtrack__TicketureAdmitEmail__c = this.contactEmail;
            oli.tixtrack__TicketureAdmitContact__c = this.contactId;
            return oli;
        }
    }
}