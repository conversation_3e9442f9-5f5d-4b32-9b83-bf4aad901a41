/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * membershipEntry.css
 * @description: 
 * @author: <PERSON><PERSON>
 * @date: 1/17/25
 */

.div-37 {
    color: #939393;
}
.div-56 {
    text-align: right;
}
.div-52 {
    text-align: right;
    padding: var(--lwc-spacingSmall);
    display: block;
    font-weight: 700;
}
table.cart-total tr td {
    vertical-align: bottom;
}
table.cart-total tr td:first-child {
    text-align: left;
    padding-right: 10px;
}
table.cart-total tr td:last-child {
    text-align: right;
}
.div-63 {
    text-align: center;
}
.mismatchWarning {
    display: block;
    background-color: #feb8ab;
    margin: var(--lwc-spacingXxSmall);
    padding: var(--lwc-spacingLarge);
    border-radius: 4px;
    border-color: #ea001e;
    border-width: 1px;
    border-style: solid;
}
.div-4 {
    font-size: 1.2em;
    text-transform: regular;
}
.card<PERSON><PERSON><PERSON>, textarea, input[type=number], input[type=password], input[type=text] {
    border: 1px solid #c9c9c9!important;
    border-radius: 3pt;
    background-color: #fff!important;
    box-shadow: none;
    padding: 7px 10px 6px;
    color: #282b38;
    font-size: 14px;
    margin-right: 20px;
}
.nochargePayment {
    margin-top: 10px;
    font-weight: bold;
}
.table-title {
    border-radius: 4px 4px 0 0;
    border: solid 1px #CCC;
    border-bottom: none;
    padding: var(--lwc-spacingSmall);
}
.grid-border {
    border: solid 1px #CCC;
    border-radius: 0 0 4px 4px;
    background-color: var(--lwc-colorBackground,rgb(243, 243, 243));
}
.layout-with-table-search-left {
    width: 200px;
}
.layout-with-table-search-search {
    width: 250px;
}
.layout-11 {
    background-color: var(--lwc-colorBackground,rgb(243, 243, 243));
    border-radius: 4px 4px 4px 4px;
    display: block;
    padding: var(--lwc-spacingSmall);
}

.cart-timer {
    color: #999;
}
table.payments {
    border-bottom: 1px solid #CCC;
    background-color: var(--lwc-colorBackground,rgb(243, 243, 243));
}
table.payments thead {
    font-weight:600;
}
table.payments td {
    padding: 5pt;
}
table.payments thead tr td {
    border-right: 1px solid #c9c9c9;
    border-top: 0;/*
    background-color: #f3f3f3;*/
}
table.payments thead tr td:first-child {
    border-right: 0;
}
table.payments thead tr td:last-child {
    border-right: 0;
}
table.payments tbody tr {
    background-color: #FFF;
    border-top: 1px solid var(--lwc-colorBorder,rgb(229, 229, 229));
}
table.payments tbody tr:hover {
    background-color: var(--lwc-colorBackground,rgb(243, 243, 243));/*var(--lwc-colorBorder,rgb(229, 229, 229));*/
    border-top: 1px solid #c9c9c9;
    border-bottom: 1px solid #c9c9c9;
}
table.payments tbody tr:last-child:hover {
    border-bottom: 0;
}
table.payments tbody tr td {
}
table.payments tfoot tr td {
    border-top: 1px solid var(--lwc-colorBackground,rgb(243, 243, 243));
    font-weight: 700;
}
.holdCart {
    padding: 10px;
}
.padding-right {
    padding-right: 10px;
}
.input-no-border {
    --slds-c-input-color-border: rgba(50, 50, 93, 0.1);
}
.table-row-align-top {
    vertical-align: top;
}
