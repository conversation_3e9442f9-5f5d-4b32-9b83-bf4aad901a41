/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * membershipEntry
 * @description:
 * @author:
 * Original Build: <PERSON>r
 * Adaptation for A&C Package: <PERSON><PERSON>
 * @date: 1/9/25
 */

import {LightningElement, api, track, wire} from 'lwc';
import TicketureModalMember from "tixtrack/ticketureModalMember";
import { reduceErrors } from 'c/utils';

import {FlowAttributeChangeEvent} from "lightning/flowSupport";
import {getObjectInfo, getPicklistValues} from 'lightning/uiObjectInfoApi';
import {getRecord, getFieldValue} from "lightning/uiRecordApi";

import OPPORTUNITY_OBJECT from '@salesforce/schema/Opportunity';
import TICKETURE_SELLER_FIELD from '@salesforce/schema/Opportunity.tixtrack__TicketureSeller__c';
import GIFT_ENTRY_MEMBER_INFO from '@salesforce/schema/GiftEntry.TicketureMemberInfo__c';
import GIFT_ENTRY_GIFT_RECEIVED_DATE from '@salesforce/schema/GiftEntry.GiftReceivedDate';
import GIFT_ENTRY_DONOR from '@salesforce/schema/GiftEntry.DonorId';

import ACCOUNT_TICKETURE_IDENTITY from '@salesforce/schema/Account.tixtrack__TicketureIdentityId__c';
import ACCOUNT_NAME from '@salesforce/schema/Account.Name';
import ACCOUNT_ID from '@salesforce/schema/Account.Id';
import ACCOUNT_PERSON_CNTCT_ID from '@salesforce/schema/Account.PersonContactId';
import ACCOUNT_PERSON_CNTCT_NAME from '@salesforce/schema/Account.PersonContact.Name';
import ACCOUNT_PERSON_CNTCT_EMAIL from '@salesforce/schema/Account.PersonEmail';

import ACCOUNT_PRIMARY_CONTACT_ID from '@salesforce/schema/Account.PrimaryContact__c';
import ACCOUNT_PRIMARY_CONTACT_NAME from '@salesforce/schema/Account.PrimaryContact__r.Name';
import ACCOUNT_PRIMARY_CONTACT_EMAIL from '@salesforce/schema/Account.PrimaryContact__r.Email';

import ticketureGet from "@salesforce/apex/MembershipController.ticketureGet";
import getTheSettings from "@salesforce/apex/MembershipController.getTheSettings";
import LightningConfirm from "lightning/confirm";
import LightningAlert from "lightning/alert";

const tableConfig = {
    fixedHeight: 200
};

const tixapi_get_events_path = "staff/events/available";
const tixapi_get_events = {
    path: tixapi_get_events_path,
    args: {
        _embed: "ticket_group,ticket_type,seller"
    }
};

const actions = [
    {label: 'Delete', name: 'delete'}
];

const ticketColumns = [
    {label: 'Event', fieldName: 'eventName', hideDefaultActions: true},
    {label: 'Group', fieldName: 'groupName', hideDefaultActions: true},
    {label: 'Type', fieldName: 'typeName', hideDefaultActions: true},
    {
        label: 'Session/Membership Start', fieldName: 'start', type: 'date', hideDefaultActions: true, typeAttributes: {
            year: "numeric",
            month: "numeric",
            day: "numeric",
            hour: "numeric",
            minute: "2-digit",
            timeZoneName: "short"
        }
    },
    {label: 'Quantity', fieldName: 'quantity', hideDefaultActions: true},
    {label: 'Amount', fieldName: 'total', type: 'currency', hideDefaultActions: true},
    {
        label: 'Action',
        type: 'action',
        typeAttributes: {
            rowActions: actions
        }
    }
];

export default class MembershipEntry extends LightningElement {

    theSettings;
    opportunityRecord = {};
    settings = {};
    error;

    eventTemplate;
    modalType = TicketureModalMember;
    localContactIdMap = {};

    hasRenderedCallback = false;
    loading = true;

    ticketColumns = ticketColumns;
    tableConfig = tableConfig;

    event_templates = [];
    ticket_groups = [];
    ticket_types = [];

    @track allEventSearchResults = []; // For the selected seller
    @track sellerPicklistOptions = [];

    @track
    _membershipInfo = {
        tickets: [], // ALL tickets for the current cart
        grpdtickets: [], // Current cart's tickets grouped by ticket type for the "Tickets" datagrid

        sellerId: null,
        memberLevel: null,
        validFrom: null,
        validTo: null,
    };

    _record;
    _donorId;
    _eventTemplateId;
    _giftReceivedDate;

    @api
    get donorId() {
        return this._donorId;
    }

    set donorId(value) {
        this._donorId = value;
        console.log('set donorId: ' + value);
    }

    @api
    get eventTemplateId() {
        return this._eventTemplateId;
    }

    set eventTemplateId(value) {
        this._eventTemplateId = value;
        this.eventTemplate = {id: value};
    }

    @api
    get giftReceivedDate() {
        return this._giftReceivedDate;
    }
    set giftReceivedDate(value) {

        if(value) {
            this._giftReceivedDate = value;
            this.opportunityRecord.memberStart = value;

            const startDate = new Date(value);
            const endDate = new Date(startDate.getFullYear(), startDate.getMonth() + 13, 0);

            endDate.setMinutes(endDate.getMinutes() - endDate.getTimezoneOffset());
            this.opportunityRecord.memberEnd = endDate.toISOString().split('T')[0];
        }
    }

    @api
    get record() {
        return this._record;
    }

    set record(value) {
        if (value) {
            this._record = value;

            if (this._record[GIFT_ENTRY_MEMBER_INFO.fieldApiName]) {
                console.log('set record -> set membershipInfo: ' + JSON.stringify(this._record[GIFT_ENTRY_MEMBER_INFO.fieldApiName]));
                this._membershipInfo = JSON.parse(this._record[GIFT_ENTRY_MEMBER_INFO.fieldApiName]);
            }

            if(this._record[GIFT_ENTRY_GIFT_RECEIVED_DATE.fieldApiName]) {
                this.opportunityRecord.memberStart = this._record[GIFT_ENTRY_GIFT_RECEIVED_DATE.fieldApiName];
            }

            if (this._record[GIFT_ENTRY_DONOR.fieldApiName]) {
                this._donorId = this._record[GIFT_ENTRY_DONOR.fieldApiName];
            }
        }
    }


    get customerSelectionTicketureId() {
        if (this.account) {
            return getFieldValue(this.account?.data, ACCOUNT_TICKETURE_IDENTITY);
        }
    }

    get customerSelectionAccountName() {
        if (this.account) {
            return getFieldValue(this.account?.data, ACCOUNT_NAME);
        }
    }

    get customerSelectionAccountId() {
        if (this.account) {
            return getFieldValue(this.account?.data, ACCOUNT_ID);
        }
    }

    get customerSelectionPrimaryContactId() {
        if (this.account) {
            return getFieldValue(this.account?.data, ACCOUNT_PRIMARY_CONTACT_ID);
        }
    }

    get customerSelectionPrimaryContactName() {
        if (this.account) {
            return getFieldValue(this.account?.data, ACCOUNT_PRIMARY_CONTACT_NAME);
        }
    }

    get customerSelectionPrimaryContactEmail() {
        if (this.account) {
            return getFieldValue(this.account?.data, ACCOUNT_PRIMARY_CONTACT_EMAIL);
        }
    }

    get customerSelectionPersonContactId() {
        if (this.account) {
            return getFieldValue(this.account?.data, ACCOUNT_PERSON_CNTCT_ID);
        }
    }

    get customerSelectionPersonContactName() {
        if (this.account) {
            return getFieldValue(this.account?.data, ACCOUNT_PERSON_CNTCT_NAME);
        }
    }

    get customerSelectionPersonContactEmail() {
        if (this.account) {
            return getFieldValue(this.account?.data, ACCOUNT_PERSON_CNTCT_EMAIL);
        }
    }

    get primaryContactId() {
        return this.customerSelectionPersonContactId? this.customerSelectionPersonContactId : this.customerSelectionPrimaryContactId;
    }

    get primaryContactName() {
        return this.customerSelectionPersonContactId? this.customerSelectionPersonContactName : this.customerSelectionPrimaryContactName;
    }

    get primaryContactEmail() {
        return this.customerSelectionPersonContactId? this.customerSelectionPersonContactEmail : this.customerSelectionPrimaryContactEmail;
    }

    @wire(getRecord, {
        recordId: '$_donorId',
        fields: [
            ACCOUNT_TICKETURE_IDENTITY,
            ACCOUNT_NAME,
            ACCOUNT_ID,
            ACCOUNT_PRIMARY_CONTACT_ID,
            ACCOUNT_PRIMARY_CONTACT_NAME,
            ACCOUNT_PRIMARY_CONTACT_EMAIL,
            ACCOUNT_PERSON_CNTCT_ID,
            ACCOUNT_PERSON_CNTCT_NAME,
            ACCOUNT_PERSON_CNTCT_EMAIL
        ],
        optionalFields: [],
    })
    account;

    get customerSelection() {
        if (this.primaryContactId) {
            return [
                {
                    id: this.primaryContactId,
                    sObjectType: 'Contact',
                    icon: 'standard:contact',
                    title: this.primaryContactName,
                    subtitle: '',
                    record: {
                        id: this.primaryContactId,
                        Name: this.primaryContactName,
                        Email: this.primaryContactEmail,
                        TicketureIdentityId__c: this.customerSelectionTicketureId
                    }
                }
            ]
        }
        return [];
    };

    @api
    get membershipInfo() {
        return this._membershipInfo;
    }
    set membershipInfo(value) {
        if (value) {
            if (typeof value === 'string') {
                try {
                    this._membershipInfo = JSON.parse(value);
                } catch (error) {
                    console.error('Invalid JSON string provided:', error);
                    this._membershipInfo = {}; // Fallback to an empty object or another default value
                }
            } else if (typeof value === 'object' && value !== null) {
                this._membershipInfo = { ...value }; // Shallow copy of the object
            } else {
                console.warn('Unsupported data type for membershipInfo:', typeof value);
                this._membershipInfo = {}; // Fallback to an empty object or another default value
            }
        } else {
            this._membershipInfo = null; // Reset or handle null/undefined as needed
        }
    }

    @api
    get membershipInfoJSON() {
        return JSON.stringify({
            donorId: this._donorId,
            primaryContactId: this.primaryContactId,
            giftReceivedDate: this.giftReceivedDate,
            eventTemplateId: this._eventTemplateId,
            subtotal: this.subTotal,
            ...this._membershipInfo
        });
    }
    set membershipInfoJSON(value) {
        if(value) {
            this.membershipInfo = JSON.parse(value);
        }
    }

    get defaultRecordTypeId() {
        return this.opportunityMetadata.data?.defaultRecordTypeId;
    }

    get showSelectMembership() {
        return this._membershipInfo.sellerId;
    }

    get subTotal() {
        return this._membershipInfo.tickets.reduce((total, el) => total + Number(el.price), 0);
    }

    renderedCallback() {
        if (!this.hasRenderedCallback) {
            this.hasRenderedCallback = true;

            this.getEventTemplates()
            .then(() => getTheSettings({}))
            .then(result => {
                this.theSettings = result;

                if (this.theSettings.defaultSellerId) {
                    return this.setSeller(this.theSettings.defaultSellerId);
                }
            }).catch(
                err => {
                    this.error = err;
                    LightningAlert.open({
                        message: reduceErrors(err),
                        theme: 'error', // a red theme intended for error states
                        label: 'Error!', // this is the header text
                    });
                }
            ).finally(()=>{
                this.loading = false;
            })
        }
    }

    async getEventTemplates() {
        await ticketureGet(
            tixapi_get_events
        ).then(
            result => {
                console.log("ticketureGet:events/available: " + result);
                let response = JSON.parse(result);

                this.event_templates = response.event_template.x_data;
                this.ticket_groups = response.ticket_group.x_data;
                this.ticket_types = response.ticket_type.x_data;

                // Add ticket groups to each template, then ticket types to each group
                this.event_templates.forEach(tmplt => {
                    tmplt.ticket_groups = [];
                    this.ticket_groups.filter(el => el.event_template_id === tmplt.id).forEach(grp => {
                        tmplt.ticket_groups.push(grp);

                        grp.ticket_types = [];
                        this.ticket_types.filter(el2 => el2.ticket_group_id === grp.id).forEach(ttype => {
                            grp.ticket_types.push(ttype);
                        });
                    });
                });
            }
        ).catch(
            err => {
                this.error = err;
                LightningAlert.open({
                    message: reduceErrors(err),
                    theme: 'error', // a red theme intended for error states
                    label: 'Error!', // this is the header text
                });
            }
        );
    }

    @wire(getObjectInfo, {objectApiName: OPPORTUNITY_OBJECT})
    opportunityMetadata;

    @wire(getPicklistValues, {
        recordTypeId: '$defaultRecordTypeId',
        fieldApiName: TICKETURE_SELLER_FIELD
    })
    wiredPicklistValues({error, data}) {
        if (data) {
            this.sellerPicklistOptions = data.values.map(item => ({
                label: item.label,
                value: item.value
            }));
        } else if (error) {
            console.error('Error fetching picklist values:', error);
        }
    }

    handleOpenSelectMembership() {
        this.modalType.open(
            {
                size: 'large',
                label: 'Select Ticket Type(s)',
                description: 'Select Ticket Type(s)',
                sellerId: this._membershipInfo.sellerId,
                eventTemplate: this.eventTemplate,
                opportunityRecord: this.opportunityRecord,
                primaryContact: this.customerSelection[0],
                settings: this.settings
            }
        ).then(
            (modalResult) => {
                /*
                 modalResult should mirror the TicketureAPI.ReserveRequest object.
                 */
                console.log("ticketureCreateCart:openModal:modalResult");
                console.log(JSON.stringify(modalResult));
                if (modalResult) {

                    console.log(this._membershipInfo);

                    const postData = {
                        tickets: []
                    };

                    if (modalResult._memberLevel) {
                        this._membershipInfo.memberLevel = modalResult._memberLevel;
                    }
                    if (modalResult._validFrom) {
                        this._membershipInfo.validFrom = modalResult._validFrom;
                    }
                    if (modalResult._validTo) {
                        this._membershipInfo.validTo = modalResult._validTo;
                    }
                    console.log("validFrom: ", this._membershipInfo.validFrom);
                    console.log("validTo: ", this._membershipInfo.validTo);

                    modalResult.tickets.forEach((el) => {
                        postData.tickets.push(el.toTicketureTicket(this.settings?.ticketureRandomEmailPattern));
                        if (el.contactId) {
                            this.localContactIdMap[el.contactId.toLowerCase()] = {
                                contactId: el.contactId,
                                contactName: el.contactName,
                                contactEmail: el.contactEmail,
                                identity_id: el.identity_id
                            };
                        }
                    });

                    if (modalResult.additional_info) {
                        postData.additional_info = [...modalResult.additional_info];
                    }

                    this.applyCartResponse(modalResult, modalResult);
                }
            }
        ).catch(
            err => {
                this.error = err;
                LightningAlert.open({
                    message: reduceErrors(err),
                    theme: 'error', // a red theme intended for error states
                    label: 'Error!', // this is the header text
                });
            }
        );
    }

    handleRowAction(event) {
        console.log('handleRowAction: ', JSON.stringify(event));
        const actionName = event.detail.action.name;
        const row = event.detail.row;

        switch (actionName) {
            case 'delete':
                this.deleteRow(row);
                break;
        }
    }

    async handleSellerChange(e) {
        console.log("handleSellerChange");

        const priorSellerId = this._membershipInfo.sellerId;
        const newSellerId = e.detail.value;


        if (this._membershipInfo.tickets != null && this._membershipInfo?.tickets?.length > 0) {
            const result = await LightningConfirm.open({
                message: 'Changing the seller will discard the current cart. Are you sure?',
                variant: 'header',
                label: 'Are you sure?',
                theme: 'warning'
            });

            if (result) {
                await this.setSeller(newSellerId);
            } else {
                // Set the seller selection back
                const sellerComboRef = this.refs.sellerCombo;
                if (sellerComboRef) {
                    sellerComboRef.value = priorSellerId;
                }
                this._membershipInfo.sellerId = priorSellerId;
            }
        } else {
            await this.setSeller(newSellerId);
        }

        this.dispatchEvent(new FlowAttributeChangeEvent('membershipInfoJSON', JSON.stringify(this.membershipInfoJSON)));

    }

    async setSeller(sellerId) {
        console.log('setSeller: ', sellerId);

        this._membershipInfo.sellerId = sellerId;

        console.log(JSON.stringify(this.membershipInfo));

        // Reset the event template display search results
        this.allEventSearchResults = [];
        for (const tmplt1 of this.event_templates.filter(tmplt => tmplt.seller_id === this._membershipInfo.sellerId)) {
            this.allEventSearchResults.push({
                id: tmplt1.id,
                sObjectType: 'Product',
                icon: 'standard:product',
                title: tmplt1.name,
                subtitleFormatted: tmplt1.description,
                event_template: tmplt1
            });
        }
        console.log(JSON.stringify(this.allEventSearchResults));
    }

    deleteRow(row) {
        console.log('deleteRow: ', JSON.stringify(row));
        const {id} = row;

        // Delete from grpdtickets
        const grpIndex = this.findRowIndexById(this._membershipInfo.grpdtickets, id);
        if (grpIndex !== -1) {
            this._membershipInfo.grpdtickets = this._membershipInfo.grpdtickets
                .slice(0, grpIndex)
                .concat(this._membershipInfo.grpdtickets.slice(grpIndex + 1));
        }

        // Delete from tickets
        const ticketsIndex = this.findRowIndexById(this._membershipInfo.tickets, id);
        if (ticketsIndex !== -1) {
            this._membershipInfo.tickets = this._membershipInfo.tickets
                .slice(0, ticketsIndex)
                .concat(this._membershipInfo.tickets.slice(ticketsIndex + 1));
        }

        this.dispatchEvent(new FlowAttributeChangeEvent('membershipInfoJSON', JSON.stringify(this.membershipInfoJSON)));

    }

    findRowIndexById(array, id) {
        let ret = -1;
        array.some((row, index) => {
            if (row.id === id) {
                ret = index;
                return true;
            }
            return false;
        });
        return ret;
    }

    applyCartResponse(cart, ticketModalResult) {
        console.log("applyCartResponse");
        console.log('ticketModalResult: ', JSON.stringify(ticketModalResult));
        console.log('processing returned tickets');
        const groupedTickets = new Map();
        cart.tickets.forEach(el => {

            let event = this.event_templates.find(e =>
                (el.event_template_id && e.id === el.event_template_id) ||
                (el.eventName && e.name === el.eventName)
            );

            let group = this.ticket_groups.find(e =>
                (el.ticket_group_id && e.id === el.ticket_group_id) ||
                (el.groupName && e.name === el.groupName)
            );

            let type = this.ticket_types.find(e =>
                (el.ticket_type_id && e.id === el.ticket_type_id) ||
                (el.typeName && e.name === el.typeName)
            );

            console.log('event: ', event);
            console.log('group: ', group);
            console.log('type: ', type);

            if (event) {
                el.eventName = event.name;
                if (!el.event_template_id) {
                    el.event_template_id = event.id;
                }
            }

            if (group) {
                el.groupName = group.name;
                if (!el.ticket_group_id) {
                    el.ticket_group_id = group.id;
                }
            }

            if (type) {
                el.typeName = type.name;
                if (!el.ticket_type_id) {
                    el.ticket_type_id = type.id;
                }
            }

            if (el.validFrom && !el.valid_from) {
                el.valid_from = el.validFrom;
            }

            if (el.validTo && !el.valid_to) {
                el.valid_to = el.validTo;
            }

            // TODO: not sure about this but i assume
            // this is what comes back from the createcart call
            if (el.contactName) {
                el.admit_name = el.contactName;
            }

            // TODO: not sure if we should do this one either. is it adjusted_price or face_value?
            if (el.price) {
                el.face_value = el.price;
            }

            Object.defineProperty(el, 'start', {
                get: function () {
                    return el.valid_from ? el.valid_from : el.start_datetime
                },
                enumerable: true
            });
            Object.defineProperty(el, 'end', {
                get: function () {
                    return el.valid_to ? el.valid_to : el.end_datetime
                },
                enumerable: true
            });

            let key = el.event_template_id + '-' + el.ticket_group_id + '-' + el.ticket_type_id + '-' + el.start;
            if (!groupedTickets.has(key)) {
                groupedTickets.set(key, {
                    id: key,
                    eventName: event.name,
                    groupName: group.name,
                    typeName: type.name,
                    start: el.start,
                    tickets: [],
                    quantity: 0,
                    total: 0
                });
            }

            groupedTickets.get(key).tickets.push(el);
            groupedTickets.get(key).quantity++;
            groupedTickets.get(key).total += Number(el.face_value);
        });
        console.log('groupedTickets: ', JSON.stringify(groupedTickets));
        this._membershipInfo.grpdtickets = [...groupedTickets.values()];

        this._membershipInfo.tickets = [...cart.tickets];
        console.log("applyCartResponse tickets: " + JSON.stringify(this._membershipInfo.tickets));

        this.dispatchEvent(new FlowAttributeChangeEvent('membershipInfoJSON', JSON.stringify(this.membershipInfoJSON)));

    }
}