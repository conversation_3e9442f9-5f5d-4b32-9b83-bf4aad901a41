<?xml version="1.0" encoding="UTF-8"?>
<LightningComponentBundle xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>61.0</apiVersion>
    <description>Membership Entry</description>
    <isExposed>true</isExposed>
    <targets>
        <target>lightning__FlowScreen</target>
    </targets>
    <targetConfigs>
        <targetConfig targets="lightning__FlowScreen">
            <propertyType name="T" extends="SObject" label="Record Object Type" description="Generic sObject data type used for input sObject properties" />
            <property name="record" type="{T}" label="Record" description="Generic sObject data type used for input sObject properties. This will likely be an existing GiftEntry record"/>
            <property name="donorId" label="Donor Id" role="inputOnly"  type="String" description="Override for the Donor that will be related to this GiftEntry"/>
            <property name="giftReceivedDate" label="Gift Received Date" role="inputOnly"  type="Date" description="The date the gift was received coming from the Gift Entry flow"/>
            <property name="eventTemplateId" label="Ticketure Membership Event Template Id" role="inputOnly"  type="String" description="Ticketure Event Template Id for Membership"/>
            <property name="membershipInfoJSON" label="Ticketure Membership Info JSON" type="String" description="Used to map the JSON structure from membership modal into the Ticketure Membership Info field on Gift Entry"/>
        </targetConfig>
    </targetConfigs>
    <masterLabel>Membership Entry</masterLabel>
</LightningComponentBundle>
