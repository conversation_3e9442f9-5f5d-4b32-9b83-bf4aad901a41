/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * MembershipControllerTest
 * @description: 
 * @author: <PERSON><PERSON>
 * @date: 1/30/25
 */

@IsTest
private class MembershipControllerTest {
    @TestSetup
    static void setupTestData() {
        Account pa = AccountTest.createPersonAccount();

        GiftDesignation gd = new GiftDesignation();
        gd.Name = 'Annual Fund';
        gd.Description = 'Annual unrestricted gifts';
        gd.IsActive = true;
        insert gd;

        Product2 testProduct = new Product2();
        testProduct.tixtrack__TicketureEventTemplateId__c = 'd2780fe7-2fea-0258-91c4-291d4d2167c7';
        testProduct.tixtrack__GiftDesignation__c = gd.Id;
        testProduct.Name = 'test';
        insert testProduct;

        PricebookEntry testPBE = new PricebookEntry();
        testPBE.Product2Id = testProduct.Id;
        testPBE.Pricebook2Id = Test.getStandardPricebookId();
        testPBE.UnitPrice = 1;
        testPBE.IsActive = true;
        testPBE.tixtrack__TicketureEventTemplateId__c = 'd2780fe7-2fea-0258-91c4-291d4d2167c7';
        insert testPBE;

        GiftTransaction gt = new GiftTransaction();
        gt.DonorId = pa.Id;
        gt.CheckDate = Date.today();
        gt.OriginalAmount = 5.0;
        gt.TransactionDate = Date.today();
        gt.TransactionDueDate = Date.today();
        gt.Name = 'Test Transaction';
        gt.PaymentMethod = 'Check';
        gt.Status = 'Paid';
        insert gt;

        GiftTransactionDesignation gtd = new GiftTransactionDesignation();
        gtd.GiftTransactionId = gt.Id;
        gtd.GiftDesignationId = gd.Id;
        gtd.Amount = 5.0;
        gtd.Percent = 100;
        insert gtd;
    }

    @IsTest
    static void testGetTheSettings() {
        Test.startTest();
        tixtrack.TixSettings settings = MembershipController.getTheSettings();
        Test.stopTest();

        System.assertNotEquals(null, settings, 'Settings should not be null');
    }

    @IsTest
    static void testProcessMembership() {

        Id membershipRecordTypeId = Opportunity.SObjectType.getDescribe().getRecordTypeInfosByDeveloperName().get('Ticketure_Purchase_w_Membership').getRecordTypeId();
        Id defaultRecordTypeId = Opportunity.SObjectType.getDescribe().getRecordTypeInfosByDeveloperName().get('Ticketure_Purchase').getRecordTypeId();

        tixtrack.TixSettings theSettings = tixtrack.TixSettings.getSettings();
        theSettings.ticketAuditMembershipRTId = membershipRecordTypeId;
        theSettings.ticketAuditDefaultOpportunityRTId = defaultRecordTypeId;

        Account personAccount = [SELECT Id, PersonContactId, PersonEmail, Name, PersonContact.FirstName, PersonContact.LastName FROM Account LIMIT 1];
        Id giftTransactionId = [SELECT Id FROM GiftTransaction LIMIT 1].Id;
        Id giftDesignationId = [SELECT Id FROM GiftDesignation LIMIT 1].Id;

        Datetime validFrom = Datetime.newInstance(2025, 1, 17, 0, 0, 0);
        Datetime validTo = Datetime.newInstance(2026, 1, 17, 0, 0, 0);
        Date giftReceivedDate = Date.newInstance(2025, 1, 17);

        MembershipController.Ticket ticket = new MembershipController.Ticket();
        ticket.event_template_id = 'd2780fe7-2fea-0258-91c4-291d4d2167c7';
        ticket.price = 100.0;
        ticket.ticket_type_id = 'test_ticket_type';
        ticket.typeName = 'test_type';
        ticket.contactId = personAccount.PersonContactId;
        ticket.contactName = personAccount.Name;
        ticket.contactEmail = personAccount.PersonEmail;
        ticket.validFrom = validFrom;
        ticket.validTo = validTo;

        MembershipController.MembershipInfo membershipInfo = new MembershipController.MembershipInfo();
        membershipInfo.tickets = new List<MembershipController.Ticket>{ticket};

        membershipInfo.primaryContactId = personAccount.PersonContactId;
        membershipInfo.donorId = personAccount.Id;
        membershipInfo.sellerId = 'testSellerId';
        membershipInfo.memberLevel = 'Contributor';
        membershipInfo.giftReceivedDate = giftReceivedDate;
        membershipInfo.validFrom = validFrom;
        membershipInfo.validTo = validTo;
        membershipInfo.subtotal = 100.0;

        MembershipController.MembershipInputContainer input = new MembershipController.MembershipInputContainer();
        input.giftTransactionId = giftTransactionId;
        input.jsonData = JSON.serialize(membershipInfo);
        input.isDryRun = false;

        List<MembershipController.MembershipInputContainer> inputs = new List<MembershipController.MembershipInputContainer>{input};

        Test.startTest();
        List<MembershipController.MembershipInputContainer> result = MembershipController.processMembership(inputs);
        Test.stopTest();

        System.assertNotEquals(null, result, 'Processed memberships should not be null');
        System.assertEquals(1, result.size(), 'There should be one processed membership');

        List<Opportunity> opps = [SELECT Id,
                AccountId,
                RecordTypeId,
                Name,
                StageName,
                CloseDate,
                tixtrack__TicketureSellerId__c,
                tixtrack__TicketureSeller__c,
                tixtrack__TixTrackPrimaryContact__c,
                tixtrack__TixTrackMemberLevel__c,
                tixtrack__TixTrackMembershipStartDate__c,
                tixtrack__TixTrackMembershipEndDate__c,
                tixtrack__PushToTicketure__c
        FROM Opportunity];

        System.assertEquals(false, opps.isEmpty());
        System.assertEquals(personAccount.Id, opps[0].AccountId);
        System.assertEquals('Closed Won', opps[0].StageName);
        System.assertEquals(giftReceivedDate, opps[0].CloseDate);
        System.assertEquals(membershipInfo.sellerId, opps[0].tixtrack__TicketureSellerId__c);
        System.assertEquals(membershipInfo.sellerId, opps[0].tixtrack__TicketureSeller__c);
        System.assertEquals(personAccount.PersonContactId, opps[0].tixtrack__TixTrackPrimaryContact__c);
        System.assertEquals(membershipInfo.memberLevel, opps[0].tixtrack__TixTrackMemberLevel__c);
        System.assertEquals(validFrom.date(), opps[0].tixtrack__TixTrackMembershipStartDate__c);
        System.assertEquals(validTo.date(), opps[0].tixtrack__TixTrackMembershipEndDate__c);
        System.assertEquals(true, opps[0].tixtrack__PushToTicketure__c);


        List<tixtrack__TixTrackPayment__c> payments = [
            SELECT Id,
                tixtrack__Paid__c,
                tixtrack__PaymentDate__c,
                tixtrack__System__c,
                tixtrack__PaymentAmount__c
            FROM tixtrack__TixTrackPayment__c
        ];

        System.assertEquals(false, payments.isEmpty());
        System.assertEquals(true, payments[0].tixtrack__Paid__c);
        System.assertEquals(giftReceivedDate, payments[0].tixtrack__PaymentDate__c);
        System.assertEquals('Ticketure', payments[0].tixtrack__System__c);
        System.assertEquals(membershipInfo.subtotal, payments[0].tixtrack__PaymentAmount__c);

        List<OpportunityLineItem> olis = [
                SELECT Id,
                        UnitPrice,
                        Quantity,
                        tixtrack__TicketureTicketTypeId__c,
                        tixtrack__TicketureTicketType__c,
                        tixtrack__TicketureValidFrom__c,
                        tixtrack__TicketureValidTo__c,
                        tixtrack__GiftDesignation__c,
                        tixtrack__TicketureAdmitName__c,
                        tixtrack__TicketureAdmitEmail__c,
                        tixtrack__TicketureAdmitContact__c
                FROM OpportunityLineItem
        ];

        System.assertEquals(false, olis.isEmpty());
        System.assertEquals(1, olis.size());
        System.assertEquals(ticket.price, olis[0].UnitPrice);
        System.assertEquals(1, olis[0].Quantity);
        System.assertEquals(ticket.ticket_type_id, olis[0].tixtrack__TicketureTicketTypeId__c);
        System.assertEquals(ticket.typeName, olis[0].tixtrack__TicketureTicketType__c);
        System.assertEquals(ticket.validFrom, olis[0].tixtrack__TicketureValidFrom__c);
        System.assertEquals(ticket.validTo, olis[0].tixtrack__TicketureValidTo__c);
        System.assertEquals(giftDesignationId, olis[0].tixtrack__GiftDesignation__c);
        System.assertEquals(personAccount.PersonContact.FirstName + '\u00A0' + personAccount.PersonContact.LastName, olis[0].tixtrack__TicketureAdmitName__c);
        System.assertEquals(ticket.contactEmail, olis[0].tixtrack__TicketureAdmitEmail__c);
        System.assertEquals(ticket.contactId, olis[0].tixtrack__TicketureAdmitContact__c);
    }
}