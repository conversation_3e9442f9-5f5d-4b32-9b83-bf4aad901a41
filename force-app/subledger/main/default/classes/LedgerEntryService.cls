/**
 * @date    4/20/23
 * <AUTHOR>
 *
 * Copyright ©2023 Foglight Solutions, Inc. All Rights Reserved.
 *
 */
public inherited sharing class LedgerEntryService {
    public static Boolean isExecuting = false; // This flag is used to prevent Ledgers from being created outside of this service

    @AuraEnabled
    public static Result processRecord(Id recordId) {
        System.debug(recordId);
        return processRecords(new Set<Id>{recordId}, null).get(recordId);
    }

    @AuraEnabled
    public static List<String> getTAccountBalances(Id recordId) {
        List<String> tAccounts = new List<String>();

        for (LedgerBalance lb : getExistingAccountBalances(recordId)) {
            tAccounts.add(lb.toString());
        }

        return tAccounts;
    }

    @AuraEnabled
    public static List<LedgerBalance> getExistingAccountBalances(Id recordId) {
        Map<Id,LedgerBalance> allExistingBalances = new Map<Id,LedgerBalance>(); // ledgerAccount,LedgerBalance

        /*
         todo make the SOQL's WHERE filter dynamic
          The current query only supports Opportunity. Instead, we should find
          the subledger definitions that reference the SObjectType of the recordId
          and dynamically inject field paths to the header object/field specified
          by those definitions.
         */
        for (LedgerEntryLineItem__c leli : (List<LedgerEntryLineItem__c>)DB.init()
                .read('SELECT Id, flmas__Amount__c, flmas__Type__c, flmas__PostDate__c, ' +
                        'flmas__LedgerEntry__r.Id, flmas__LedgerEntry__r.flmas__Status__c, flmas__LedgerEntry__r.flmas__TransactionDate__c, ' +
                        'flmas__LedgerAccount__r.Id, flmas__LedgerAccount__r.Name, flmas__LedgerAccount__r.flmas__GLAccount__r.Id, flmas__LedgerAccount__r.flmas__GLAccount__r.flmas__AccountSubType__c ' +
                        'FROM flmas__LedgerEntryLineItem__c ' +
                        'WHERE flmas__LedgerEntry__r.flmas__Opportunity__c = :recordId ' +
                        'ORDER BY flmas__LedgerAccount__r.Name ASC')
                .setParam('recordId',recordId)
                .go()
        ) {
            LedgerBalance lb = allExistingBalances.get(leli.LedgerAccount__c);
            if (lb == null) {
                lb = new LedgerBalance(leli.LedgerAccount__r);
                allExistingBalances.put(leli.LedgerAccount__c, lb);
            }
            lb.applyLineItem(leli);
        }

        return allExistingBalances.values();
    }

    /**
     * Used by Ledger Approve/Download
     * @param filterz
     * @return A LedgerDownload instance that includes all the Ledger Entry Line Items that meet the filter criteria and a summary of those lines.
     */
    @AuraEnabled
    public static LedgerDownload getLedgerDownload(Map<String,Object> filterz) {
        System.debug(JSON.serialize(filterz));
        LedgerDownloadFilter filters = (LedgerDownloadFilter)JSON.deserialize(JSON.serialize(filterz), LedgerDownloadFilter.class);
        System.debug(JSON.serialize(filters));

        DescribeSObjectResult primaryDSOR   = LedgerEntryLineItem__c.SObjectType.getDescribe();
        List<FieldSetMember> fsms = FieldSetUtils.createService().getFieldSetMembers('flmas__ApproveDownload', primaryDSOR);
        List<LinesEditorController.DatatableColumn> cols = LinesEditorController.getDatatableColumns(fsms);

        Selector sel = new Selector('LedgerEntryLineItem__c', DB.init().setNamespace('flmas'));
        sel.addField('Id');
        for (FieldSetMember fsm : fsms) {
            if (fsm.type === DisplayType.PICKLIST) {
                sel.addField('TOLABEL(' + fsm.fieldPath + ')');
            } else if (fsm.type == DisplayType.REFERENCE) {
                String tmpField;
                if (fsm.fieldPath.endsWith('Id')) {
                    tmpField = fsm.fieldPath.removeEnd('Id');
                } else if (fsm.fieldPath.endsWith('__c')) {
                    tmpField = fsm.fieldPath.removeEnd('__c') + '__r';
                }
                sel.addField(tmpField + '.Name');
            } else {
                sel.addField(fsm.fieldPath);
            }
        }
        sel.addField('LedgerAccount__r.GLAccount__r.Id');
        sel.addField('LedgerAccount__r.GLAccount__r.AccountSubType__c');

        sel.addFilter('LedgerEntry__r.Status__c', Selector.Comparator.IS_IN, filters.ledgerStatuses);
        sel.addFilter('LedgerEntry__r.PostDate__c', Selector.Comparator.GREATER_THAN_EQUAL, filters.startDate);
        sel.addFilter('LedgerEntry__r.PostDate__c', Selector.Comparator.LESS_THAN_EQUAL, filters.endDate);
        sel.addFilter('LedgerEntry__r.Opportunity__r.RecordTypeId', Selector.Comparator.IS_IN, filters.oppyRecordTypes);
        if (filters.accountId != null) {
            sel.addFilter('LedgerEntry__r.Opportunity__r.AccountId', Selector.Comparator.EQUALS, filters.accountId);
        }
        if (filters.opportunityId != null) {
            sel.addFilter('LedgerEntry__r.Opportunity__c', Selector.Comparator.EQUALS, filters.opportunityId);
        }

        sel.addOrderBy('LedgerEntry__r.PostDate__c', true, true);
        sel.addOrderBy('LedgerAccount__r.Name', true, true);
        //sel.addOrderBy('Type__c', true, true);

        //System.debug(sel.toString());
        List<LedgerEntryLineItem__c> lelis = (List<LedgerEntryLineItem__c>)sel.runQuery().values();

        LedgerDownload ld = new LedgerDownload(cols, lelis);

        return ld;
    }

    @AuraEnabled
    public static void setLedgerStatus(List<String> ledgerEntryIds, String statusString) {
        System.debug(ledgerEntryIds);

        LedgerEntryStatus newStatus = LedgerEntryStatus.valueOf(statusString);

        // Get the ledgers and their current status
        List<LedgerEntry__c> les = (List<LedgerEntry__c>)DB.init().setNamespace('flmas').read(
                'SELECT Id, Status__c ' +
                'FROM LedgerEntry__c ' +
                'WHERE Id IN :ledgerEntryIds')
                .setParam('ledgerEntryIds', ledgerEntryIds)
                .go();
        List<LedgerEntry__c> lesToUpdate = new List<LedgerEntry__c>();
        for (LedgerEntry__c le : les) {
            //if (LedgerEntryStatus.valueOf(le.Status__c).ordinal() < newStatus.ordinal()) {
            le.Status__c = newStatus.name();
            lesToUpdate.add(le);
            //}
        }
        List<DB.DMLResult> results = DB.init().edit(lesToUpdate);
        System.debug(results);
    }

    /**
     * Generate ledgers for the specified parentRecordIds
     *
     * This method is the main entry point to creating Ledgers and is used by:
     *  - "Generate Ledgers" quick action
     *  - Platform Events
     *  - Batch jobs
     *
     * It should not be called by triggers. This is a greedy and long process and there's no need to have this data
     * immediately available
     *
     * @param parentRecordIds the record ids of the parent records to process
     * @param subLedgerDefinition the developerName of the subledger definition to process, or `null` for all active defintions.
     *
     * @return a list of LedgerEntryServiceMessage records
     */
    public static Map<Id, Result> processRecords(Set<Id> parentRecordIds, SubledgerDefinition subLedgerDefinition) {
        Map<Id, Result> msgs = new Map<Id, Result>();
        for (Id i : parentRecordIds) {
            msgs.put(i, new Result(i));
        }

        // Don't process records if subledger isn't enabled.
        if (!SLSettings.getInstance().isEnabled) {
            System.debug('Subledger is not enabled.');
            for (Result lesm : msgs.values()) {
                lesm.addNonFatalMessage('Subledger is not enabled.');
            }
            return msgs;
        }

        isExecuting = true;
        System.debug(parentRecordIds);
        Assert.paramRequired(parentRecordIds, 'parentRecordIds');

        // Strip nulls and skip processing if empty
        parentRecordIds.remove(null);
        if (parentRecordIds.size() == 0) {
            throw new LESException('parentRecordIds cannot be empty');
        }

        // Get the Record instances for the specified recordIds
        List<HeaderProcessor> records = getRecords(parentRecordIds, msgs, subLedgerDefinition);

        // Create a UnitOfWork
        UnitOfWork uow  = new UnitOfWork();

        // Process all of the headers, errors will be logged directly to the applicable "Result" instance
        for (HeaderProcessor hp : records) {
            Result lesm = msgs.get(hp.getHeader().Id);
            hp.processRecord(uow, lesm);
        }

        // Commit UoW
        uow
                .addCommitAction(DB.Action.DESTROY, LedgerEntry__c.SObjectType)
                .addCommitAction(DB.Action.CREATEOREDIT, LedgerAccount__c.SObjectType)
                .addCommitAction(DB.Action.CREATE, LedgerEntry__c.SObjectType)
                .addCommitAction(DB.Action.CREATE, LedgerEntryLineItem__c.SObjectType)
                .commitSequence();

        // Find Errors and Log them?
        isExecuting = false;

        return msgs;
    }

    public static Set<Id> qualifyRecords(List<SObject> newRecords, Map<Id,SObject> oldRecords) {
        Set<Id> qualifiedHeaders = new Set<Id>();

        // Don't process records if subledger isn't enabled.
        if (!SLSettings.getInstance().isEnabled) {
            System.debug('Subledger is not enabled.');
            return qualifiedHeaders;
        }

        for(SubledgerDefinition def : SubledgerDefinition.getAllDefinitions()) {
            qualifiedHeaders.addAll(def.qualifyRecords(newRecords, oldRecords));
        }

        // Submit a PE
        if (qualifiedHeaders.size() > 0) {
            List<LedgerEntryProcessor__e> events = new List<LedgerEntryProcessor__e>();
            for (Id rid : qualifiedHeaders) {
                events.add(new LedgerEntryProcessor__e(SourceRecordId__c = rid));
            }
            List<Database.SaveResult> publishResults = EventBus.publish(events);
        }

        return qualifiedHeaders;
    }

    /**
     * Cascade deletes Ledger Entries/Lines related to the header of the specified source records. If any ledger entries
     * are in a non-new state, LedgerEntryDomain will prevent their deletion and an error will thusly bubbled up to the
     * triggered record.
     *
     * This method should always run, even if Subledger is disabled
     *
     * @param deletedSourceRecords the source records that are trying to be deleted
     */
    public static void cascadeDelete(List<SObject> deletedSourceRecords) {
        System.debug('cascadeDelete():Enter');
        /*
        similar to qualify records, this needs to:
        - identify the source SObjectType
        - Find the definitions for the SObjectType
        - Identify the header and the header IDs
        - Find Ledger Entry records linked to the header
        - Delete the records with all-or-none false
        - Process results and add an error to the source record if any of the related ledgers failed to delete.
         */
        SObjectType sot             = deletedSourceRecords.getSObjectType();
        Map<Id,SObject> recordsMap  = new Map<Id,SObject>(deletedSourceRecords);

        // Get the header lookup fields referenced by the definitions
        Set<String> leHeaderLookupFields      = new Set<String>();
        for(SubledgerDefinition def : SubledgerDefinition.getAllDefinitions(new Set<SObjectType>{sot})) {
            leHeaderLookupFields.add(String.valueOf(def.getFieldLedgerEntryHeaderLookup()).toLowerCase());
        }

        if (leHeaderLookupFields.size() > 0) {
            /*
            Build and execute 2 queries because SOQL doesn't support semi-joins with an OR

            Query 1: All LedgerEntries associated directly to a source record
            Query 2: All LedgerEntries that have a LineItem that is associated to a source record

            The queries have to return the exact same structure because their results will be merged later.
             */
            String qry = 'SELECT Id, ' + String.join(new List<String>(leHeaderLookupFields), ', ');
            qry += ', (SELECT Id, flmas__SourceRecordId__c FROM flmas__LineItems__r WHERE flmas__SourceRecordId__c IN :sourceRecords) FROM flmas__LedgerEntry__c WHERE ';
            qry += ' Id IN (' +
                    'SELECT LedgerEntry__c ' +
                    'FROM flmas__LedgerEntryLineItem__c ' +
                    'WHERE flmas__SourceRecordId__c IN :sourceRecords)';
            DB db = DB.init(false, false, false, false);

            Map<Id,LedgerEntry__c> ledgerEntryMap = new Map<Id,LedgerEntry__c>((List<LedgerEntry__c>)db.read(qry).setParam('sourceRecords', recordsMap.keySet()).go());
            System.debug('Ledgers: ' + ledgerEntryMap.size());

            String qry2 = 'SELECT Id, ' + String.join(new List<String>(leHeaderLookupFields), ', ');
            qry2 += ', (SELECT Id, flmas__SourceRecordId__c FROM flmas__LineItems__r WHERE flmas__SourceRecordId__c IN :sourceRecords) FROM flmas__LedgerEntry__c WHERE ';
            qry2 += String.join(new List<String>(leHeaderLookupFields), ' IN :sourceRecords OR ') + ' IN :sourceRecords';

            /*
             Merge the results of the 2 queries so that we can perform a single delete call across the combined set.
             */
            for (LedgerEntry__c le : (List<LedgerEntry__c>)db.read(qry2).setParam('sourceRecords', recordsMap.keySet()).go()) {
                if (ledgerEntryMap.containsKey(le.id)) {
                    for (String sof : leHeaderLookupFields) {
                        le.put(sof, le.get(sof));
                    }
                } else {
                    ledgerEntryMap.put(le.Id, le);
                }
            }

            List<DB.DMLResult> results = db.destroy(ledgerEntryMap.values());
            for (DB.DMLResult result : results) {
                if (!result.isSuccess()) {
                    // If the ledger failed to delete...
                    LedgerEntry__c le = (LedgerEntry__c)result.originalRecord; // The ledger we tried to delete

                    // Add an error to any source records related directly to the ledger (i.e. Query 2)
                    for (String sof : leHeaderLookupFields) {
                        Id headerId = (Id)le.get(sof);
                        if (headerId != null) {
                            SObject source = recordsMap.get(headerId);
                            System.debug('source: ' + source);
                            if (source != null) {
                                System.debug('headerId: ' + headerId);
                                System.debug('le: ' + JSON.serializePretty(le));
                                for (Database.Error de : result.getErrors()) {
                                    source.addError(de.getMessage());
                                }
                            }
                        }
                    }

                    // Add an error to any source records related to Line Items (i.e. Query 1)
                    for (LedgerEntryLineItem__c leli : le.LineItems__r) {
                        for (Database.Error de : result.getErrors()) {
                            recordsMap.get(leli.SourceRecordId__c).addError(de.getMessage());
                        }
                    }
                }
            }

        }
    }

    /**
     * @param recordIds the list of parent record ids to process
     * @param msgs A map of messages to track errors and successes
     * @param definitionName the developerName of the subledger definition to process, or `null` for all active defintions.
     *
     * @return A list of Record instances for the specified recordIDs that match the criteria for 1 or more SubledgerDefinitions
     */
    private static List<HeaderProcessor> getRecords(Set<Id> recordIds, Map<Id, Result> msgs, SubledgerDefinition definitionName) {
        List<HeaderProcessor> records = new List<HeaderProcessor>();

        // Get all the object types for the recordIds
        Set<SObjectType> objTypes = new Set<SObjectType>();
        for (Id rId : recordIds) {
            objTypes.add(rId.getSobjectType());
        }

        // Get all of the Enabled subledger definitions for the object types
        List<SubledgerDefinition> defs;
        if (definitionName == null) {
            defs = SubledgerDefinition.getAllDefinitions(objTypes);
        } else {
            defs = new List<SubledgerDefinition>{definitionName};//SubledgerDefinition.getDefinition(definitionName);
        }

        Set<Id> qualified = new Set<Id>();
        Set<Id> hasSources = new Set<Id>();
        for (SubledgerDefinition definition : defs) {
            System.debug(definition.definitionName + ' Enabled?: ' + definition.isEnabled);
            if (definition.isEnabled) {
                // query for the records taking care to use the definitions filter
                // definition.getFindQualifyingRecordsQueryLocator();
                Database.QueryLocator recordsQL = definition.getFindQualifyingRecordsQueryLocator(recordIds);

                // query for other records
                Map<Object, List<SObject>> sourceRecordsByHeader = definition.getSourceRecords(recordIds);

                // Query for existing LedgerEntries
                Map<Object, List<SObject>> ledgersByHeader = definition.getLedgerRecords(recordIds);

                Database.QueryLocatorIterator qli = recordsQL.iterator();
                while (qli.hasNext()) {
                    SObject headerRecord = qli.next();
                    try {
                        qualified.add(headerRecord.Id);

                        List<SObject> sourceRecords = sourceRecordsByHeader.get(headerRecord.Id);

                        // arts-culture-solution #150
                        if ((sourceRecords != null && sourceRecords.size() > 0) || ledgersByHeader.containsKey(headerRecord.Id)) {
                            hasSources.add(headerRecord.Id);

                            if (sourceRecords == null) {
                                sourceRecords = new List<SObject>();
                            }

                            List<SObject> ledgerRecords = ledgersByHeader.get(headerRecord.Id);

                            HeaderProcessor hp = definition.getProcessor(headerRecord, sourceRecords, ledgerRecords);
                            if (hp != null) {
                                records.add(hp);
                            }
                        }
                    } catch (Exception e) {
                        System.debug(e);
                        System.debug(e.getStackTraceString());
                        msgs.get(headerRecord?.Id)?.addError(e);
                    }
                }
            }
        }

        for (Id rid : recordIds) {
            if (!qualified.contains(rid)) {
                Result lesm = msgs.get(rid);
                if (lesm != null) {
                    lesm.addNonFatalMessage('This record does not match the criteria for any Subledger Definitions.');
                }
            }
            if (!hasSources.contains(rid)) {
                Result lesm = msgs.get(rid);
                if (lesm != null) {
                    lesm.addNonFatalMessage('This record does not have any qualified source records for ledger generation.');
                }
            }
        }

        return records;
    }

    public class LESException extends Exception {}

    public class Result implements UnitOfWork.INotify {
        @AuraEnabled
        public Id sourceId;
        @AuraEnabled
        public Boolean isSuccess;
        @AuraEnabled
        public List<Message> messages;
        @AuraEnabled
        public Boolean hasFatalErrors;

        public Result(Id sourceId) {
            this.sourceId   = sourceId;
            this.isSuccess  = true;
            this.hasFatalErrors = false;
            this.messages   = new List<Message>();
        }

        public void addNonFatalMessage(String msg) {
            this.isSuccess = false;
            this.messages.add(new Message(msg, false));
        }

        public void addError(Exception excpt) {
            this.isSuccess = false;
            this.hasFatalErrors = true;
            this.messages.add(new Message(excpt));
        }

        public void success(SObject so, DB.DMLResult result) {
        }

        public void fail(UnitOfWork.UoWException xcpt) {
            addError(xcpt);
        }
    }
    public class Message {
        @AuraEnabled
        public String msgText;
        @AuraEnabled
        public Boolean isFatal;

        public Message(String msgText, Boolean isFatal) {
            this.msgText = msgText;
            this.isFatal = isFatal;
        }

        public Message(Exception e) {
            this.msgText = e.getMessage();
            this.isFatal = true;
        }
    }
    public class LedgerDownloadFilter {
        @AuraEnabled
        public List<String> ledgerStatuses;
        @AuraEnabled
        public Date startDate;
        @AuraEnabled
        public Date endDate;
        @AuraEnabled
        public List<String> oppyRecordTypes;
        @AuraEnabled
        public Id accountId;
        @AuraEnabled
        public Id opportunityId;
    }
    public class LedgerDownload {
        @AuraEnabled
        public final List<LinesEditorController.DatatableColumn> cols;
        @AuraEnabled
        public final List<AccountSummary> accountSummaries;
        @AuraEnabled
        public final List<LedgerEntryLineItem__c> lineItems;

        public LedgerDownload(List<LinesEditorController.DatatableColumn> cols, List<LedgerEntryLineItem__c> lineItems) {
            this.cols               = cols;
            this.lineItems          = lineItems;

            Map<String,AccountSummary> summaries = new Map<String,AccountSummary>();
            for (LedgerEntryLineItem__c leli : lineItems) {
                AccountSummary summary = summaries.get(leli.LedgerAccount__r.GLAccount__r.AccountSubType__c);
                if (summary == null) {
                    summary = new AccountSummary(leli.LedgerAccount__r);
                    summaries.put(leli.LedgerAccount__r.GLAccount__r.AccountSubType__c, summary);
                }

                summary.applyTransaction(leli);
            }

            this.accountSummaries = summaries.values();
            this.accountSummaries.sort(new LedgerSummaryComparator());
        }
    }
    public class AccountSummary extends LedgerSummary {
        @AuraEnabled
        public List<LedgerSummary> accounts {get {return this.accountsMap.values();}}
        public final Map<String,LedgerSummary> accountsMap;

        public AccountSummary(LedgerAccount__c acct) {
            super(acct);
            this.accountName    = acct.GLAccount__r.AccountSubType__c?.replaceAll('_',' ');
            this.accountsMap    = new Map<String,LedgerSummary>();
        }

        public override void applyTransaction(LedgerEntryLineItem__c leli) {
            super.applyTransaction(leli);

            LedgerSummary account = accountsMap.get(leli.LedgerAccount__r.GLAccount__r.AccountSubType__c);
            if (account == null) {
                account = new LedgerSummary(leli.LedgerAccount__r);
                accountsMap.put(leli.LedgerAccount__r.GLAccount__r.AccountSubType__c, account);
            }
            account.applyTransaction(leli);
        }
    }
    public virtual class LedgerSummary {
        private final transient LedgerAccount__c account;
        @AuraEnabled
        public String accountName;
        @AuraEnabled
        public Decimal totalDebits;
        @AuraEnabled
        public Decimal totalCredits;
        @AuraEnabled
        public Decimal balance;
        public TransactionType accountsTransType;

        public LedgerSummary (LedgerAccount__c acct) {
            this.account            = acct;
            this.accountName        = acct.Name;
            this.totalDebits        = ((Decimal)0.00).setScale(2);
            this.totalCredits       = ((Decimal)0.00).setScale(2);
            this.balance            = ((Decimal)0.00).setScale(2);

            AccountSubType acctSubType  = AccountSubType.valueOf(acct?.GLAccount__r?.AccountSubType__c);
            AccountType acctType        = LedgerBalance.ACCOUNTSUBTYPE_ACCOUNTTYPE.get(acctSubType);
            this.accountsTransType      = LedgerBalance.ACCOUNT_TRANSACTION_TYPE.get(acctType);
        }

        public virtual void applyTransaction(LedgerEntryLineItem__c leli) {
            switch on TransactionType.valueOf(leli.Type__c) {
                when Debit {
                    this.totalDebits += leli.Amount__c;
                }
                when Credit {
                    this.totalCredits += leli.Amount__c;
                }
            }

            if (this.accountsTransType == TransactionType.valueOf(leli.Type__c)) {
                this.balance += leli.Amount__c;
            } else {
                this.balance -= leli.Amount__c;
            }
        }
    }

    public class LedgerSummaryComparator implements Comparator<LedgerSummary> {
        public Integer compare(LedgerSummary param1, LedgerSummary param2) {
            //AccountSubType ast1 = AccountSubType.valueOf(param1.account.GLAccount__r.AccountSubType__c);
            //AccountSubType ast2 = AccountSubType.valueOf(param2.account.GLAccount__r.AccountSubType__c);
            return param1.account.Name.compareTo(param2.account.Name);//ast1.ordinal() - ast2.ordinal();
        }
    }
}