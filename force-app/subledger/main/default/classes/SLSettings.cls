/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 9/4/23.
 */

public with sharing class SLSettings {
    private static SLSettings singleton;
    private final SubledgerSettings__c record;

    private SLSettings(SubledgerSettings__c record) {
        this.record = record;
        loadSettings();
    }

    public static SLSettings getInstance() {
        if (singleton == null) {
            singleton = new SLSettings(SubledgerSettings__c.getOrgDefaults());
        }
        return singleton;
    }

    public Boolean isEnabled;
    public Boolean use13Periods;




    private void loadSettings () {  {
        this.isEnabled      = this.record.IsEnabled__c == true;
        this.use13Periods   = this.record.Use13Periods__c == true;
    }

    }
    public void saveSettings () {
        SubledgerSettings__c theSettingsRecord = SubledgerSettings__c.getOrgDefaults();

        theSettingsRecord.IsEnabled__c      = this.isEnabled == true;
        theSettingsRecord.Use13Periods__c   = this.use13Periods == true;


        upsert theSettingsRecord;
    }
}