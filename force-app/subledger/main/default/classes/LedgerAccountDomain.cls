/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 3/28/23.
 */

public without sharing class LedgerAccountDomain extends Domain {
    public static final Set<DescribeFieldResult> DIMENSION_FIELDS  = SObjectUtils.getReferenceFieldsOfType(LedgerAccount__c.SObjectType, AccountingDimension__c.SObjectType);

    public LedgerAccountDomain (){}
    public LedgerAccountDomain (List<LedgerAccount__c> records) {
        super(records);
    }

    public override void doBeforeInsert() {
        Map<String,UnitOfWork.RecordTx> acctDimsByIdxCode    = new Map<String,UnitOfWork.RecordTx>();
        Map<String,UnitOfWork.RecordTx> glAcctsByCode        = new Map<String,UnitOfWork.RecordTx>();

        UnitOfWork uow = new UnitOfWork(DB.init(false, false, false));
        for (LedgerAccount__c la : (List<LedgerAccount__c>)this.triggerRecords) {
            LedgerAccountFormat laf = LedgerAccountFormat.getLedgerAccountFormat(la.Name);
            if (laf == null) {
                la.addError('This account code\'s format is not supported: ' + la.Name);
            } else {
                laf.setLedgerAccount(la);

                la.GLAccount__r.Name = la.GLAccount__r.GLCode__c;

                UnitOfWork.RecordTx glRTX = glAcctsByCode.get(la.GLAccount__r.GLCode__c);
                if (glRTX == null) {
                    glRTX = uow.addUpsert(la.GLAccount__r, GLAccount__c.GLCode__c);
                    glAcctsByCode.put(la.GLAccount__r.GLCode__c, glRTX);
                }
                glRTX.addChildRecord(la, LedgerAccount__c.GLAccount__c);

                for (LedgerAccountFormat.SegmentDefinition sd : laf.segments) {
                    if (sd.type?.equalsIgnoreCase('GL_Account') == false) {
                        AccountingDimension__c acctDim = (AccountingDimension__c)la.getSObject(sd.ledgerAccountField);
                        if (acctDim != null) {

                            UnitOfWork.RecordTx adRTX = acctDimsByIdxCode.get(acctDim.IndexedCode__c);
                            if (adRTX == null) {
                                adRTX = uow.addUpsert(acctDim, AccountingDimension__c.IndexedCode__c);
                                acctDimsByIdxCode.put(acctDim.IndexedCode__c, adRTX);
                            }
                            adRTX.addChildRecord(la, sd.ledgerAccountField);

                            System.debug('Accounting Dimension: (' + sd.type + ') ' + acctDim);
                        }
                    }
                }
            }
            la.UniqueIndex__c   = la.Name;
        }

        uow.checkpoint();
        uow.commitWork();
    }


    /**
     * Rules
     *  - Don't allow any GL or Dimension changes
     *
     * @param oldRecordsMap the records before they were changed
     */
    public override void doValidateUpdate(Map<Id, SObject> oldRecordsMap) {
        if (!SLSettings.getInstance().isEnabled) {
            return;
        }

        Set<DescribeFieldResult> allFields = DIMENSION_FIELDS.clone();
        allFields.add(LedgerAccount__c.GLAccount__c.getDescribe());
        allFields.add(LedgerAccount__c.Name.getDescribe());

        for (LedgerAccount__c recNew : (List<LedgerAccount__c>)this.triggerRecords) {
            LedgerAccount__c recOld     = (LedgerAccount__c)oldRecordsMap.get(recNew.Id);

            for (DescribeFieldResult sof : allFields) {
                SObjectUtils.validateNoChange(recOld, recNew, sof.sobjectField, 'Field cannot be changed.');
            }
        }
    }
}