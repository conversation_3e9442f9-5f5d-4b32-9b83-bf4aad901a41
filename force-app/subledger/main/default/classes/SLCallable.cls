/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 7/16/23.
 */

global with sharing class SLCallable implements System.Callable {
    global Object call(String param1, Map<String, Object> param2) {
        switch on (param1) {

            /*
                Callable c = new flmas.SLCallable();
                c.call('LedgerEntryService.processRecords',new Map<String,Object>{'parentRecordIds'=>new Set<Id>{'0060300000ArEeaAAF',...}});
            */
            when 'LedgerEntryService.processRecords' {
                Set<Id> recordIds = (Set<Id>)param2.get('parentRecordIds');
                LedgerEntryService.processRecords(recordIds, null);
            }

            /*
                Callable c = new flmas.SLCallable();
                c.call('EnableSL',new Map<String,Object>{'value'=>true});
            */
            when 'EnableSL' {
                Boolean b = (Boolean)param2.get('value');
                if (b != null) {
                    SLSettings settings = SLSettings.getInstance();
                    settings.isEnabled = b;
                    settings.saveSettings();
                }
            }

            /*
                Callable c = new flmas.SLCallable();
                c.call('Use13Periods',new Map<String,Object>{'value'=>true});
            */
            when 'Use13Periods' {
                Boolean b = (Boolean)param2.get('value');
                if (b != null) {
                    SLSettings settings = SLSettings.getInstance();
                    settings.use13Periods = b;
                    settings.saveSettings();
                }
            }

            /*
                Callable c = new flmas.SLCallable();
                c.call('CreateAccountingPeriods',new Map<String,Object>{
                    'startFiscalYear' => 2018
                    ,'numberOfPeriods' => 20
                });
             */
            when 'CreateAccountingPeriods' {
                Integer startFiscalYear = (Integer)param2.get('startFiscalYear');
                Integer numberOfPeriods = (Integer)param2.get('numberOfPeriods');

                AccountingPeriodService.createPeriods(startFiscalYear, numberOfPeriods);
            }

            /*
                Callable c = new flmas.SLCallable();
                c.call('BatchLedgerEntry.schedule',new Map<String,Object>());
             */
            when 'BatchLedgerEntry.schedule' {
                BatchLedgerEntry.schedule();
            }

            /*
                Callable c = new flmas.SLCallable();
                c.call('BatchLedgerEntry.runNow',new Map<String,Object>());
             */
            when 'BatchLedgerEntry.runNow' {
                BatchLedgerEntry.runNow();
            }

            /*
                Callable c = new flmas.SLCallable();
                c.call('DMLBatch',new Map<String,Object>{
                    'soql'      => 'UPDATE Contact SET MailingStreet = Account.BillingStreet WHERE MailingStreet != Account.BillingStreet AND Id NOT IN :contactIds'
                    'bindMap'   => new Map<String,Object>{'contactIds'=>new Set<Id>()}}
                    'batchSize' => 200
                });
             */
            when 'DMLBatch' {
                String soql                 = (String)param2.get('soql');
                Map<String,Object> bindMap  = (Map<String,Object>)param2.get('bindMap');
                Integer batchSize           = (Integer)param2.get('batchSize');
                DMLBatch.go(soql, bindMap, batchSize);
            }

            when else {
                throw new ExtensionMalformedCallException('Method not implemented');
            }
        }

        return null;
    }
    public class ExtensionMalformedCallException extends Exception {}
}