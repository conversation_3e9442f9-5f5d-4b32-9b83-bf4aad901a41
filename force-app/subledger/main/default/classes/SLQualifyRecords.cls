/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 8/30/23.
 */

public without sharing class SLQualifyRecords extends Domain {

    public override void doAfterInsert() {
        LedgerEntryService.qualifyRecords(this.triggerRecords, null);
    }

    public override void doAfterUpdate(Map<Id, SObject> oldRecordsMap) {
        LedgerEntryService.qualifyRecords(this.triggerRecords, oldRecordsMap);
    }

    public override void doAfterDelete () {
        LedgerEntryService.qualifyRecords(this.triggerRecords, null);
    }
}