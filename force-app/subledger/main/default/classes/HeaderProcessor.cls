/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 11/21/23.
 */

public abstract with sharing class HeaderProcessor {

    @TestVisible
    protected final Map<String,LedgerBalance> presentBalancesByIds; // The balances that the source header *should* have
    @TestVisible
    protected final Map<String,LedgerBalance> existingBalancesByIds; // The balances that the source header *actually* has, based on Ledger Entries with a status of Approved or Copied...
    @TestVisible
    protected final List<ExistingLedger> existingLedgers;
    protected final SubledgerDefinition definition;
    @TestVisible
    protected Date headerDate;
    @TestVisible
    protected SObject header;

    public HeaderProcessor(SubledgerDefinition definition) {
        Assert.paramRequired(definition, 'definition');

        this.definition             = definition;
        this.existingLedgers        = new List<ExistingLedger>();
        this.presentBalancesByIds   = new Map<String,LedgerBalance>();
        this.existingBalancesByIds  = new Map<String,LedgerBalance>();
    }

    public HeaderProcessor(SubledgerDefinition definition,
            SObject header,
            List<SObject> sourceRecords,
            List<LedgerEntry__c> existingLedgerEntries) {
        this(definition);

        Assert.paramRequired(header, 'header');

        this.header             = header;
        this.headerDate         = (Date)header.get(definition.getFieldHeaderDate());

        if (sourceRecords != null) {
            this.addSourceRecords(sourceRecords);
        }
        if (existingLedgerEntries != null) {
            this.addExistingLedgers(existingLedgerEntries);
        }
    }


    public abstract void calculate();
    public abstract void addSourceRecords(List<SObject> sourceRecords);
    public abstract void addExistingLedger(LedgerEntry__c ledgerEntry);
    public abstract List<Ledger> getLedgersToCreate();
    public abstract List<LedgerEntry__c> getLedgersToDelete();


    public SObject getHeader() {
        return this.header;
    }

    public Date getHeaderDate() {
        return this.headerDate;
    }

    public List<LedgerBalance> getExistingBalances() {
        for (ExistingLedger re : this.existingLedgers) {
            re.applyAmount();
        }
        return this.existingBalancesByIds.values();
    }

    public List<LedgerBalance> getPresentBalances() {
        return this.presentBalancesByIds.values();
    }

    public LedgerBalance getPresentLedgerBalance(LedgerAccount__c accountRecord) {
        return getLedgerBalance(this.presentBalancesByIds, accountRecord, false);
    }
    public LedgerBalance getExistingLedgerBalance(LedgerAccount__c accountRecord) {
        return getLedgerBalance(this.existingBalancesByIds, accountRecord, true);
    }

    public virtual void addExistingLedgers(List<LedgerEntry__c> ledgerEntries) {
        for (LedgerEntry__c leHdr : ledgerEntries) {
            this.addExistingLedger(leHdr);
        }
    }
    protected void addExistingLedger(ExistingLedger el) {
        this.existingLedgers.add(el);
    }

    /**
     * Processes LedgerEntries into the database.
     *
     * Deletes unapproved ledgers and creates new ledgers.
     *
     * @param uow the UnitOfWork instance to use for DMLing Ledger Entries
     * @param notificationRecipient an INotify implementation that should receive notifications of DML successes/failures
     */
    public void processRecord(UnitOfWork uow, UnitOfWork.INotify notificationRecipient) {
        calculate();

        for (LedgerEntry__c le : this.getLedgersToDelete()) {
            uow.addDelete(le, notificationRecipient);
        }

        for (Ledger l : this.getLedgersToCreate()) {
            System.debug(l);
            UnitOfWork.RecordTx lrtx = uow.addInsert(l.ledgerEntry, notificationRecipient);
            uow.checkpoint();
            for (LedgerEntryLineItem__c leli : l.lineItems) {
                System.debug(leli);
                System.debug(leli.LedgerAccount__r);
                UnitOfWork.RecordTx gltx = uow.addUpsert(leli.LedgerAccount__r, notificationRecipient, LedgerAccount__c.Name);
                uow.addInsert(leli, notificationRecipient);
                gltx.addChildRecord(leli, LedgerEntryLineItem__c.LedgerAccount__c);
                lrtx.addChildRecord(leli, LedgerEntryLineItem__c.LedgerEntry__c);
                uow.checkpoint();
            }
        }
        uow.checkpoint();
    }

    private static LedgerBalance getLedgerBalance(Map<String,LedgerBalance> collection, LedgerAccount__c accountRecord, Boolean isExisting) {
        LedgerBalance lb;
        if (accountRecord != null) {
            lb = collection.get(accountRecord.Name);
            if (lb == null) {
                lb = new LedgerBalance(accountRecord, isExisting);
                collection.put(accountRecord.Name, lb);
            }
        }
        return lb;
    }

    public class ExistingLedger {
        public final Decimal amount;
        public final LedgerBalance account;
        public final LedgerEntryLineItem__c record;
        public final Boolean isPosted; // True if this record has been approved or posted and should not be deleted.
        private Boolean isApplied; // True if this record has been applied to the account balance. Useful so that we don't double count this instance.

        public ExistingLedger(LedgerBalance account, LedgerEntryLineItem__c record, Boolean isPosted) {
            this.account    = account;
            this.record     = record;
            this.isPosted   = isPosted;
            this.amount     = record.Amount__c;

            this.isApplied  = false;

            applyAmount();
        }
        public void applyAmount() {
            System.debug('ExistingLedger: ' + this);
            System.debug('applyAmount: ' + this.isApplied);
            System.debug('applyAmount: ' + this.account);
            if (this.isApplied) {
                return; // Only apply this line item 1 time.
            }
            this.isApplied = true;
            this.account.applyAmount(TransactionType.valueOf(record.Type__c), record.Amount__c, this.record.LedgerEntry__r.PostDate__c, this.isPosted);
        }
    }
}