/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 11/28/23.
 */

public with sharing class LedgerAccountService {
    private static Map<String,GLAccount__c> ALL_GL_ACCOUNTS;

    public static GLAccount__c getGLAccount(String glAccountCode) {
        if (ALL_GL_ACCOUNTS == null) {
            ALL_GL_ACCOUNTS = new Map<String,GLAccount__c>();
            for (GLAccount__c la : [
                    SELECT Id, Name, GLCode__c, AccountType__c, AccountSubType__c
                    FROM GLAccount__c
            ]) {
                ALL_GL_ACCOUNTS.put(la.GLCode__c.toLowerCase(), la);
            }
        }

        return ALL_GL_ACCOUNTS.get(glAccountCode?.toLowerCase());
    }
}