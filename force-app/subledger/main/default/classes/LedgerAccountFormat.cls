/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 3/28/23.
 */

public inherited sharing class LedgerAccountFormat {
    @TestVisible
    private static final Map<Id,LedgerAccountFormat> ALL_FORMATS = new Map<Id,LedgerAccountFormat>();
    @TestVisible
    private static final Map<Schema.SObjectType,List<LedgerAccountFormat>> ALL_FORMATS_BY_OBJECT = new Map<Schema.SObjectType,List<LedgerAccountFormat>>();
    private static final Map<SObjectType,ParentQuery> ALL_PARENT_OBJS = new Map<SObjectType,ParentQuery>();
    private static final DescribeSObjectResult LA_DESCRIBE = LedgerAccount__c.SObjectType.getDescribe();

    @TestVisible
    private final LedgerAccountFormat__mdt definitionRecord;
    private final Pattern p;
    @TestVisible
    private final SObjectType sourceType;
    private final SObjectField lookupField; // The field on a source record that references a LedgerAccount record.
    @TestVisible
    private final String defaultAccountCodeFormula;
    @TestVisible
    private final DescribeSObjectResult sourceSObjectResult;
    private final Map<String,SObjectType> parentRefFields;  // only the *relationship* fields (on the source object) referenced by formulas. fieldName, the object type the field references
    private final Set<SObjectField> formulaRefs; // the fields (on the source object) referenced by formulas, includes relationships.
    private final Map<SObjectType,ParentQuery> queries;
    @TestVisible
    private final String separatorChar;
    public final List<SegmentDefinition> segments;
    protected Matcher currentMatcher;

    static {
        for (LedgerAccountFormat__mdt laf : [
                SELECT Id, DeveloperName, Label, FormatValidation__c, LedgerAccountObject__c, DefaultAccountCodeFormula__c,
                        ObjectWithAccountLookup__r.QualifiedApiName,
                        AccountLookupField__r.QualifiedApiName,AccountLookupField__r.EntityDefinition.QualifiedApiName,
                        SegmentSeparatorChar__c,
                        Segment1Type__c, Segment1Field__r.QualifiedApiName, Segment1ReplacementRules__c, Segment1ValueFormula__c,
                        Segment2Type__c, Segment2Field__r.QualifiedApiName, Segment2ReplacementRules__c, Segment2ValueFormula__c,
                        Segment3Type__c, Segment3Field__r.QualifiedApiName, Segment3ReplacementRules__c, Segment3ValueFormula__c,
                        Segment4Type__c, Segment4Field__r.QualifiedApiName, Segment4ReplacementRules__c, Segment4ValueFormula__c,
                        Segment5Type__c, Segment5Field__r.QualifiedApiName, Segment5ReplacementRules__c, Segment5ValueFormula__c,
                        Segment6Type__c, Segment6Field__r.QualifiedApiName, Segment6ReplacementRules__c, Segment6ValueFormula__c,
                        Segment7Type__c, Segment7Field__r.QualifiedApiName, Segment7ReplacementRules__c, Segment7ValueFormula__c,
                        Segment8Type__c, Segment8Field__r.QualifiedApiName, Segment8ReplacementRules__c, Segment8ValueFormula__c,
                        Segment9Type__c, Segment9Field__r.QualifiedApiName, Segment9ReplacementRules__c, Segment9ValueFormula__c,
                        Segment10Type__c, Segment10Field__r.QualifiedApiName, Segment10ReplacementRules__c, Segment10ValueFormula__c
                FROM LedgerAccountFormat__mdt
                WHERE IsActive__c = TRUE
        ]) {
            LedgerAccountFormat laft = new LedgerAccountFormat(laf);
            addFormat(laft);
        }
    }

    @TestVisible
    private LedgerAccountFormat() {
        this.parentRefFields            = new Map<String,SObjectType>();
        this.formulaRefs                = new Set<SObjectField>();
        this.queries                    = new Map<SObjectType,ParentQuery>();
        this.segments                   = new List<SegmentDefinition>();
    }

    @TestVisible
    private LedgerAccountFormat(
            Pattern p,
            SObjectType sourceType,
            String defaultAccountCodeFormula,
            DescribeSObjectResult sourceSObjectResult,
            String separatorChar
    ) {
        this();
        this.p = p;
        this.sourceType = sourceType;
        this.defaultAccountCodeFormula = defaultAccountCodeFormula;
        this.sourceSObjectResult        = sourceSObjectResult;
        this.separatorChar              = separatorChar;
    }

    @TestVisible
    private LedgerAccountFormat(LedgerAccountFormat__mdt definitionRecord) {
        this();
        this.definitionRecord           = definitionRecord;
        this.p                          = Pattern.compile(definitionRecord.FormatValidation__c);
        this.sourceType                 = SObjectUtils.getSObjectType(definitionRecord.ObjectWithAccountLookup__r.QualifiedApiName);
        this.lookupField                = SObjectUtils.getSObjectField(definitionRecord.AccountLookupField__r);
        this.defaultAccountCodeFormula  = definitionRecord.DefaultAccountCodeFormula__c;
        this.sourceSObjectResult        = SObjectUtils.getSObjectDescribe(definitionRecord.ObjectWithAccountLookup__r);

        this.separatorChar              = definitionRecord.SegmentSeparatorChar__c;

        // Build the segments
        for (Integer i=1; i<=10; i++) {
            String type = (String)this.definitionRecord.get('Segment'+i+'Type__c');
            if (String.isNotBlank(type)) {
                segments.add(new SegmentDefinition(
                        i-1
                        , type
                        , (FieldDefinition)this.definitionRecord.getSObject('Segment'+i+'Field__r')
                        , (String)this.definitionRecord.get('Segment'+i+'ReplacementRules__c')
                        , (String)this.definitionRecord.get('Segment'+i+'ValueFormula__c')
                ));
            }
        }

        addFormula(this.defaultAccountCodeFormula);

        for (SegmentDefinition sd : this.segments) {
            addFormula(sd.valueExpression);
            for (String f : sd.getReplacementFormulas()) {
                addFormula(f);
            }
        }

        this.formulaRefs.remove(null);
        this.parentRefFields.remove(null);

        System.debug(definitionRecord.FormatValidation__c);
    }

    public Set<String> getQueryFields() {
        Set<String> qryFields = new Set<String>();

        if (this.defaultAccountCodeFormula?.startsWith('"') == false) {
            qryFields.add(this.defaultAccountCodeFormula);
        }
        for (SegmentDefinition sd : this.segments) {
            if (sd.valueExpression?.startsWith('"') == false) {
                qryFields.add(sd.valueExpression);
            }
        }

        return qryFields;
    }

    /*
     The purpose of this method is to identify if a given formula references a parent lookup field
     and if so, add that reference to a list for easy access later
     */
    private void addFormula(String formula) {
        if (formula?.startsWith('"') == false && this.sourceSObjectResult != null) {
            Map<String, SObjectField> dsorFields = this.sourceSObjectResult == null ? new Map<String, SObjectField>() : this.sourceSObjectResult.fields.getMap();

            String fieldRef = formula.substringBefore('.')?.toLowerCase();
            if (fieldRef.endsWith('__r')) {
                fieldRef = fieldRef.replace('__r', '__c');
            } else if (!fieldRef.endsWith('Id') && formula?.contains('.') == true) {
                fieldRef += 'Id';
            }
            System.debug('fieldRef: ' + fieldRef);
            System.debug('this.sourceSObjectResult: ' + this.sourceSObjectResult?.name);

            DescribeFieldResult dfr = dsorFields.get(fieldRef)?.getDescribe();

            if (dfr != null) {
                List<SObjectType> refs = dfr.getReferenceTo();
                SObjectType parentObjType;
                if (refs != null && refs.size() > 0) {
                    // This is a reference to another object
                    parentObjType = refs.get(0);
                    System.debug('parentObjType: ' + parentObjType);

                    // Get/Make a ParentQuery instance. This identifies the fields to query on the parent object and the
                    // records to pull.
                    ParentQuery pq = ALL_PARENT_OBJS.get(parentObjType);
                    if (pq == null) {
                        pq = new ParentQuery(parentObjType);
                        ALL_PARENT_OBJS.put(parentObjType, pq);
                    }
                    this.queries.put(parentObjType, pq);

                    // Add the rest of the formula field
                    pq.queryFields.add(formula.substringAfter('.')?.toLowerCase());

                    this.parentRefFields.put(fieldRef, parentObjType);
                }

                this.formulaRefs.add(dfr.sobjectField);
            }
        }
    }

    public Map<SObjectType,ParentQuery> getParentQueries(List<SObject> records) {
        for (String parentRefField : this.parentRefFields.keySet()) {
            System.debug('parentRefField: ' + parentRefField);
            ParentQuery pq = this.queries.get(this.parentRefFields.get(parentRefField));
            for (SObject rec : records) {
                Id parentId = (Id)rec.get(parentRefField);
                System.debug('parentId: ' + parentId);
                pq.parentIds.add(parentId);
            }
        }

        return this.queries;
    }

    public Boolean matches(String accountCodeString) {
        this.currentMatcher     = this.p.matcher(accountCodeString);
        return this.currentMatcher.matches();
    }

    /**
     * Updates the provided LedgerAccount__c record's dimension fields
     *
     * @param record The LedgerAccount record to update
     *
     * @return the LedgerAccount record instance that was updated
     */
    public LedgerAccount__c setLedgerAccount(LedgerAccount__c record) {
        if (this.currentMatcher == null) {
            matches(record.Name);
        }

        System.debug('Group Count: ' + this.currentMatcher.groupCount());
        System.debug(this.definitionRecord);
        System.debug(JSON.serialize(this.definitionRecord));
        for (Integer i=1; i<=this.currentMatcher.groupCount(); i++) {
            String value    = this.currentMatcher.group(i);
            System.debug(StringUtils.format('Group {grpNum}: {value}').set('{grpNum}',i).set('{value}',value));

            SegmentDefinition sd = this.segments.get(i-1);

            if (!String.isBlank(value) && sd != null) {
                String type = sd.type;
                System.debug('Segment Type: ' + type);

                if ('GL_Account'.equalsIgnoreCase(type)) {
                    record.GLAccount__r = new GLAccount__c(GLCode__c = value);
                } else {
                    Id rtId = AccountingDimensionDomain.RECORDTYPEINFOS_BY_DEVNAME.get(type).recordTypeId;
                    String code = value;
                    value += '-' + rtId;
                    System.debug('Dimension value: ' + value);

                    if (sd.ledgerAccountField != null) {
                        record.putSObject(sd.ledgerAccountField, new AccountingDimension__c(IndexedCode__c = value, Code__c = code, Name = code, RecordTypeId=rtId));
                    }
                }
            }
        }
        return record;
    }

    /**
     * Updates the specified source SObject's target account field with the LedgerAccount__c computed
     * by this LedgerAccountFormat instance. Adds the LedgerAccount__c record to a map so that any new LedgerAccounts
     * can be inserted to the DB.
     *
     * @param so The source SObject to update
     * @param la The ledger account record to set on the SObject
     * @return The LedgerAccount that was applied to the SObject
     */
    public LedgerAccount__c setAccountLookup(SObject so, LedgerAccount__c la) {
        if (la != null) {
            System.debug(LoggingLevel.FINEST, 'Putting account: ' + la);
            so.putSObject(this.lookupField, la);
        }

        return la;
    }

    /**
     * Determines the LedgerAccount__c that should be on the provided SObject instance as computed
     * by this LedgerAccountFormat instance.
     *
     * @param so The SObject instance to compute a Ledger Account for
     *
     * @return The Ledger Account
     */
    public LedgerAccount__c getLedgerAccount(SObject so) {
        String accountCode = '';

        // Is the format using a default account code?
        if (String.isNotBlank(this.defaultAccountCodeFormula)) {
            accountCode = (String)getValue(so, this.defaultAccountCodeFormula);
        }

        System.debug('accountCode: ' + accountCode);
        if (String.isBlank(accountCode)) {
            //so.putSObject(this.lookupField, null);
            return null;
        }

        List<String> acSegments = accountCode.split(this.separatorChar);

        /*
        loop through all segments.
        if the account code is empty, or if the segments part in the account code is all "_", calculate the segment's portion
         */
        LedgerAccount__c la = new LedgerAccount__c();

        for (SegmentDefinition sd : this.segments) {
            Object acSeg    = acSegments.size() > sd.pos ? acSegments.get(sd.pos) : null;
            System.debug(LoggingLevel.FINEST, 'Segment ' + sd.pos + ': ' + acSeg);
            System.debug(LoggingLevel.FINEST, sd.valueExpression);

            if (String.isNotBlank(sd.valueExpression)) {
                acSeg = getValue(so, sd.valueExpression);
                System.debug(LoggingLevel.FINEST, acSeg);
            }

            // Process replacement rules
            acSeg = sd.processRules(acSeg, so);
            System.debug(LoggingLevel.FINEST, acSeg);

            String sVal = String.valueOf(acSeg);
            System.debug(LoggingLevel.FINEST, sVal);
            if (String.isNotBlank(sVal)) {
                acSegments.set(sd.pos, sVal);
            }

            System.debug(LoggingLevel.FINEST, 'sd.type: ' + sd.type);
            if (sd.type?.equalsIgnoreCase('GL_Account') == true) {
                la.GLAccount__r     = LedgerAccountService.getGLAccount(sVal);
            }
        }

        accountCode = String.join(acSegments, this.separatorChar);
        System.debug(LoggingLevel.FINEST, accountCode);

        la.Name             = accountCode;
        la.UniqueIndex__c   = accountCode;

        return la;
    }

    public void setAccountLookupId(SObject so) {
        LedgerAccount__c la = (LedgerAccount__c)so.getSObject(this.lookupField);
        if (la?.Id != null) {
            System.debug(LoggingLevel.FINEST, 'Putting account id: ' + la);
            so.put(this.lookupField, la?.Id);
        }
    }

    private Object getValue(SObject so, String formula) {
        formula = formula?.toLowerCase();

        Object value = '';
        if (formula?.startsWith('"') == true) {
            value = formula?.removeStart('"').removeEnd('"');
        } else {
            value = SObjectUtils.getValueForPath(so, formula);

            if (value == null && formula?.contains('.') == true) {
                String parentRef = formula.substringBefore('.');
                if (parentRef.endsWith('__r')) {
                    parentRef = parentRef.replace('__r','__c');
                } else if (!parentRef.endsWith('Id')) {
                    parentRef += 'Id';
                }

                Id parentId                 = (Id)so.get(parentRef);
                if (parentId != null) {
                    SObjectType parentObjType   = this.parentRefFields.get(parentRef);
                    ParentQuery pq              = this.queries.get(parentObjType);
                    String path                 = formula?.substringAfter('.');

                    value = SObjectUtils.getValueForPath(pq.getRecords().get(parentId), path);
                }
            }
        }

        return value;
    }

    public static LedgerAccountFormat getLedgerAccountFormat(String accountCodeString) {
        for (LedgerAccountFormat laf : ALL_FORMATS.values()) {
            if (laf.matches(accountCodeString)) {
                return laf;
            }
        }
        return null;
    }

    public static LedgerAccountFormat getLedgerAccountFormat(Id ledgerAccountFormatId) {
        return ALL_FORMATS.get(ledgerAccountFormatId);
    }

    public static LedgerAccount__c sSetLedgerAccount(LedgerAccount__c record) {
        return getLedgerAccountFormat(record.Name)?.setLedgerAccount(record);
    }

    /**
     * Calculates the ledger account for the provided newRecords based on the rules
     * defined for that object.
     *
     * @param newRecords the new records from the trigger
     * @param oldRecords the old version of the records from the trigger
     */
    public static void sApplyCode(List<SObject> newRecords, Map<Id,SObject> oldRecords) {
        System.debug('sApplyCode:enter');
        if (!SLSettings.getInstance().isEnabled) {
            System.debug('Subledger not enabled.');
            return;
        }

        /*
            1. Determine the object
            2. Query for the extended data
            3. Find the active rules for that object using ObjectWithAccountLookup__c
            4. For each rule:
                a. Determine if the record is new or if the AccountLookupField__c is null
                b. Build / Upsert a Ledger Account record and link to the AccountLookupField__c
         */
        Schema.SObjectType sot                  = newRecords?.get(0).getSObjectType();

        // Determine which records should be processed
        List<SObject> recordsToProcess          = new List<SObject>();
        for (SObject soNew : newRecords) {
            try {
                for (LedgerAccountFormat laf : getFormatsForSObjectType(sot)) {
                    System.debug(LoggingLevel.FINEST, 'LedgerAccountFormat: ' + laf.definitionRecord.Label + '(' + laf.definitionRecord.DeveloperName + ')');
                    System.debug(LoggingLevel.FINEST, 'laf.lookupField: ' + laf.lookupField);
                    // Only process records if the target field is empty.
                    if (laf.lookupField != null && soNew.get(laf.lookupField) == null) {
                        recordsToProcess.add(soNew);
                        break; // Avoid possibly adding the same record again.
                    }

                    // OR, if a field referenced by the default account code or segement X formulas has changed
                    if (laf.lookupField != null && oldRecords != null) {
                        System.debug('oldRecords...');
                        for (SObjectField fieldRef : laf.formulaRefs) {
                            System.debug('fieldRef: ' + fieldRef);
                            SObject soOld = oldRecords.get(soNew.Id);
                            System.debug('Old Value: ' + soOld.get(fieldRef));
                            System.debug('New Value: ' + soNew.get(fieldRef));
                            if (soNew.get(fieldRef) != soOld.get(fieldRef)) {
                                soNew.put(laf.lookupField, null); // null out the account lookup field so that it's updated later
                                recordsToProcess.add(soNew);
                                break; // Avoid possibly adding the same record again.
                            }
                        }
                    }
                }
            } catch (Exception e) {
                System.debug(e);
                System.debug(e.getStackTraceString());
                soNew.addError(e);
            }
        }

        System.debug('recordsToProcess: ' + recordsToProcess.size());
        if (recordsToProcess.size() > 0) {
            // Clear existing data from parentQueries to make it safe for triggers called multiple times in a single Tx
            for (ParentQuery pq : ALL_PARENT_OBJS.values()) {
                pq.parentIds?.clear();
                pq.data = null;
            }

            // Query for the extended parent data
            Map<SObjectType,ParentQuery> recordsExt = new Map<SObjectType,ParentQuery>();
            for (LedgerAccountFormat laf : getFormatsForSObjectType(sot)) {
                recordsExt.putAll(laf.getParentQueries(recordsToProcess));
            }
            for (ParentQuery pq : recordsExt.values()) {
                pq.getRecords();
            }

            // Update records with their account codes
            Map<String,LedgerAccount__c> ledgerAccountMap = new Map<String,LedgerAccount__c>();
            for (SObject soNew : recordsToProcess) {
                System.debug(LoggingLevel.FINEST, 'Processing record: ' + soNew);
                try {
                    for (LedgerAccountFormat laf : getFormatsForSObjectType(sot)) {
                        // Only process if the target field is empty
                        if (soNew.get(laf.lookupField) == null) {
                            System.debug(LoggingLevel.FINEST, ' Only process if the target field is empty');
                            LedgerAccount__c acct = laf.getLedgerAccount(soNew);

                            if (acct != null) {
                                if (!ledgerAccountMap.containsKey(acct.Name.toLowerCase())) {
                                    ledgerAccountMap.put(acct.Name.toLowerCase(), acct);
                                }
                                laf.setAccountLookup(soNew, ledgerAccountMap.get(acct.Name.toLowerCase()));
                            }
                        }
                    }
                } catch (Exception e) {
                    System.debug(e);
                    System.debug(e.getStackTraceString());
                    soNew.addError(e);
                }
            }

            // Upsert the ledger account records
            List<Database.UpsertResult> urs = Database.upsert(ledgerAccountMap.values(), LedgerAccount__c.UniqueIndex__c, false);
            System.debug(urs);


            // Update the triggered records with the correct references
            for (SObject soNew : recordsToProcess) {
                try {
                    for (LedgerAccountFormat laf : getFormatsForSObjectType(sot)) {
                        if (soNew.get(laf.lookupField) == null) {
                            System.debug(LoggingLevel.FINEST, 'Update the triggered records with the correct references');
                            laf.setAccountLookupId(soNew);
                        }
                    }
                    System.debug(LoggingLevel.FINEST, 'Updated all LAFs: ' + soNew);
                } catch (Exception e) {
                    System.debug(e);
                    System.debug(e.getStackTraceString());
                    soNew.addError(e);
                }
            }
        }
    }

    public static List<LedgerAccountFormat> getFormatsForSObjectType(SObjectType sot) {
        return ALL_FORMATS_BY_OBJECT.containsKey(sot) ? ALL_FORMATS_BY_OBJECT.get(sot) : new List<LedgerAccountFormat>();
    }

    public static void addFormat(LedgerAccountFormat laf) {
        ALL_FORMATS.put(laf.definitionRecord.Id, laf);
        List<LedgerAccountFormat> lafs = ALL_FORMATS_BY_OBJECT.get(laf.sourceType);
        if (lafs == null) {
            lafs = new List<LedgerAccountFormat>();
            ALL_FORMATS_BY_OBJECT.put(laf.sourceType, lafs);
        }
        lafs.add(laf);
    }

    @TestVisible
    private class ParentQuery {
        private final Set<Id> parentIds;
        @TestVisible
        private final Set<String> queryFields;
        private final SObjectType sot;
        private Map<Id,SObject> data;

        @TestVisible
        private ParentQuery(SObjectType sot) {
            this.parentIds   = new Set<Id>();
            this.queryFields = new Set<String>();
            this.queryFields.add('Id');
            this.sot    = sot;
        }

        @TestVisible
        String buildQuery() {
            if (queryFields.size() > 0) {
                return StringUtils.format('SELECT {fields} FROM {obj} WHERE Id IN :parentIds')
                        .set('{fields}', String.join(new List<String>(queryFields), ', '))
                        .set('{obj}', String.valueOf(sot))
                        .toString();
            }
            return null;
        }

        @TestVisible
        Map<Id,SObject> getRecords() {
            if (data == null) {
                String soql = buildQuery();
                if (String.isNotBlank(soql)) {
                    this.data = new Map<Id,SObject>(Database.query(soql));
                    System.debug('Executed query: ' + soql);
                } else {
                    this.data = new Map<Id,SObject>();
                }
            }
            return this.data;
        }
    }

    public class SegmentDefinition {
        public final Integer pos;                        // 0-base position index of this segment within an entire account code
        public final String type;                        // I'd normally make this an enum, but using a string so that subscribers can add new types and map them to recordtypes
        public final SObjectField ledgerAccountField;    // The field on the LedgerAccount__c record that this segment points to
        public final String valueExpression;             // The expression used to retrieve a source value for this segment
        public final List<SegmentRule> rules;            // The rules for replacing the source value

        @TestVisible
        private SegmentDefinition(Integer pos, String type, SObjectField ledgerAccountField){
            this.pos                = pos;
            this.type               = type;
            this.ledgerAccountField = ledgerAccountField;
            this.rules              = new List<SegmentRule>();
        }

        private SegmentDefinition(Integer pos, String type, FieldDefinition field, String rulesJson, String valueExpression) {
            this.pos                = pos;
            this.type               = type;
            this.valueExpression    = valueExpression;
            this.ledgerAccountField = LA_DESCRIBE.fields.getMap().get(field.QualifiedApiName);

            if (String.isNotBlank(rulesJson)) {
                this.rules  = (List<SegmentRule>) JSON.deserialize(rulesJson, List<SegmentRule>.class);
            } else {
                this.rules  = new List<SegmentRule>();
            }
        }

        private Object processRules(Object sourceValue, SObject so) {
            Object ret;
            for (SegmentRule sr : this.rules) {
                ret = sr.processRule(sourceValue, so);
                if (ret != null) {
                    break;
                }
            }

            if (ret == null) {
                ret = sourceValue;
            }

            return ret;
        }

        private Set<String> getReplacementFormulas() {
            Set<String> formulas = new Set<String>();
            for (SegmentRule sr : rules) {
                formulas.add(sr.getReplacementFormula());
            }
            formulas.remove(null);
            return formulas;
        }
    }

    @TestVisible
    private class SegmentRule {
        @TestVisible
        private ComparisonOperator co;  // The comparison operator
        @TestVisible
        private String ce;              // The right hand comparing expression
        @TestVisible
        private String v;               // The target value to use if the condition is true

        private Object processRule(Object sourceValue, SObject so) {
            String rh = evaluateExpression(so);

            if (matches(sourceValue, rh)) {
                if (this.v.startsWith('"')) {
                    return this.v.removeStart('"').removeEnd('"');
                }
                return String.valueOf(SObjectUtils.getValueForPath(so, this.v));
            }

            return null;
        }

        private String evaluateExpression(SObject so) {
            if (this.ce.startsWith('"')) {
                return this.ce.removeStart('"').removeEnd('"');
            }
            return String.valueOf(SObjectUtils.getValueForPath(so, this.ce));
        }

        private String getReplacementFormula() {
            // is the replacement value a string literal (with doublequotes?) or a formula (no quotes)
            if (!this.v.startsWith('"')) {
                return this.v;
            }
            return null;
        }

        @TestVisible
        private Boolean matches(Object sourceValue, String rh) { // todo consider using the SOQL.WhereStmt. This requires we fuse the queried and triggered records...
            System.debug(sourceValue + ' ' + co + ' ' + rh);

            Integer comparison;
            if (sourceValue instanceof Decimal) {
                comparison = compareTo((Decimal)sourceValue,Decimal.valueOf(rh));
            } else if (sourceValue instanceof Date) {
                comparison = compareTo((Date)sourceValue,getDate(rh));
            } else if (sourceValue instanceof Datetime) {
                comparison = compareTo((Datetime)sourceValue,getDatetime(rh));
            } else if (sourceValue instanceof String) {
                comparison = compareTo((String)sourceValue,rh);
            } else if (sourceValue instanceof Boolean) {
                comparison = compareTo((Boolean)sourceValue, Boolean.valueOf(rh));
            }

            switch on (this.co) {
                when lt {
                    return comparison < 0;
                }
                when lte {
                    return comparison <= 0;
                }
                when e {
                    //return sourceValue == null ? rh == null : sourceValue.equals(rh);
                    return comparison == 0;
                }
                when gte {
                    return comparison >= 0;
                }
                when gt {
                    return comparison > 0;
                }
                when ne {
                    return comparison != 0;
                }
            }
            return false;
        }
        public Datetime getDatetime(String s) {
            s = '"' + s + '"';
            return (Datetime)JSON.deserialize(s, Datetime.class);
        }
        public Date getDate(String s) {
            s = '"' + s + '"';
            return (Date)JSON.deserialize(s, Date.class);
        }
        public Integer compareTo(Decimal dt1, Decimal dt2) {
            return dt1 == dt2 ? 0 : dt1 < dt2 ? -1 : 1;
        }
        public Integer compareTo(Date dt1, Date dt2) {
            return dt1 == dt2 ? 0 : dt1 < dt2 ? -1 : 1;
        }
        public Integer compareTo(Datetime dt1, Datetime dt2) {
            return dt1 == dt2 ? 0 : dt1 < dt2 ? -1 : 1;
        }
        public Integer compareTo(String dt1, String dt2) {
            return dt1 == dt2 ? 0 : dt1 < dt2 ? -1 : 1;
        }
        public Integer compareTo(Boolean b1, Boolean b2) {
            System.debug(b1);
            System.debug(b2);
            return b1 == b2 ? 0 : b1 == true ? 1 : -1;
        }
    }

    @TestVisible
    private enum ComparisonOperator {
        lt      // Less than
        ,lte    // Less or equal than
        ,e      // Equal to
        ,gte    // Greater or equal than
        ,gt     // Greater than
        ,ne     // Not Equal to
    }
}