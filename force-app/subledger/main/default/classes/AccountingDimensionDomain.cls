/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 3/30/23.
 */

public without sharing class AccountingDimensionDomain extends Domain {
    public static final Map<String,RecordTypeInfo> RECORDTYPEINFOS_BY_DEVNAME = AccountingDimension__c.SObjectType.getDescribe().getRecordTypeInfosByDeveloperName();
    public AccountingDimensionDomain (){}
    public AccountingDimensionDomain (List<AccountingDimension__c> records) {
        super(records);
    }

    public override void doBeforeInsert() {
        for (AccountingDimension__c recNew : (List<AccountingDimension__c>)this.triggerRecords) {
            setIndexedCode(recNew);
        }
    }

    public override void doBeforeUpdate(Map<Id, SObject> oldRecordsMap) {
        for (AccountingDimension__c recNew : (List<AccountingDimension__c>)this.triggerRecords) {
            setIndexedCode(recNew);
        }
    }

    private static String setIndexedCode(AccountingDimension__c record) {
        return record.IndexedCode__c   = record.Code__c + '-' + record.RecordTypeId;
    }
}