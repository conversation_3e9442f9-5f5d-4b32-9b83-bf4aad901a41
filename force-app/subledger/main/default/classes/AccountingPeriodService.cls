/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 9/5/23.
 */

public inherited sharing class AccountingPeriodService {
    public static void createPeriods(Integer startFiscalYear, Integer numberOfYears) {
        SLSettings settings = SLSettings.getInstance();

        Date startDate = Date.newInstance(startFiscalYear, DateTimeUtils.getFiscalYearStartMonth(), 1);

        List<flmas__AccountingPeriod__c> periods = new List<flmas__AccountingPeriod__c>();
        for (Integer i=0; i<numberOfYears; i++) {
            for (Integer j=1; j<13; j++) {
                String uniqueId = String.valueOf(startDate.year()) + '-' + String.valueOf(j).leftPad(2, '0');
                periods.add(
                        new flmas__AccountingPeriod__c(flmas__FiscalYear__c = String.valueOf(startDate.year()), flmas__FiscalMonth__c = String.valueOf(j), flmas__UniqueId__c = uniqueId)
                );
            }
            // Period 13
            if (settings.use13Periods) {
                periods.add(
                        new flmas__AccountingPeriod__c(flmas__FiscalYear__c = String.valueOf(startDate.year()), flmas__FiscalMonth__c = '13', UniqueId__c = String.valueOf(startDate.year()) + '-13')
                );
            }
            startDate = startDate.addYears(1);
        }

        DB.init().createOrEdit(periods, flmas__AccountingPeriod__c.UniqueId__c);
    }

    @AuraEnabled
    public static void createPeriods(Integer startFiscalYear, Integer numberOfYears, Boolean use13Periods) {
        SLSettings settings     = SLSettings.getInstance();
        settings.use13Periods   = use13Periods;
        settings.saveSettings();

        // Find the last accounting period if startFiscalYear is blank
        if (startFiscalYear == null) {
            List<AccountingPeriod__c> periods = DB.init().read('SELECT Id, flmas__FiscalYear__c FROM AccountingPeriod__c ORDER BY flmas__FiscalYear__c DESC LIMIT 1').go();

            if (periods.size() == 1) {
                startFiscalYear = Integer.valueOf(periods.get(0).FiscalYear__c);
            } else {
                throw new AccountingPeriodServiceException('Start Fiscal Year is empty and there are no existing periods to extend. Please enter a Start Fiscal Year.');
            }
        }

        createPeriods(startFiscalYear, numberOfYears);
    }

    public class AccountingPeriodServiceException extends Exception {}
}