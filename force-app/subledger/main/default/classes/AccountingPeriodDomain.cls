/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 3/27/23.
 *
 *
 *
 *
 */
public without sharing class AccountingPeriodDomain extends Domain {
    public AccountingPeriodDomain (){}
    public AccountingPeriodDomain (List<AccountingPeriod__c> records) {
        super(records);
    }

    /**
     * Set the Period Start/End based on the Calendar Month & Year
     * Set the Fiscal Year
     * Set the name "FYYY-MM"
     */
    public override void applyDefaults() {
        SLSettings settings = SLSettings.getInstance();
        Integer fyStartMonth    = DateTimeUtils.getFiscalYearStartMonth();

        for (AccountingPeriod__c ap : (List<AccountingPeriod__c>)this.triggerRecords) {
            // Set the period start & end, if the period is not an opening or closing period.
            Integer fYear       = Integer.valueOf(ap.FiscalYear__c);
            Integer fMonth      = Integer.valueOf(ap.FiscalMonth__c);

            if (fMonth > 0 && fMonth < 13) {
                Integer cYear       = DateTimeUtils.getCalendarYear(fYear, fMonth, fyStartMonth);
                Integer cMonth      = DateTimeUtils.getCalendarMonth(fMonth, fyStartMonth);
                ap.PeriodStart__c   = Date.newInstance(cYear, cMonth, 1);
                ap.PeriodEnd__c     = DateTimeUtils.toEndOfMonth(ap.PeriodStart__c);

                if (fMonth == 12 && settings.use13Periods) {
                    ap.PeriodEnd__c     = ap.PeriodEnd__c.addDays(-1);
                }
            } else if (fMonth == 13 && settings.use13Periods) {
                // 13th period is always the last day of the fiscal year
                Integer cYear       = DateTimeUtils.getCalendarYear(fYear, fMonth-1, fyStartMonth);
                Integer cMonth      = DateTimeUtils.getCalendarMonth(fMonth-1, fyStartMonth);
                ap.PeriodStart__c   = Date.newInstance(cYear, cMonth, 1);
                ap.PeriodStart__c   = DateTimeUtils.toEndOfMonth(ap.PeriodStart__c);
                ap.PeriodEnd__c     = ap.PeriodStart__c;
            } else {
                ap.addError('Fiscal Month "' + fMonth + '" is not valid.');
            }

            // Set the Name to FYYY-MM
            ap.Name         = ap.FiscalYear__c + '-' + ap.FiscalMonth__c.leftPad(2, '0');

            // Set the Unique Id to ensure there are no duplicate periods
            ap.UniqueId__c      = ap.Name;
        }
    }

    /**
     * Accounting Period Rules:
     *  - If closed: cannot modify dates
     *
     * @param oldRecordsMap The records before modification
     */
    public override void doValidateUpdate(Map<Id, SObject> oldRecordsMap) {
        for (AccountingPeriod__c apNew : (List<AccountingPeriod__c>)this.triggerRecords) {
            AccountingPeriod__c apOld   = (AccountingPeriod__c)oldRecordsMap.get(apNew.Id);

            if ('Closed'.equalsIgnoreCase(apNew.Status__c)) {
                SObjectUtils.validateNoChange(apOld, apNew, AccountingPeriod__c.FiscalYear__c, Label.AccountingPeriod_NoChange);
                SObjectUtils.validateNoChange(apOld, apNew, AccountingPeriod__c.FiscalMonth__c, Label.AccountingPeriod_NoChange);
            }
        }
    }

    public override void doBeforeUpdate(Map<Id, SObject> oldRecordsMap) {
        applyDefaults();
    }

    public static String getPeriodName(Date d) {
        return String.valueOf(DateTimeUtils.getFiscalYear(d)) + '-' + String.valueOf(DateTimeUtils.getFiscalMonth(d)).leftPad(2, '0');
    }

    public static AccountingPeriod__c newAccountingPeriod(Date periodStart) {
        AccountingPeriod__c ap = new AccountingPeriod__c();
        ap.PeriodStart__c       = periodStart;
        ap.PeriodEnd__c         = DateTimeUtils.toEndOfMonth(ap.PeriodStart__c);
        ap.FiscalYear__c        = String.valueOf(DateTimeUtils.getFiscalYear(periodStart));
        ap.FiscalMonth__c       = String.valueOf(DateTimeUtils.getFiscalMonth(periodStart));
        ap.Name                 = getPeriodName(periodStart);

        return ap;
    }
}