/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 11/21/23.
 */

public with sharing class HeaderProcessorApportioned extends HeaderProcessor {

    @TestVisible
    private final List<Apportion> paymentLineItems  = new List<Apportion>();
    @TestVisible
    private final List<LineItem> lineItems          = new List<LineItem>();
    @TestVisible
    private final List<Payment> payments           = new List<Payment>();
    private final SubledgerDefinitionApportioned.LineItemType lineType;
    private Decimal sourceTotalAmount;
    private Decimal totalPaymentsOnTxDate;

    public HeaderProcessorApportioned(SubledgerDefinitionApportioned definition) {
        super(definition);

        this.lineType           = definition.getLineItemType();
    }

    public HeaderProcessorApportioned(SubledgerDefinitionApportioned definition,
            SObject header,
            List<SObject> sourceRecords,
            List<LedgerEntry__c> existingLedgerEntries) {
        super(definition, header, sourceRecords, existingLedgerEntries);

        this.lineType           = definition.getLineItemType();
    }

    public override void calculate() {
        if (this.paymentLineItems != null && this.paymentLineItems.size() > 0) {
            for (Apportion ra : this.paymentLineItems) {
                ra.applyAmount();
            }
        } else {
            for (LineItem rli : this.lineItems) {
                rli.applyAmount();
            }
            for (Payment rp : this.payments) {
                rp.applyAmount();
            }
        }
    }

    public override void addSourceRecords(List<SObject> sourceRecords) {
        if (sourceRecords != null) {
            // Split the payLineRecords
            for (SObject so : sourceRecords) {
                this.paymentLineItems.add(apportionFromPayLineItemRecord(so, (SubledgerDefinitionApportioned)definition, this));

                this.lineItems.add(lineItemFromPayLineItemRecord(so, (SubledgerDefinitionApportioned)definition, this));
                this.payments.add(paymentFromPayLineItemRecord(so, (SubledgerDefinitionApportioned)definition, this));
            }
        }
    }

    public override List<LedgerEntry__c> getLedgersToDelete() {
        Map<Id,LedgerEntry__c> ledgerEntriesToDelete = new Map<Id,LedgerEntry__c>();
        for (ExistingLedger re : this.existingLedgers) {
            if (re.isPosted != true) {
                ledgerEntriesToDelete.put(re.record.LedgerEntry__c, new LedgerEntry__c(Id=re.record.LedgerEntry__c));
            }
        }
        return ledgerEntriesToDelete.values();
    }

    public override List<Ledger> getLedgersToCreate() {
        getPresentBalances(); // make sure all the lines are applied

        List<LedgerBalance> deltaBalances = new List<LedgerBalance>();

        System.debug('Existing Balances: ' + this.existingBalancesByIds.size());
        System.debug('Present Balances: ' + this.presentBalancesByIds.size());

        Map<String,LedgerAccount__c> allAcctIds = new Map<String,LedgerAccount__c>();
        for (String acctName : this.existingBalancesByIds.keySet()) {
            allAcctIds.put(acctName, this.existingBalancesByIds.get(acctName).account);
        }
        for (String acctName : this.presentBalancesByIds.keySet()) {
            allAcctIds.put(acctName, this.presentBalancesByIds.get(acctName).account);
        }

        for (String acctName : allAcctIds.keySet()) {
            LedgerAccount__c account    = allAcctIds.get(acctName);
            System.debug('Calculating Delta for: ' + account.Name);

            LedgerBalance existingBalance = getExistingLedgerBalance(account);
            LedgerBalance presentBalance = getPresentLedgerBalance(account);

            deltaBalances.add(LedgerBalance.calculateDelta(existingBalance, presentBalance));
        }

        System.debug('Delta Balances: ');
        for (LedgerBalance lb : deltaBalances) {
            System.debug(lb.toString());
        }

        Map<Date,Ledger> entriesByDates = new Map<Date,Ledger>();
        for (LedgerBalance deltaBalance : deltaBalances) {
            deltaBalance.getLedgers(entriesByDates, this.header?.Id, this.definition);
        }

        return entriesByDates.values();
    }

    public override void addExistingLedger(LedgerEntry__c ledgerEntry) {
        System.debug('ledgerEntry.LineItems__r.size(): ' + ledgerEntry.LineItems__r.size());
        for (LedgerEntryLineItem__c leli : ledgerEntry.LineItems__r) {
            addExistingLedgerEntryLineItem(leli);
        }
    }
    public void addExistingLedgerEntryLineItem(LedgerEntryLineItem__c leliWithHeader) {
        LedgerEntryStatus les = LedgerEntryStatus.valueOf(leliWithHeader.LedgerEntry__r.Status__c);
        System.debug('LedgerEntryStatus: ' + les.name());

        Boolean isPosted = false;
        switch on (les) {
            when Approved, CopiedToFinancials, Discarded {
                isPosted = true;
            }
        }

        LedgerAccount__c acct   = (LedgerAccount__c)leliWithHeader.LedgerAccount__r;
        LedgerBalance lBalance  = this.getExistingLedgerBalance(acct);

        this.addExistingLedger(new ExistingLedger(lBalance, leliWithHeader, isPosted));
    }


    public Boolean shouldHaveARorAP() {
        return totalPaymentsOnTxDate() < getSourceTotalAmount();
    }

    public Decimal getSourceTotalAmount() {
        if (this.sourceTotalAmount  == null) {
            this.sourceTotalAmount  = 0;
            for (LineItem rli : this.lineItems) {
                this.sourceTotalAmount += rli.amount;
            }
        }

        return this.sourceTotalAmount;
    }

    /**
     * This method is useful in helping determine whether the header is a receipt vs invoice, or expense vs bill.
     * i.e. It can help identify whether there should/shouldn't be an AR or AP balance.
     *
     * @return The total of all positive, paid payments that occurred on the date of the header's transaction
     */
    public Decimal totalPaymentsOnTxDate() {
        if (this.totalPaymentsOnTxDate == null) {
            this.totalPaymentsOnTxDate = 0;
            for (Payment pl : this.payments) {
                if (pl.paidDate != null && pl.paidDate == this.getHeaderDate() && pl.amount > 0) {
                    this.totalPaymentsOnTxDate += pl.amount;
                }
            }
        }
        return this.totalPaymentsOnTxDate;
    }



    private static Apportion apportionFromPayLineItemRecord(SObject payLineItemRecord, SubledgerDefinitionApportioned definition, HeaderProcessorApportioned header) {
        Decimal amount              = (Decimal)payLineItemRecord.get(definition.getFieldPaymentLineItemAmount());
        Date paidDate               = (Date)payLineItemRecord?.getSObject(definition.getFieldPaymentLineItemPaymentLookup())?.get(definition.getFieldPaymentPaidDate());
        Date writeOffDate           = (Date)payLineItemRecord?.getSObject(definition.getFieldPaymentLineItemPaymentLookup())?.get(definition.getFieldPaymentWriteoffDate());
        Date revExpDate             = header.getHeaderDate();

        LedgerBalance cashWOAccount         = header.getPresentLedgerBalance((LedgerAccount__c)payLineItemRecord?.getSObject(definition.getFieldPaymentLineItemCashOrWOAcctLookup()));
        LedgerBalance arOrApAccount         = header.getPresentLedgerBalance((LedgerAccount__c)payLineItemRecord?.getSObject(definition.getFieldPaymentLineItemARorAPAcctLookup()));
        LedgerBalance revOrExpAccount       = header.getPresentLedgerBalance((LedgerAccount__c)payLineItemRecord?.getSObject(definition.getFieldPaymentLineItemRevOrExpAcctLookup()));

        return new Apportion(payLineItemRecord.Id, header, amount, paidDate, writeOffDate, revExpDate, cashWOAccount, arOrApAccount, revOrExpAccount);
    }
    private static LineItem lineItemFromPayLineItemRecord(SObject payLineItemRecord, SubledgerDefinitionApportioned definition, HeaderProcessorApportioned header) {
        Decimal amount      = (Decimal)payLineItemRecord.get(definition.getFieldPaymentLineItemAmount());

        LedgerBalance arOrAPAccountBalance         = header.getPresentLedgerBalance((LedgerAccount__c)payLineItemRecord?.getSObject(definition.getFieldPaymentLineItemARorAPAcctLookup()));
        LedgerBalance revOrExpAccountBalance       = header.getPresentLedgerBalance((LedgerAccount__c)payLineItemRecord?.getSObject(definition.getFieldPaymentLineItemRevOrExpAcctLookup()));

        return new LineItem(header, arOrAPAccountBalance, revOrExpAccountBalance, amount);
    }
    private static Payment paymentFromPayLineItemRecord(SObject payLineItemRecord, SubledgerDefinitionApportioned definition, HeaderProcessorApportioned header) {
        Decimal amount              = (Decimal)payLineItemRecord.get(definition.getFieldPaymentLineItemAmount());
        SObject payRec              = payLineItemRecord.getSObject(definition.getFieldPaymentLineItemPaymentLookup());
        Date paidDate               = (Date)payRec?.get(definition.getFieldPaymentPaidDate());
        Date writeOffDate           = (Date)payRec?.get(definition.getFieldPaymentWriteoffDate());

        LedgerBalance cashWOAccountBalance         = header.getPresentLedgerBalance((LedgerAccount__c)payLineItemRecord?.getSObject(definition.getFieldPaymentLineItemCashOrWOAcctLookup()));
        LedgerBalance arOrAPAccountBalance         = header.getPresentLedgerBalance((LedgerAccount__c)payLineItemRecord?.getSObject(definition.getFieldPaymentLineItemARorAPAcctLookup()));
        LedgerBalance revOrExpAccountBalance       = header.getPresentLedgerBalance((LedgerAccount__c)payLineItemRecord?.getSObject(definition.getFieldPaymentLineItemRevOrExpAcctLookup()));

        return new Payment(payLineItemRecord.Id, header, amount, paidDate, writeOffDate, cashWOAccountBalance, arOrAPAccountBalance, revOrExpAccountBalance);
    }

    @TestVisible
    private class Apportion {
        private final Id srcId;
        private final HeaderProcessorApportioned header;
        private final Decimal amount;
        private final Date paidDate;
        private final Date writeOffDate;
        private final Date revExpDate;
        private final LedgerBalance cashWOAccount;
        private final LedgerBalance arOrApAccount;
        private final LedgerBalance revOrExpAccount;
        private Boolean isApplied;

        @TestVisible
        private Apportion(Id srcId, HeaderProcessorApportioned header, Decimal amount, Date paidDate, Date writeOffDate, Date revExpDate, LedgerBalance cashWOAccount, LedgerBalance arOrApAccount, LedgerBalance revOrExpAccount) {
            Assert.paramRequired(header, 'header');
            Assert.paramRequired(amount, 'amount');

            System.debug(paidDate != null);
            System.debug(writeOffDate != null);
            System.debug(cashWOAccount == null);

            Assert.assert(!((paidDate != null || writeOffDate != null) && cashWOAccount == null), 'A payment account is required if the payment has a paid or write-off date.');
            Assert.assert(!(paidDate != null && writeOffDate != null), 'A payment cannot be both paid and written off.');

            this.srcId = srcId;
            this.header = header;
            this.amount = amount;
            this.paidDate = paidDate;
            this.writeOffDate = writeOffDate;
            this.revExpDate = revExpDate;
            this.cashWOAccount = cashWOAccount;
            this.arOrApAccount = arOrApAccount;
            this.revOrExpAccount = revOrExpAccount;

            this.isApplied              = false;
        }

        private void applyAmount() {
            if (this.amount == 0) {
                // don't transact $0
                return;
            }
            if (this.isApplied) {
                return; // Only apply this line item 1 time.
            }
            this.isApplied = true;

            // Determine if it's a payment or a refund
            Decimal amountToTx  = Math.abs(this.amount);
            Boolean isRefund    = this.amount < 0;

            TransactionType arOrApTxType    = this.header.lineType == SubledgerDefinitionApportioned.LineItemType.Revenue ? TransactionType.Debit : TransactionType.Credit;
            TransactionType revOrExpTxType  = this.header.lineType == SubledgerDefinitionApportioned.LineItemType.Revenue ? TransactionType.Credit : TransactionType.Debit;
            TransactionType cashTxType;

            Date paidOrWrittenOffDate = this.paidDate != null ? this.paidDate : this.writeOffDate;

            if (isRefund) {
                /*
                 This is a refund:
                 If this is a revenue transaction, we credit cash
                 If this is an expense transaction, we debit cash
                 */
                cashTxType  = this.header.lineType == SubledgerDefinitionApportioned.LineItemType.Revenue ? TransactionType.Credit : TransactionType.Debit;

                /*
                 For refunds, we create the following TX:
                 Dr: Rev/Exp
                  Cr: Cash
                 */
                this.revOrExpAccount?.applyAmount(cashTxType == TransactionType.Debit ? TransactionType.Credit : TransactionType.Debit, amountToTx, paidOrWrittenOffDate, false);
                this.cashWOAccount?.applyAmount(cashTxType, amountToTx, paidOrWrittenOffDate, false);
            } else {
                /*
                 This is a payment:
                 If this is a revenue transaction, we debit cash
                 If this is an expense transaction, we credit cash
                 */
                cashTxType  = this.header.lineType == SubledgerDefinitionApportioned.LineItemType.Revenue ? TransactionType.Debit : TransactionType.Credit;

                if (this.header.shouldHaveARorAP()) {
                    /*
                     If the header should have AR/AP, create the following TXs
                         TX1: on the rev date
                          Dr: AR/AP
                          Cr: rev/exp account

                         TX2: on the payment date
                          Dr: The cash account
                          Cr: AR/AP

                     ELSE, there's no AR/AP, create the following TXs

                         TX1: on the rev date
                          Dr: Cash
                          Cr: rev/exp account
                   */
                    // This header has AR/AP, create receivables/payables

                    // TX1
                    this.arOrApAccount.applyAmount(arOrApTxType, this.amount, this.revExpDate, false);
                    this.revOrExpAccount.applyAmount(revOrExpTxType, this.amount, this.revExpDate, false);

                    // TX2
                    if (paidOrWrittenOffDate != null) {
                        this.cashWOAccount?.applyAmount(cashTxType, amountToTx, paidOrWrittenOffDate, false);
                        this.arOrApAccount.applyAmount(revOrExpTxType, this.amount, paidOrWrittenOffDate, false);
                    }
                } else {
                    // No AR/AP, just transact cash and revenue.

                    // TX1
                    if (paidOrWrittenOffDate != null) {
                        this.cashWOAccount?.applyAmount(cashTxType, amountToTx, paidOrWrittenOffDate, false);
                        this.revOrExpAccount.applyAmount(revOrExpTxType, this.amount, paidOrWrittenOffDate, false);
                    }
                }
            }
        }
    }
    @TestVisible
    private class LineItem {
        private final HeaderProcessorApportioned header;
        private final LedgerBalance arOrApAccountBalance;
        private final LedgerBalance revOrExpAccountBalance;
        private final Decimal amount;
        private Boolean isApplied;

        @TestVisible
        private LineItem(HeaderProcessorApportioned header, LedgerBalance arOrApAccountBalance, LedgerBalance revOrExpAccountBalance, Decimal amount) {
            Assert.paramRequired(header, 'header');
            Assert.paramRequired(arOrApAccountBalance, 'AR/AP Account');
            Assert.paramRequired(revOrExpAccountBalance, 'Rev/Exp Account');
            Assert.paramRequired(amount, 'amount');

            this.header                 = header;
            this.arOrApAccountBalance   = arOrApAccountBalance;
            this.revOrExpAccountBalance = revOrExpAccountBalance;
            this.amount                 = amount;

            this.isApplied              = false;
        }

        private void applyAmount() {
            if (this.amount == 0) {
                return; // don't transact $0
            }
            if (!(this.isApplied ^= true)) {
                return; // Only apply this line item 1 time.
            }

            // Apply balance to AR/AP if applicable
            if (this.header.shouldHaveARorAP()) {
                TransactionType arOrApTxType    = this.header.lineType == SubledgerDefinitionApportioned.LineItemType.Revenue ? TransactionType.Debit : TransactionType.Credit;
                this.arOrApAccountBalance.applyAmount(arOrApTxType, this.amount, this.header.getHeaderDate(), false);
            }

            TransactionType revOrExpTxType  = this.header.lineType == SubledgerDefinitionApportioned.LineItemType.Revenue ? TransactionType.Credit : TransactionType.Debit;
            this.revOrExpAccountBalance.applyAmount(revOrExpTxType, this.amount, this.header.getHeaderDate(), false);
        }
    }
    @TestVisible
    private class Payment {
        private final Id srcId;
        private final HeaderProcessorApportioned header;
        private final Decimal amount;
        private final Date paidDate;
        private final Date writeOffDate;
        private final LedgerBalance cashWOAccount;
        private final LedgerBalance arOrApAccountBalance;
        private final LedgerBalance revOrExpAccount;
        private Boolean isApplied;

        @TestVisible
        private Payment(Id srcId, HeaderProcessorApportioned header, Decimal amount, Date paidDate, Date writeOffDate, LedgerBalance cashWOAccount, LedgerBalance arOrApAccountBalance, LedgerBalance revOrExpAccount) {
            Assert.paramRequired(header, 'header');
            Assert.paramRequired(amount, 'amount');

            System.debug(srcId);
            System.debug(paidDate);
            System.debug(writeOffDate);
            System.debug(cashWOAccount);

            Assert.assert(!((paidDate != null || writeOffDate != null) && cashWOAccount == null), 'A payment account is required if the payment has a paid or write-off date.');
            Assert.assert(!(paidDate != null && writeOffDate != null), 'A payment cannot be both paid and written off.');

            this.srcId = srcId;
            this.header = header;
            this.amount = amount;
            this.paidDate = paidDate;
            this.writeOffDate = writeOffDate;
            this.cashWOAccount = cashWOAccount;
            this.arOrApAccountBalance = arOrApAccountBalance;
            this.revOrExpAccount = revOrExpAccount;

            this.isApplied              = false;
        }

        private void applyAmount() {
            if (this.amount == 0) {
                // don't transact $0
                return;
            }
            if (!(this.isApplied ^= true)) {
                return; // Only apply this line item 1 time.
            }

            TransactionType lit;
            Decimal amountToTx  = Math.abs(this.amount);
            if (this.amount > 0) {
                // This is a payment
                lit  = this.header.lineType == SubledgerDefinitionApportioned.LineItemType.Revenue ? TransactionType.Debit : TransactionType.Credit;
            } else {
                // This is a refund
                lit  = this.header.lineType == SubledgerDefinitionApportioned.LineItemType.Revenue ? TransactionType.Credit : TransactionType.Debit;
            }

            if (this.paidDate != null || this.writeOffDate != null) {
                Date d = this.paidDate != null ? this.paidDate : this.writeOffDate;
                this.cashWOAccount?.applyAmount(lit, amountToTx, d, false);

                if (this.amount < 0) {
                    // This is a refund. reverse the revenue / expense
                    if (this.revOrExpAccount != null) {
                        this.revOrExpAccount?.applyAmount(lit == TransactionType.Debit ? TransactionType.Credit : TransactionType.Debit, amountToTx, d, false);
                    } else {
                        // todo: apportion the refund to all of the header's line items' accounts.
                    }
                } else if (this.header.shouldHaveARorAP()) {
                    this.arOrApAccountBalance?.applyAmount(lit == TransactionType.Debit ? TransactionType.Credit : TransactionType.Debit, amountToTx, d, false);
                }
            }
        }
    }
}