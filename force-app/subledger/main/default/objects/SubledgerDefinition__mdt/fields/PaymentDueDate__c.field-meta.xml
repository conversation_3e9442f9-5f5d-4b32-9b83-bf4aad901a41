<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>PaymentDueDate__c</fullName>
    <description>The field on the child payment object that indicates the date the payment is scheduled/due to be paid. This is used to distinguish between current and long-term receivables.</description>
    <fieldManageability>SubscriberControlled</fieldManageability>
    <inlineHelpText>The field on the child payment object that indicates the date the payment is scheduled/due to be paid. This is used to distinguish between current and long-term receivables.</inlineHelpText>
    <label>Payment: Due Date</label>
    <metadataRelationshipControllingField>SubledgerDefinition__mdt.PaymentObject__c</metadataRelationshipControllingField>
    <referenceTo>FieldDefinition</referenceTo>
    <relationshipLabel>SubledgerDefinitions_PayDueDate</relationshipLabel>
    <relationshipName>SubledgerDefinitions_PayDueDate</relationshipName>
    <required>false</required>
    <type>MetadataRelationship</type>
    <unique>false</unique>
</CustomField>
