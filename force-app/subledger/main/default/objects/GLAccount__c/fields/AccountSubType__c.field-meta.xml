<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>AccountSubType__c</fullName>
    <label>Account Sub Type</label>
    <required>true</required>
    <trackHistory>false</trackHistory>
    <trackTrending>false</trackTrending>
    <type>Picklist</type>
    <valueSet>
        <controllingField>AccountType__c</controllingField>
        <restricted>true</restricted>
        <valueSetName>AccountSubType</valueSetName>
        <valueSettings>
            <controllingFieldValue>Asset</controllingFieldValue>
            <valueName>BankAccount</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Asset</controllingFieldValue>
            <valueName>AR_Current</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Asset</controllingFieldValue>
            <valueName>AR_LongTerm</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Asset</controllingFieldValue>
            <valueName>OtherAsset</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Liability</controllingFieldValue>
            <valueName>OtherLiability</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Equity</controllingFieldValue>
            <valueName>OtherEquity</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Income</controllingFieldValue>
            <valueName>DeferredRevenue</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Income</controllingFieldValue>
            <valueName>EarnedRevenue</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Income</controllingFieldValue>
            <valueName>OtherIncome</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Expense</controllingFieldValue>
            <valueName>Expense</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Expense</controllingFieldValue>
            <valueName>WriteOff</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Expense</controllingFieldValue>
            <valueName>OtherExpense</valueName>
        </valueSettings>
    </valueSet>
</CustomField>
