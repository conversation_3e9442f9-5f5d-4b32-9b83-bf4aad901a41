/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 5/31/23.
 */
pre {
    border: 1px solid #ccc;
    border-radius: 4pt;
    padding: 5px;
    background-color: #eee;
}


:host {
    --header-bg-odd: #78b0fd;
    --tx-bg-odd: #eef4ff;
    --footer-bg-odd: #d8e6fe;

    --header-bg-even: #aeaeae;
    --tx-bg-even: #f3f3f3;
    --footer-bg-even: #e5e5e5;

    --column-spacing: 10px;
    --column-bg-color: #FFF;
}
table {
    background-color: #FFFFFF;
}
.date {
    font-style: italic;
}
.label {
    text-align: right;
}
.acctHeader.odd {
    background-color: var(--header-bg-odd);
}
.acctHeader.even {
    background-color: var(--header-bg-even);
}
.tx.odd {
    background-color: var(--tx-bg-odd);
}
.tx.even {
    background-color: var(--tx-bg-even);
}
.acctFooter.odd {
    background-color: var(--footer-bg-odd);
}
.acctFooter.even {
    background-color: var(--footer-bg-even);
}
.acctHeader.title,.acctHeader.credit,.tx.credit,.acctFooter.credit,.acctFooter.title {
    border-right: var(--column-spacing) solid var(--column-bg-color);
}
th.acct,td.acct {
    padding: 15px;
}
.acctHeader {
    color: #FFFFFF;
    text-align: center;
}
.acctHeader.debit,.acctHeader.credit {
    text-align: center;
}
.debit,.credit {
    text-align: right;
}
.title {
    padding: 15px;
    font-size: .9rem;
    min-width: 225px;
    text-align: center;
}
h4 {
    font-size: 1.1rem;
    font-weight: 100 !important;
    line-height: 1.3em;
}
.tx {
    line-height: 1rem;
}
.acctFooter {
    font-size: .9rem;
    line-height: 1rem;
}
.balance {
    font-size: 1rem;
    font-weight: 500;
}
.posted {
    position: relative;
    overflow: hidden;
}
.posted:after {
    content: "";
    position: absolute;
    margin-left: -100%;
    width: 0px;
    height: 0px;
    transform: rotate(45deg);
    /* background-color: #F00; */
    box-shadow: 0 0 0 5px #F00;
}
.legend {
    width: 275px;
    text-align: right;
}