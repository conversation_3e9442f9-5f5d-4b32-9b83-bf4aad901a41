<!--
  ~ Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
  ~
  ~ The code below is part of the Foglight Arts & Culture package and is not
  ~ to be redistributed without express written consent by Foglight.
  -->

<!--
 - Created by edwardblazer on 5/31/23.
 -->

<!-- T-Account Balance Report -->
<template>
    <lightning-quick-action-panel header="T-Account Balances">
        <div slot="footer">
            <table style="background-color: transparent;">
                <tr>
                    <td>
                        <div class="posted legend">Transaction Approved / Copied to Financials</div>
                    </td>
                    <td>
                        <div style="text-align: right;">
                            <lightning-button variant="brand" label="Ok" onclick={handleClose} class="slds-m-left_x-small" aria-haspopup="modal" disabled={saveDisabled}></lightning-button>
                        </div>
                    </td>
                </tr>
            </table>
        </div>
        <template if:true={hasRenderedCallback}>
            <div style="display: none">{recordId}</div>

            <template if:true={doneLoading}>
                <div style="overflow-x: auto">
                    <table>
                        <thead>
                            <tr>
                                <th>&nbsp;</th>
                                <!-- loop over accounts -->
                                <template for:each={accounts} for:item="acct">
                                    <th colspan=2 key={acct.idx} class={acct.styleHeader}><h4>{acct.account.Name}</h4></th>
                                </template>
                            </tr>
                            <tr>
                                <th>&nbsp;</th>
                                <!-- loop over accounts -->
                                <template for:each={accounts} for:item="acct">
                                    <th key={acct.idx} class={acct.styleHeaderDebit}>Debit</th>
                                    <th key={acct.idx} class={acct.styleHeaderCredit}>Credit</th>
                                </template>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- loop over dates-->
                            <template for:each={txsByDates} for:item="d">
                            <tr key={d.idx}>
                                <td class="acct tx date label"><lightning-formatted-date-time value={d.date}
                                                                   year="numeric"
                                                                   month="numeric"
                                                                   day="numeric"></lightning-formatted-date-time></td>
                                <!-- loop over account-->
                                <template for:each={d.accts} for:item="acct">
                                    <td key={acct.idx} class={acct.styleTxDebit}>
                                        <template for:each={acct.debits} for:item="dr">
                                            <div key={dr.amount}>
                                                <template lwc:if={dr.isPosted}><div class="posted"><lightning-formatted-number value={dr.amount} format-style="currency"></lightning-formatted-number></div></template>
                                                <template lwc:else><lightning-formatted-number value={dr.amount} format-style="currency"></lightning-formatted-number></template>
                                            </div>
                                        </template>
                                    </td>
                                    <td key={acct.idx} class={acct.styleTxCredit}>
                                        <template for:each={acct.credits} for:item="cr">
                                            <div key={cr.amount}>
                                                <template lwc:if={cr.isPosted}><div class="posted"><lightning-formatted-number value={cr.amount} format-style="currency"></lightning-formatted-number></div></template>
                                                <template lwc:else><lightning-formatted-number value={cr.amount} format-style="currency"></lightning-formatted-number></template>
                                            </div>
                                        </template>
                                    </td>
                                </template>
                            </tr>
                            </template>
                        </tbody>
                        <tfoot>
                            <tr>
                                <td class="acct acctFooter label">Totals</td>
                                <!-- loop over accounts -->
                                <template for:each={accounts} for:item="acct">
                                    <td key={acct.idx} class={acct.styleFooterDebit}><lightning-formatted-number value={acct.totalDebits} format-style="currency"></lightning-formatted-number></td>
                                    <td key={acct.idx} class={acct.styleFooterCredit}><lightning-formatted-number value={acct.totalCredits} format-style="currency"></lightning-formatted-number></td>
                                </template>
                            </tr>
                            <tr>
                                <td class="acct acctFooter label">Balance</td>
                                <!-- loop over accounts -->
                                <template for:each={accounts} for:item="acct">
                                    <template lwc:if={acct.balanceDebit}>
                                        <td key={acct.idx} class={acct.styleFooterBalanceDebit}><lightning-formatted-number value={acct.balance} format-style="currency"></lightning-formatted-number></td>
                                        <td key={acct.idx} class={acct.styleFooterBalanceCredit}></td>
                                    </template>
                                    <template lwc:elseif={acct.balanceCredit}>
                                        <td key={acct.idx} class={acct.styleFooterBalanceDebit}></td>
                                        <td key={acct.idx} class={acct.styleFooterBalanceCredit}><lightning-formatted-number value={acct.balance} format-style="currency"></lightning-formatted-number></td>
                                    </template>
                                    <template lwc:else>
                                        <td key={acct.idx} colspan="2" class={acct.styleFooter}><lightning-formatted-number value={acct.balance} format-style="currency"></lightning-formatted-number></td>
                                    </template>
                                </template>
                            </tr>
                        </tfoot>
                    </table>
                </div>

                <!--

                <p></p>
                <p></p>
                <p></p>

                <template for:each={balances} for:item="balance">
                    <pre key={balance.id}>{balance.balance}</pre>
                </template>-->
            </template>
            <template if:false={doneLoading}>
                <lightning-spinner alternative-text="Loading..." variant="brand" size="medium" class="slds-is-static"></lightning-spinner>
            </template>
        </template>
    </lightning-quick-action-panel>
</template>