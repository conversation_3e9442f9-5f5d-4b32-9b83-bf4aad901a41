/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 11/21/23.
 */

@IsTest
private class HeaderProcessorApportionedTest {
    @TestSetup
    static void setup(){
        SLSettings settings = SLSettings.getInstance();
        settings.isEnabled = true;
        settings.saveSettings();
    }

    @IsTest
    static void miscCoverage_constructor() {
        HeaderProcessorTest.misc_coverage_constructor(getDefinition());
    }

    @IsTest
    static void nonapportion_sale_refund() {
        SubledgerDefinitionApportioned def = getDefinition();

        LedgerAccount__c laAR   = LedgerBalanceTest.mockLedgerAccount(AccountSubType.AR_Current, 'A/R');
        LedgerAccount__c laRev  = LedgerBalanceTest.mockLedgerAccount(AccountSubType.DeferredRevenue, 'Deferred Revenue');
        LedgerAccount__c laCash = LedgerBalanceTest.mockLedgerAccount(AccountSubType.BankAccount, 'Cash');
        LedgerAccount__c laWO   = LedgerBalanceTest.mockLedgerAccount(AccountSubType.WriteOff, 'Bad Debts');

        LedgerBalance lbAR     = LedgerBalanceTest.mockLedgerBalance(laAR);
        LedgerBalance lbWO     = LedgerBalanceTest.mockLedgerBalance(laWO);
        LedgerBalance lbRev     = LedgerBalanceTest.mockLedgerBalance(laRev);
        LedgerBalance lbCash    = LedgerBalanceTest.mockLedgerBalance(laCash);

        HeaderProcessorApportioned hdr = new HeaderProcessorApportioned(def);
        hdr.presentBalancesByIds.put(lbRev.account.Id, lbRev);
        hdr.presentBalancesByIds.put(lbCash.account.Id, lbCash);
        hdr.presentBalancesByIds.put(lbAR.account.Id, lbAR);
        hdr.presentBalancesByIds.put(lbWO.account.Id, lbWO);

        hdr.headerDate = Date.newInstance(2023, 3, 15);
        hdr.lineItems.add(new HeaderProcessorApportioned.LineItem(hdr, lbAR, lbRev, 200));
        hdr.payments.add(new HeaderProcessorApportioned.Payment(
                'pa0000000000000'
                , hdr
                , 200
                , Date.newInstance(2023, 3, 15)
                , null
                , lbCash
                , lbAR
                , lbRev
        ));
        hdr.payments.add(new HeaderProcessorApportioned.Payment(
                'pa0000000000001'
                , hdr
                , -200
                , Date.newInstance(2023, 3, 15)
                , null
                , lbCash
                , lbAR
                , lbRev
        ));

        hdr.calculate();
        hdr.getPresentBalances();

        System.debug(lbAR.toString());
        System.debug(lbRev.toString());
        System.debug(lbCash.toString());
        System.debug(lbWO.toString());

        System.assertEquals(0, lbRev.getBalance());
        System.assertEquals(200, lbRev.getTotalDebits());
        System.assertEquals(200, lbRev.getTotalCredits());
        System.assertEquals(0, lbCash.getBalance());
        System.assertEquals(200, lbCash.getTotalDebits());
        System.assertEquals(200, lbCash.getTotalCredits());
        System.assertEquals(0, lbAR.getBalance());
        System.assertEquals(0, lbAR.getTotalDebits());
        System.assertEquals(0, lbAR.getTotalCredits());
        System.assertEquals(0, lbWO.getBalance());
        System.assertEquals(0, lbWO.getTotalDebits());
        System.assertEquals(0, lbWO.getTotalCredits());
    }

    @IsTest
    static void nonapportion_invoice() {
        SubledgerDefinitionApportioned def = getDefinition();

        LedgerAccount__c laRev  = LedgerBalanceTest.mockLedgerAccount(AccountSubType.DeferredRevenue, 'Deferred Revenue');
        LedgerAccount__c laAR   = LedgerBalanceTest.mockLedgerAccount(AccountSubType.AR_Current, 'A/R');
        LedgerAccount__c laCash = LedgerBalanceTest.mockLedgerAccount(AccountSubType.BankAccount, 'Cash');
        LedgerAccount__c laWO   = LedgerBalanceTest.mockLedgerAccount(AccountSubType.WriteOff, 'Bad Debts');

        HeaderProcessorApportioned hdr = new HeaderProcessorApportioned(def);
        hdr.headerDate = Date.newInstance(2023, 3, 15);

        LedgerBalance lbAR      = hdr.getPresentLedgerBalance(laAR);
        LedgerBalance lbWO      = hdr.getPresentLedgerBalance(laWO);
        LedgerBalance lbRev     = hdr.getPresentLedgerBalance(laRev);
        LedgerBalance lbCash    = hdr.getPresentLedgerBalance(laCash);

        hdr.lineItems.add(new HeaderProcessorApportioned.LineItem(hdr, lbAR, lbRev, 200));
        hdr.lineItems.add(new HeaderProcessorApportioned.LineItem(hdr, lbAR, lbRev, 300));
        hdr.payments.add(new HeaderProcessorApportioned.Payment(
                'pa0000000000000'
                , hdr
                , 250
                , Date.newInstance(2024, 3, 31)
                , null
                , lbCash
                , lbAR
                , lbRev
        ));
        hdr.payments.add(new HeaderProcessorApportioned.Payment(
                'pa0000000000001'
                , hdr
                , 250
                , null
                , Date.newInstance(2024, 3, 31)
                , lbWO
                , lbAR
                , lbRev
        ));

        hdr.calculate();
        hdr.getPresentBalances();

        System.debug(lbAR.toString());
        System.debug(lbRev.toString());
        System.debug(lbCash.toString());
        System.debug(lbWO.toString());

        System.assertEquals(0, lbAR.getBalance());
        System.assertEquals(500, lbAR.getTotalDebits());
        System.assertEquals(500, lbAR.getTotalCredits());
        System.assertEquals(500, lbRev.getBalance());
        System.assertEquals(250, lbCash.getBalance());
        System.assertEquals(250, lbWO.getBalance());

        System.debug(JSON.serializePretty(hdr.getLedgersToCreate()));
    }

    @IsTest
    static void nonapportion_invoice_refund() {
        SubledgerDefinitionApportioned def = getDefinition();

        LedgerAccount__c laAR   = LedgerBalanceTest.mockLedgerAccount(AccountSubType.AR_Current, 'A/R');
        LedgerAccount__c laRev  = LedgerBalanceTest.mockLedgerAccount(AccountSubType.DeferredRevenue, 'Deferred Revenue');
        LedgerAccount__c laCash = LedgerBalanceTest.mockLedgerAccount(AccountSubType.BankAccount, 'Cash');
        LedgerAccount__c laWO   = LedgerBalanceTest.mockLedgerAccount(AccountSubType.WriteOff, 'Bad Debts');

        LedgerBalance lbAR     = LedgerBalanceTest.mockLedgerBalance(laAR);
        LedgerBalance lbWO     = LedgerBalanceTest.mockLedgerBalance(laWO);
        LedgerBalance lbRev     = LedgerBalanceTest.mockLedgerBalance(laRev);
        LedgerBalance lbCash    = LedgerBalanceTest.mockLedgerBalance(laCash);

        HeaderProcessorApportioned hdr = new HeaderProcessorApportioned(def);
        hdr.presentBalancesByIds.put(lbRev.account.Id, lbRev);
        hdr.presentBalancesByIds.put(lbCash.account.Id, lbCash);
        hdr.presentBalancesByIds.put(lbAR.account.Id, lbAR);
        hdr.presentBalancesByIds.put(lbWO.account.Id, lbWO);
        hdr.headerDate          = Date.newInstance(2023,3,15);

        hdr.lineItems.add(new HeaderProcessorApportioned.LineItem(hdr, lbAR, lbRev, 200));
        hdr.payments.add(new HeaderProcessorApportioned.Payment(
                'pa0000000000000'
                , hdr
                , 200
                , Date.newInstance(2023, 3, 16)
                , null
                , lbCash
                , lbAR
                , lbRev
        ));
        hdr.payments.add(new HeaderProcessorApportioned.Payment(
                'pa0000000000001'
                , hdr
                , -200
                , Date.newInstance(2023, 3, 16)
                , null
                , lbCash
                , lbAR
                , lbRev
        ));

        hdr.calculate();
        hdr.getPresentBalances();

        System.debug(lbAR.toString());
        System.debug(lbRev.toString());
        System.debug(lbCash.toString());
        System.debug(lbWO.toString());

        System.assertEquals(0, lbRev.getBalance());
        System.assertEquals(200, lbRev.getTotalDebits());
        System.assertEquals(200, lbRev.getTotalCredits());
        System.assertEquals(0, lbCash.getBalance());
        System.assertEquals(200, lbCash.getTotalDebits());
        System.assertEquals(200, lbCash.getTotalCredits());
        System.assertEquals(0, lbAR.getBalance());
        System.assertEquals(200, lbAR.getTotalDebits());
        System.assertEquals(200, lbAR.getTotalCredits());
        System.assertEquals(0, lbWO.getBalance());
        System.assertEquals(0, lbWO.getTotalDebits());
        System.assertEquals(0, lbWO.getTotalCredits());
    }

    /**
     * A simple apportioned sale with no existing line items
     */
    @IsTest
    static void apportion_sale() {
        SubledgerDefinitionApportioned def = getDefinition();

        LedgerAccount__c laAR   = LedgerBalanceTest.mockLedgerAccount(AccountSubType.AR_Current, 'A/R');
        LedgerAccount__c laRev  = LedgerBalanceTest.mockLedgerAccount(AccountSubType.DeferredRevenue, 'Deferred Revenue');
        LedgerAccount__c laCash = LedgerBalanceTest.mockLedgerAccount(AccountSubType.BankAccount, 'Cash');
        LedgerAccount__c laWO   = LedgerBalanceTest.mockLedgerAccount(AccountSubType.WriteOff, 'Bad Debts');

        Date txDate             = Date.newInstance(2023,3,15);

        HeaderProcessorApportioned hdr = new HeaderProcessorApportioned(def);
        LedgerBalance lbAR      = hdr.getPresentLedgerBalance(laAR);
        LedgerBalance lbWO      = hdr.getPresentLedgerBalance(laWO);
        LedgerBalance lbRev     = hdr.getPresentLedgerBalance(laRev);
        LedgerBalance lbCash    = hdr.getPresentLedgerBalance(laCash);
        hdr.headerDate          = txDate;

        hdr.paymentLineItems.add(new HeaderProcessorApportioned.Apportion(
                'pa0000000000000'
                , hdr
                , 200
                , txDate
                , null
                , txDate
                , lbCash
                , lbAR
                , lbRev
        ));
        /*hdr.lineItems.add(new HeaderProcessorApportioned.LineItem(hdr, lbAR, lbRev, 200));
        hdr.payments.add(new HeaderProcessorApportioned.Payment(
                'pa0000000000000'
                , hdr
                , 200
                , txDate
                , null
                , lbCash
                , lbAR
                , lbRev
        ));*/

        hdr.calculate();
        hdr.getPresentBalances();

        System.debug(lbAR.toString());
        System.debug(lbRev.toString());
        System.debug(lbCash.toString());
        System.debug(lbWO.toString());

        System.assertEquals(200, lbRev.getBalance());
        System.assertEquals(0, lbRev.getTotalDebits());
        System.assertEquals(200, lbRev.getTotalCredits());
        System.assertEquals(200, lbCash.getBalance());
        System.assertEquals(200, lbCash.getTotalDebits());
        System.assertEquals(0, lbCash.getTotalCredits());
        System.assertEquals(0, lbAR.getBalance());
        System.assertEquals(0, lbAR.getTotalDebits());
        System.assertEquals(0, lbAR.getTotalCredits());
        System.assertEquals(0, lbWO.getBalance());
        System.assertEquals(0, lbWO.getTotalDebits());
        System.assertEquals(0, lbWO.getTotalCredits());
    }

    /**
     * A simple apportioned sale with no existing line items
     */
    @IsTest
    static void apportion_sale_refund() {
        SubledgerDefinitionApportioned def = getDefinition();

        LedgerAccount__c laAR   = LedgerBalanceTest.mockLedgerAccount(AccountSubType.AR_Current, 'A/R');
        LedgerAccount__c laRev  = LedgerBalanceTest.mockLedgerAccount(AccountSubType.DeferredRevenue, 'Deferred Revenue');
        LedgerAccount__c laCash = LedgerBalanceTest.mockLedgerAccount(AccountSubType.BankAccount, 'Cash');
        LedgerAccount__c laWO   = LedgerBalanceTest.mockLedgerAccount(AccountSubType.WriteOff, 'Bad Debts');

        Date txDate             = Date.newInstance(2023,3,15);

        HeaderProcessorApportioned hdr = new HeaderProcessorApportioned(def);
        LedgerBalance lbAR      = hdr.getPresentLedgerBalance(laAR);
        LedgerBalance lbWO      = hdr.getPresentLedgerBalance(laWO);
        LedgerBalance lbRev     = hdr.getPresentLedgerBalance(laRev);
        LedgerBalance lbCash    = hdr.getPresentLedgerBalance(laCash);
        hdr.headerDate          = txDate;

        hdr.paymentLineItems.add(new HeaderProcessorApportioned.Apportion(
                'pa0000000000000'
                , hdr
                , 200
                , txDate
                , null
                , txDate
                , lbCash
                , lbAR
                , lbRev
        ));

        hdr.paymentLineItems.add(new HeaderProcessorApportioned.Apportion(
                'pa0000000000000'
                , hdr
                , -200
                , txDate
                , null
                , txDate
                , lbCash
                , lbAR
                , lbRev
        ));
        /*hdr.lineItems.add(new HeaderProcessorApportioned.LineItem(hdr, lbAR, lbRev, 200));
        hdr.payments.add(new HeaderProcessorApportioned.Payment(
                'pa0000000000000'
                , hdr
                , 200
                , txDate
                , null
                , lbCash
                , lbAR
                , lbRev
        ));*/

        hdr.calculate();
        hdr.getPresentBalances();

        System.debug(lbAR.toString());
        System.debug(lbRev.toString());
        System.debug(lbCash.toString());
        System.debug(lbWO.toString());

        System.assertEquals(0, lbRev.getBalance());
        System.assertEquals(200, lbRev.getTotalDebits());
        System.assertEquals(200, lbRev.getTotalCredits());
        System.assertEquals(0, lbCash.getBalance());
        System.assertEquals(200, lbCash.getTotalDebits());
        System.assertEquals(200, lbCash.getTotalCredits());
        System.assertEquals(0, lbAR.getBalance());
        System.assertEquals(0, lbAR.getTotalDebits());
        System.assertEquals(0, lbAR.getTotalCredits());
        System.assertEquals(0, lbWO.getBalance());
        System.assertEquals(0, lbWO.getTotalDebits());
        System.assertEquals(0, lbWO.getTotalCredits());
    }

    /**
     * test that revenue is transferred from Rev1 to Rev2
     *
     * Should result in:
     *  Dr: Cash (deleted)
     *   Cr: Rev1 (deleted)
     *  Dr: Cash (new)
     *   Cr: Rev2 (new)
     */
    @IsTest
    static void apportion_sale_revenue_transfer_unposted() {
        SubledgerDefinitionApportioned def = getDefinition();

        LedgerAccount__c laCash = LedgerBalanceTest.mockLedgerAccount(AccountSubType.BankAccount, 'Cash');
        LedgerAccount__c laRev1 = LedgerBalanceTest.mockLedgerAccount(AccountSubType.EarnedRevenue, 'Earned Revenue #1');
        LedgerAccount__c laRev2 = LedgerBalanceTest.mockLedgerAccount(AccountSubType.EarnedRevenue, 'Earned Revenue #2');

        Date txDate             = Date.newInstance(2023,3,15);

        LedgerEntry__c le1              = LedgerEntryDomainTest.mockLedgerEntry(txDate, LedgerEntryStatus.Unreviewed);
        LedgerEntryLineItem__c leli1    = LedgerEntryLineItemDomainTest.mockLedgerEntry(le1, TransactionType.Debit, 100, laCash);
        LedgerEntryLineItem__c leli2    = LedgerEntryLineItemDomainTest.mockLedgerEntry(le1, TransactionType.Credit, 100, laRev1);

        HeaderProcessorApportioned hdr  = new HeaderProcessorApportioned(def);
        hdr.headerDate                  = txDate;
        LedgerBalance lbExistingCash    = hdr.getExistingLedgerBalance(laCash);
        LedgerBalance lbExistingRev1    = hdr.getExistingLedgerBalance(laRev1);
        LedgerBalance lbExistingRev2    = hdr.getExistingLedgerBalance(laRev2);
        LedgerBalance lbPresentCash     = hdr.getPresentLedgerBalance(laCash);
        LedgerBalance lbPresentRev1     = hdr.getPresentLedgerBalance(laRev1);
        LedgerBalance lbPresentRev2     = hdr.getPresentLedgerBalance(laRev2);

        // These existing line Items point to Rev1 account
        hdr.existingLedgers.add(new HeaderProcessor.ExistingLedger(lbExistingCash, leli1, false));
        hdr.existingLedgers.add(new HeaderProcessor.ExistingLedger(lbExistingRev1, leli2, false));

        // This apportion record points to Rev2 account
        hdr.paymentLineItems.add(new HeaderProcessorApportioned.Apportion(
                'pa0000000000000'
                , hdr
                , 100
                , txDate
                , null
                , txDate
                , lbPresentCash
                , null
                , lbPresentRev2
        ));

        // Calculate stuff.
        hdr.calculate();
        hdr.getPresentBalances();
        System.debug(lbExistingCash.toString());
        System.debug(lbExistingRev1.toString());
        System.debug(lbExistingRev2.toString());
        System.debug(lbPresentCash.toString());
        System.debug(lbPresentRev1.toString());
        System.debug(lbPresentRev2.toString());

        // Assert that nothing is going to be deleted.
        List<LedgerEntry__c> leToDelete = hdr.getLedgersToDelete();
        System.debug(leToDelete);
        System.assertEquals(1, leToDelete.size());

        // Test the ledger lines that will be created
        List<Ledger> leToCreate = hdr.getLedgersToCreate();
        System.assertEquals(1, leToCreate.size());
        for (Ledger l : leToCreate) {
            System.debug(JSON.serializePretty(l));
            System.assertEquals(txDate, l.ledgerEntry.PostDate__c);
            System.assertEquals(2, l.lineItems.size());

            LedgerEntryLineItem__c cashDr;
            LedgerEntryLineItem__c revCr;
            for (LedgerEntryLineItem__c li : l.lineItems) {
                System.debug(li.Type__c + ' ' + li.Amount__c + ' ' + li.LedgerAccount__c);
                switch on (li.Type__c) {
                    when 'Debit' {
                        cashDr = li;
                    }
                    when 'Credit' {
                        revCr = li;
                    }
                }
            }

            System.assertNotEquals(null, cashDr);
            System.assertEquals(laCash.Id, cashDr.LedgerAccount__r.Id);
            System.assertEquals(100, cashDr.Amount__c);
            System.assertNotEquals(null, revCr);
            System.assertEquals(laRev2.Id, revCr.LedgerAccount__r.Id);
            System.assertEquals(100, revCr.Amount__c);
        }
    }

    /**
     * test that revenue is transferred from Rev1 to Rev2
     *
     * Should result in:
     *  Dr: Cash (posted)
     *   Cr: Rev1 (posted)
     *  Dr: Rev1 (new)
     *   Cr: Rev2 (new)
     */
    @IsTest
    static void apportion_sale_revenue_transfer_posted() {
        SubledgerDefinitionApportioned def = getDefinition();

        LedgerAccount__c laCash = LedgerBalanceTest.mockLedgerAccount(AccountSubType.BankAccount, 'Cash');
        LedgerAccount__c laRev1 = LedgerBalanceTest.mockLedgerAccount(AccountSubType.EarnedRevenue, 'Earned Revenue #1');
        LedgerAccount__c laRev2 = LedgerBalanceTest.mockLedgerAccount(AccountSubType.EarnedRevenue, 'Earned Revenue #2');

        Date txDate             = Date.newInstance(2023,3,15);

        LedgerEntry__c le1      = LedgerEntryDomainTest.mockLedgerEntry(txDate, LedgerEntryStatus.Approved);
        LedgerEntryLineItem__c leli1    = LedgerEntryLineItemDomainTest.mockLedgerEntry(le1, TransactionType.Debit, 100, laCash);
        LedgerEntryLineItem__c leli2    = LedgerEntryLineItemDomainTest.mockLedgerEntry(le1, TransactionType.Credit, 100, laRev1);

        HeaderProcessorApportioned hdr  = new HeaderProcessorApportioned(def);
        hdr.headerDate                  = txDate;
        LedgerBalance lbExistingCash    = hdr.getExistingLedgerBalance(laCash);
        LedgerBalance lbExistingRev1    = hdr.getExistingLedgerBalance(laRev1);
        LedgerBalance lbExistingRev2    = hdr.getExistingLedgerBalance(laRev2);
        LedgerBalance lbPresentCash     = hdr.getPresentLedgerBalance(laCash);
        LedgerBalance lbPresentRev1     = hdr.getPresentLedgerBalance(laRev1);
        LedgerBalance lbPresentRev2     = hdr.getPresentLedgerBalance(laRev2);

        // These existing line Items point to Cash & Rev1 accounts
        hdr.existingLedgers.add(new HeaderProcessor.ExistingLedger(lbExistingCash, leli1, true));
        hdr.existingLedgers.add(new HeaderProcessor.ExistingLedger(lbExistingRev1, leli2, true));

        // This apportion record points to Rev2 account
        hdr.paymentLineItems.add(new HeaderProcessorApportioned.Apportion(
                'pa0000000000000'
                , hdr
                , 100
                , txDate
                , null
                , txDate
                , lbPresentCash
                , null
                , lbPresentRev2
        ));

        // Calculate stuff.
        hdr.calculate();
        hdr.getPresentBalances();
        System.debug(lbExistingCash.toString());
        System.debug(lbExistingRev1.toString());
        System.debug(lbExistingRev2.toString());
        System.debug(lbPresentCash.toString());
        System.debug(lbPresentRev1.toString());
        System.debug(lbPresentRev2.toString());

        // Assert that nothing is going to be deleted
        List<LedgerEntry__c> leToDelete = hdr.getLedgersToDelete();
        System.debug(leToDelete);
        System.assertEquals(0, leToDelete.size());

        // Test the ledger lines that will be created
        List<Ledger> leToCreate = hdr.getLedgersToCreate();
        System.assertEquals(1, leToCreate.size());
        for (Ledger l : leToCreate) {
            System.assertEquals(txDate, l.ledgerEntry.PostDate__c);
            System.assertEquals(2, l.lineItems.size());

            LedgerEntryLineItem__c revDr;
            LedgerEntryLineItem__c revCr;
            for (LedgerEntryLineItem__c li : l.lineItems) {
                System.debug(li.Type__c + ' ' + li.Amount__c + ' ' + li.LedgerAccount__c);
                switch on (li.Type__c) {
                    when 'Debit' {
                        revDr = li;
                    }
                    when 'Credit' {
                        revCr = li;
                    }
                }
            }

            System.assertNotEquals(null, revDr);
            System.assertEquals(laRev1.Id, revDr.LedgerAccount__r.Id);
            System.assertEquals(100, revDr.Amount__c);
            System.assertNotEquals(null, revCr);
            System.assertEquals(laRev2.Id, revCr.LedgerAccount__r.Id);
            System.assertEquals(100, revCr.Amount__c);
        }
    }

    /**
     * A simple apportioned invoice
     */
    @IsTest
    static void apportion_invoice() {
        SubledgerDefinitionApportioned def = getDefinition();

        LedgerAccount__c laRev  = LedgerBalanceTest.mockLedgerAccount(AccountSubType.DeferredRevenue, 'Deferred Revenue');
        LedgerAccount__c laAR   = LedgerBalanceTest.mockLedgerAccount(AccountSubType.AR_Current, 'A/R');
        LedgerAccount__c laCash = LedgerBalanceTest.mockLedgerAccount(AccountSubType.BankAccount, 'Cash');
        LedgerAccount__c laWO   = LedgerBalanceTest.mockLedgerAccount(AccountSubType.WriteOff, 'Bad Debts');

        Date txDate             = Date.newInstance(2024,3,15);

        Opportunity oppyHeader  = new Opportunity();
        oppyHeader.CloseDate    = txDate;
        oppyHeader.Amount       = 1000;

        HeaderProcessorApportioned hdr  = new HeaderProcessorApportioned(def);
        hdr.headerDate                  = txDate;
        hdr.header                      = oppyHeader;
        LedgerBalance lbAR      = hdr.getPresentLedgerBalance(laAR);
        LedgerBalance lbWO      = hdr.getPresentLedgerBalance(laWO);
        LedgerBalance lbRev     = hdr.getPresentLedgerBalance(laRev);
        LedgerBalance lbCash    = hdr.getPresentLedgerBalance(laCash);


        hdr.lineItems.add(new HeaderProcessorApportioned.LineItem(hdr, lbAR, lbRev, 1000));
        hdr.payments.add(new HeaderProcessorApportioned.Payment(
                'pa0000000000000'
                , hdr
                , 500
                , Date.newInstance(2024, 3, 15)
                , null
                , lbCash
                , lbAR
                , lbRev
        ));
        hdr.payments.add(new HeaderProcessorApportioned.Payment(
                'pa0000000000001'
                , hdr
                , 250
                , Date.newInstance(2024, 3, 31)
                , null
                , lbCash
                , lbAR
                , lbRev
        ));
        hdr.payments.add(new HeaderProcessorApportioned.Payment(
                'pa0000000000001'
                , hdr
                , 250
                , null
                , null
                , lbCash
                , lbAR
                , lbRev
        ));


        hdr.paymentLineItems.add(new HeaderProcessorApportioned.Apportion(
                'pa0000000000000'
                , hdr
                , 500
                , Date.newInstance(2024, 3, 15)
                , null
                , Date.newInstance(2024, 3, 31)
                , lbCash
                , lbAR
                , lbRev
        ));
        hdr.paymentLineItems.add(new HeaderProcessorApportioned.Apportion(
                'pa0000000000001'
                , hdr
                , 250
                , Date.newInstance(2024, 3, 31)
                , null
                , Date.newInstance(2024, 3, 31)
                , lbCash
                , lbAR
                , lbRev
        ));
        hdr.paymentLineItems.add(new HeaderProcessorApportioned.Apportion(
                'pa0000000000002'
                , hdr
                , 250
                , null
                , null
                , Date.newInstance(2024, 3, 31)
                , lbWO
                , lbAR
                , lbRev
        ));

        System.Assert.areEqual(500, hdr.totalPaymentsOnTxDate());
        System.Assert.areEqual(1000, hdr.getSourceTotalAmount());
        System.Assert.isTrue(hdr.shouldHaveARorAP());
        hdr.calculate();
        hdr.getPresentBalances();

        System.debug(lbAR.toString());
        System.debug(lbRev.toString());
        System.debug(lbCash.toString());
        System.debug(lbWO.toString());

        System.assertEquals(250, lbAR.getBalance());
        System.assertEquals(1000, lbRev.getBalance());
        System.assertEquals(750, lbCash.getBalance());
        System.assertEquals(0, lbWO.getBalance());

        System.debug(JSON.serializePretty(hdr.getLedgersToCreate()));
    }

    public static SubledgerDefinitionApportioned getDefinition() {
        SubledgerDefinitionApportioned sd = new SubledgerDefinitionApportioned();
        sd.definitionId         = IdUtilsTest.generateNextId(SubledgerDefinition__mdt.SObjectType);
        sd.definitionName       = 'ApportionedTest';
        sd.type                 = SubledgerDefinition.DefinitionType.Apportioned;
        sd.isEnabled            = true;
        sd.groupLinesByAccount  = false;

        sd.headerFilter         = null;
        sd.headerSObjectType    = Opportunity.SObjectType;
        sd.headerDateField      = Opportunity.CloseDate;

        sd.ledgerEntryHeaderLookup          = LedgerEntry__c.Opportunity__c;
        sd.ledgerEntryLineItemHeaderLookup  = LedgerEntryLineItem__c.Opportunity__c;

        sd.lineItemType         = SubledgerDefinitionApportioned.LineItemType.Revenue;
        if (OrgInfoUtils.isRevenueSchedulesEnabled()) {
            SObjectType sot = ((SObject)Type.forName('OpportunityLineItemSchedule').newInstance()).getSObjectType();
            sd.lineItemSObjectType = sot;
        }
        sd.lineItemAmount       = OpportunityLineItem.TotalPrice;

        sd.lineItemARAPAcctRef      = 'ARAPAccount__r';
        sd.lineItemRevExpAcctRef    = 'RevExpAccount__r';
        sd.lineItemHeaderRef        = 'Opportunity__r';
        sd.paymentHeaderRef         = 'Opportunity__r';
        sd.payLineItemRevExpAcctRef = 'RevExpAccount__r';
        sd.payLineItemARAPAcctRef   = 'ARAPAccount__r';
        sd.payLineItemCashWOAcctRef = 'CashWOAccount__r';
        sd.payLineItemLineItemRef   = 'LineItem__r';
        sd.payLineItemPaymentRef    = 'Payment__r';



        return sd;
    }
}