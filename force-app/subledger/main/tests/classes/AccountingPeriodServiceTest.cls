/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 11/16/23.
 */

@IsTest
private class AccountingPeriodServiceTest {
    @IsTest
    static void createPeriods() {
        AccountingPeriodService.createPeriods(Date.today().year(), 36, true);
    }
}