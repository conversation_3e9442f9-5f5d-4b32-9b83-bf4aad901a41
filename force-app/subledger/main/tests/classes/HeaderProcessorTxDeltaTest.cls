/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 3/9/24.
 */

@IsTest
private class HeaderProcessorTxDeltaTest {


    @IsTest
    static void calculateDeltas() {
        List<DeltaHarness> dhs = new List<DeltaHarness>();

        /**
         *     Present      Existing      Expected
         *    Db | Cr       Db | Cr       Db | Cr
         *   ----+----     ----+----     ----+----
         *      0|   0      100| 100         |
         *   ---------     ---------     ---------
         *       0             0
         *
         * Expected Result: no new transactions
         *
         * The source has existing transactions, but shouldn't have any. Since they balance
         * leave them as-is.
         *
         */
        dhs.add(new DeltaHarness('Delta 1', 0, 0, 100, 100, 0, 0, 0));

        /**
         *     Present      Existing      Expected
         *    Db | Cr       Db | Cr       Db | Cr
         *   ----+----     ----+----     ----+----
         *    100|   0        0|   0      100|
         *   ---------     ---------     ---------
         *    100              0          100
         *
         * Expected Result: 1 new transaction
         *
         * The source has no existing transactions. Generate a new debit.
         *
         */
        dhs.add(new DeltaHarness('Delta 2', 100, 0, 0, 0, 1, 100, 0));

        /**
         *     Present      Existing      Expected
         *    Db | Cr       Db | Cr       Db | Cr
         *   ----+----     ----+----     ----+----
         *    100| 100        0|   0      100| 100
         *   ---------     ---------     ---------
         *       0             0             0
         *
         * Expected Result: no new transactions
         *
         * The source has no existing transactions. Generate a new debit and a new credit
         *
         */
        dhs.add(new DeltaHarness('Delta 3', 100, 100, 0, 0, 2, 100, 100));

        /**
         *     Present      Existing      Expected
         *    Db | Cr       Db | Cr       Db | Cr
         *   ----+----     ----+----     ----+----
         *    100| 100       50|  50       50|  50
         *   ---------     ---------     ---------
         *       0             0             0
         *
         * Expected Result: Generate a new debit and a new credit for the difference
         *
         * The source has existing transactions.
         *
         */
        dhs.add(new DeltaHarness('Delta 4', 100, 100, 50, 50, 2, 50, 50));

        /**
         *     Present      Existing      Expected
         *    Db | Cr       Db | Cr       Db | Cr
         *   ----+----     ----+----     ----+----
         *    100|   0       50|  50       50|
         *                                 50|
         *   ---------     ---------     ---------
         *    100              0          100
         *
         * Expected Result: Generate two new 50 debits (one to balance the debit and one to balance the cr)
         *
         * The source has existing transactions.
         *
         */
        dhs.add(new DeltaHarness('Delta 5', 100, 0, 50, 50, 2, 100, 0));

        /**
         *     Present      Existing      Expected
         *    Db | Cr       Db | Cr       Db | Cr
         *   ----+----     ----+----     ----+----
         *    100|   0      150|  50
         *   ---------     ---------     ---------
         *    100           100              0
         *
         * Expected Result: no new transactions
         *
         * The source has existing transactions that balance.
         *
         */
        dhs.add(new DeltaHarness('Delta 6', 100, 0, 150, 50, 0, 0, 0));


        calculateDelta(dhs);
    }

    static void calculateDelta(List<DeltaHarness> dhs) {
        HeaderProcessorTxTest.getDefinition_CashRevenue_Simple();

        GLAccount__c glaCash = new GLAccount__c(Name = '1000', GLCode__c = '1000', AccountType__c = AccountType.Asset.name(), AccountSubType__c = AccountSubType.BankAccount.name());
        insert new List<GLAccount__c>{glaCash};

        AccountingDimension__c dim = new AccountingDimension__c(
                Name = '000000',
                Code__c = '000000',
                RecordTypeId = SObjectUtils.getRecordTypeIdForObjectAndName(AccountingDimension__c.SObjectType, 'Project')
        );
        insert dim;

        LedgerAccount__c laCash = new LedgerAccount__c(Name = '1000-000000', GLAccount__r = glaCash, Project__r = dim);
        insert new List<LedgerAccount__c>{laCash};

        AccountingPeriod__c originalPeriod = new flmas__AccountingPeriod__c(flmas__FiscalYear__c = String.valueOf(DateTimeUtils.getFiscalYear(Date.today())), flmas__FiscalMonth__c = String.valueOf(DateTimeUtils.getFiscalMonth(Date.today())));
        insert new List<AccountingPeriod__c> {
                originalPeriod,
                new flmas__AccountingPeriod__c(flmas__FiscalYear__c = String.valueOf(DateTimeUtils.getFiscalYear(Date.today().addMonths(1))), flmas__FiscalMonth__c = String.valueOf(DateTimeUtils.getFiscalMonth(Date.today().addMonths(1))))
        };

        for (DeltaHarness dh : dhs) {

            //Decimal sourceTxAmount = dh.presentDebits != 0 ? dh.presentDebits : dh.presentCredits;
            HeaderProcessorTx.SourceRecord sr = new HeaderProcessorTx.SourceRecord(
                    null,
                    0,
                    Date.today(),
                    (LedgerAccount__c)null,
                    (LedgerAccount__c)null
            );

            /*
             todo WIP
               instead of calling getDeltaBalances, call calculateDelta().
               It makes for a more discreet test where we can test the comparison of two LedgerBalance instances
               without the logic of getDeltaBalances() getting in the way
             */

            /*
            create present LedgerBalance
            create existing LedgerBalance
             */
            LedgerBalance present   = new LedgerBalance(laCash, false);
            if (dh.presentDebits != 0) {
                present.applyDebit(dh.presentDebits, Date.today(), false);
            }
            if (dh.presentCredits != 0) {
                present.applyCredit(dh.presentCredits, Date.today(), false);
            }

            LedgerBalance existing  = new LedgerBalance(laCash, true);
            if (dh.existingDebits != 0) {
                existing.applyDebit(dh.existingDebits, Date.today(), true);
            }
            if (dh.existingCredits != 0) {
                existing.applyCredit(dh.existingCredits, Date.today(), true);
            }

            LedgerBalance deltaBalance = sr.calculateDelta(present, existing);

            System.Assert.areEqual(dh.expectedTransactions, deltaBalance.transactions.size(), 'Expected Transactions mismatch: ' + dh.testName);
            if (dh.expectedTransactions > 0) {
                if (dh.expectedTxDebitAmount != null) {
                    System.Assert.areEqual(dh.expectedTxDebitAmount, deltaBalance.totalDebits, 'Expected Total Debit mismtach: ' + dh.testName);
                }
                if (dh.expectedTxCreditAmount != null) {
                    System.Assert.areEqual(dh.expectedTxCreditAmount, deltaBalance.totalCredits, 'Expected Total Credit mismtach: ' + dh.testName);
                }
            }

            /*if (dh.existingDebits > 0 || dh.existingCredits > 0) {
                LedgerEntry__c le = new LedgerEntry__c();
                le.TransactionDate__c = Date.today();
                le.PostDate__c = Date.today();
                le.Status__c = LedgerEntryStatus.CopiedToFinancials.name();
                le.AccountingPeriod__c = originalPeriod.Id;
                le.CopiedToFinancials__c = Date.today();

                if (dh.existingDebits > 0) {
                    LedgerEntryLineItem__c leDr = new LedgerEntryLineItem__c();
                    leDr.LedgerEntry__r = le;
                    leDr.Amount__c = dh.existingDebits;
                    leDr.LedgerAccount__r = laCash;
                    leDr.Type__c = TransactionType.Debit.name();
                    sr.addExistingLedgerEntryLineItem(leDr);
                }

                if (dh.existingCredits > 0) {
                    LedgerEntryLineItem__c leCr = new LedgerEntryLineItem__c();
                    leCr.LedgerEntry__r = le;
                    leCr.Amount__c = dh.existingCredits;
                    leCr.LedgerAccount__r = laCash;
                    leCr.Type__c = TransactionType.Credit.name();
                    sr.addExistingLedgerEntryLineItem(leCr);
                }
            }

            System.Assert.areEqual(0, sr.getLedgersToDelete().size(), 'Expected ledgers to delete mismatch: ' + dh.testName);

            List<LedgerBalance> deltaBalances = sr.getDeltaBalances();

            System.debug(dh.testName + ' Delta Balances:\n' + JSON.serializePretty(deltaBalances));
            System.Assert.areEqual(1, deltaBalances.size(), 'Expected count of deltaBalances: ' + dh.testName);
            System.Assert.areEqual(dh.expectedTransactions, deltaBalances.get(0).transactions.size(), 'Expected Transactions mismatch: ' + dh.testName);

            if (dh.expectedTransactions > 0) {
                for (LedgerBalanceTx tx : deltaBalances.get(0).transactions) {
                    System.Assert.areEqual(tx.type == TransactionType.Debit ? dh.expectedTxDebitAmount : dh.expectedTxCreditAmount, tx.amount, 'Expected ' + tx.type + ' amount: ' + dh.testName);
                }
            }*/
        }
    }

    class DeltaHarness {
        final String testName;
        final Decimal presentDebits;
        final Decimal presentCredits;
        final Decimal existingDebits;
        final Decimal existingCredits;

        final Integer expectedTransactions;
        final Decimal expectedTxDebitAmount;
        final Decimal expectedTxCreditAmount;

        private DeltaHarness(String testName, Decimal presentDebits, Decimal presentCredits, Decimal existingDebits, Decimal existingCredits, Integer expectedTransactions, Decimal expectedTxDebitAmount, Decimal expectedTxCreditAmount) {
            this.testName   = testName;
            this.presentDebits = presentDebits;
            this.presentCredits = presentCredits;
            this.existingDebits = existingDebits;
            this.existingCredits = existingCredits;
            this.expectedTxDebitAmount = expectedTxDebitAmount;
            this.expectedTxCreditAmount = expectedTxCreditAmount;
            this.expectedTransactions = expectedTransactions;
        }/*

        public override String toString() {
            *//*

         *     Present      Existing      Expected
         *    Db | Cr       Db | Cr       Db | Cr
         *   ----+----     ----+----     ----+----
         *    100|   0      150|  50
         *   ---------     ---------     ---------
         *    100           100              0
             *//*
            String prDebs = StringUtils.formatCurrency(this.presentDebits);
            String prCrds = StringUtils.formatCurrency(this.presentCredits);
            String exDebs = StringUtils.formatCurrency(this.existingDebits);
            String exCrds = StringUtils.formatCurrency(this.existingCredits);
            String newDebs = StringUtils.formatCurrency(this.expectedTxDebitAmount);
            String newCrds = StringUtils.formatCurrency(this.expectedTxCreditAmount);

            return super.toString();
        }*/
    }
}