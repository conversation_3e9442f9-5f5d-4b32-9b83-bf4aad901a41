/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 11/22/23.
 */

@IsTest
private class LedgerEntryProcessorTriggersTest {
    @IsTest
    static void testBehavior() {
        LedgerEntryProcessor__e lep = new LedgerEntryProcessor__e(
                SourceRecordId__c = IdUtilsTest.generateNextId(Opportunity.SObjectType)
        );
        Test.startTest();
        EventBus.publish(lep);
        Test.getEventBus().deliver();
        Test.stopTest();
    }
}