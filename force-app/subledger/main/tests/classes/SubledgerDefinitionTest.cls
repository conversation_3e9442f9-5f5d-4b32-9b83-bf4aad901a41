/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 4/1/23.
 */
@IsTest
public class SubledgerDefinitionTest {
    @TestSetup
    public static void setup() {
        SLSettings settings = SLSettings.getInstance();
        settings.isEnabled = true;
        settings.saveSettings();


        /*npsp__General_Accounting_Unit__c membershipGAU = new npsp__General_Accounting_Unit__c(
                Name = 'Membership',
                npsp__Active__c = true
        );
        insert membershipGAU;*/

        Product2 prod = new Product2(Name = 'Membership'); //tixtrack__GeneralAccountingUnit__c=membershipGAU.Id,
        if (OrgInfoUtils.isRevenueSchedulesEnabled()) {
            prod.put('CanUseRevenueSchedule', true);
        }
        insert prod;

        Id pbId   = Test.getStandardPricebookId();

        PricebookEntry pbe  = new PricebookEntry();
        pbe.Product2Id      = prod.Id;
        pbe.Pricebook2Id    = pbId;
        pbe.IsActive        = true;
        pbe.UnitPrice       = 100.00;
        insert pbe;
        /*
        PricebookEntry pbe = [SELECT Id FROM PricebookEntry LIMIT 1];
        npsp__General_Accounting_Unit__c membershipGAU  = [SELECT Id FROM npsp__General_Accounting_Unit__c LIMIT 1];*/

        // Create Giver & Recipient Contacts
        Contact recipient = new Contact(FirstName = 'Mrs', LastName = 'Recipient');
        insert recipient;

        // Query the contacts for their accounts
        recipient = [
                SELECT Id,
                        FirstName,
                        LastName,
                        AccountId
                FROM Contact
                WHERE Id = :recipient.Id
        ];

        // Create Oppy
        Opportunity oppy = new Opportunity();
        oppy.Name = 'Test Oppy';
        oppy.CloseDate = Date.today();
        oppy.StageName = 'Received';
        oppy.AccountId = recipient.AccountId;

        if (NPSPUtils.isNPSPInstalled()) {
            oppy.put('npsp__Primary_Contact__c', recipient.Id);
            oppy.put('npe01__Do_Not_Automatically_Create_Payment__c', true);
        }

        //oppy.RecordTypeId = SObjectUtils.getRecordTypeIdForObjectAndName(Opportunity.SObjectType, 'Ticketure_Purchase');
        oppy.Amount = 100;

        if (Type.forName('flmas','ACSettings') != null) {
            oppy.put('RevenueType__c','Individual');
        }

        insert oppy;

        OpportunityLineItem oli = new OpportunityLineItem();
        oli.OpportunityId       = oppy.Id;
        oli.PricebookEntryId    = pbe.Id;
        oli.UnitPrice           = 100.0;
        oli.Quantity            = 1;
        //oli.tixtrack__GeneralAccountingUnit__c  = membershipGAU.Id;
        insert oli; // This might generate a OLISchedule

        /*npe01__OppPayment__c pymt = new npe01__OppPayment__c(
                npe01__Opportunity__c = oppy.Id,
                npe01__Payment_Date__c = Date.today(),
                npe01__Payment_Method__c = 'Cash',
                npe01__Payment_Amount__c = oppy.Amount
        );
        insert pymt;*/
    }

    @IsTest
    static void getAllDefinitions() {
        SubledgerDefinition.getAllDefinitions(new Set<SObjectType>{Opportunity.SObjectType});
    }

    static SubledgerDefinition__mdt getDefinition(String devName) {
        List<SubledgerDefinition__mdt> defs = [
                SELECT  Id,
                        Label,
                        DeveloperName,
                        IsEnabled__c,
                        DefinitionType__c,
                        HeaderFilter__c,
                        HeaderObject__r.QualifiedApiName,
                        HeaderDate__r.QualifiedApiName,
                        HeaderDate__r.EntityDefinition.QualifiedApiName,
                        LedgerEntryObject__r.QualifiedApiName,
                        LedgerEntryHeaderLookup__r.QualifiedApiName,
                        LedgerEntryHeaderLookup__r.RelationshipName,
                        LedgerEntryHeaderLookup__r.EntityDefinition.QualifiedApiName,
                        LedgerEntryLineItemObject__r.QualifiedApiName,
                        LedgerEntryLineItemHeaderLookup__r.QualifiedApiName,
                        LedgerEntryLineItemHeaderLookup__r.RelationshipName,
                        LedgerEntryLineItemHeaderLookup__r.EntityDefinition.QualifiedApiName,
                        LineItemObject__r.QualifiedApiName,
                        LineItemType__c,
                        LineItemAmount__r.QualifiedApiName,
                        LineItemAmount__r.EntityDefinition.QualifiedApiName,
                        LineItemARorAPAcctLookup__r.QualifiedApiName,
                        LineItemARorAPAcctLookup__r.RelationshipName,
                        LineItemARorAPAcctLookup__r.EntityDefinition.QualifiedApiName,
                        LineItemRevOrExpAcctLookup__r.QualifiedApiName,
                        LineItemRevOrExpAcctLookup__r.RelationshipName,
                        LineItemRevOrExpAcctLookup__r.EntityDefinition.QualifiedApiName,
                        LineItemHeaderLookup__r.QualifiedApiName,
                        LineItemHeaderLookup__r.RelationshipName,
                        LineItemHeaderLookup__r.EntityDefinition.QualifiedApiName,
                        PaymentObject__r.QualifiedApiName,
                        PaymentHeaderLookup__r.QualifiedApiName,
                        PaymentHeaderLookup__r.RelationshipName,
                        PaymentHeaderLookup__r.EntityDefinition.QualifiedApiName,
                        PaymentPaymentAmount__r.QualifiedApiName,
                        PaymentPaymentAmount__r.EntityDefinition.QualifiedApiName,
                        PaymentDueDate__r.QualifiedApiName,
                        PaymentDueDate__r.EntityDefinition.QualifiedApiName,
                        PaymentPaidDate__r.QualifiedApiName,
                        PaymentPaidDate__r.EntityDefinition.QualifiedApiName,
                        PaymentWriteoffDate__r.QualifiedApiName,
                        PaymentWriteoffDate__r.EntityDefinition.QualifiedApiName,
                        PaymentCashOrWOAcctLookup__r.QualifiedApiName,
                        PaymentCashOrWOAcctLookup__r.RelationshipName,
                        PaymentCashOrWOAcctLookup__r.EntityDefinition.QualifiedApiName,
                        PaymentLineItemObject__r.QualifiedApiName,
                        PaymentLineItemPaymentLookup__r.QualifiedApiName,
                        PaymentLineItemPaymentLookup__r.RelationshipName,
                        PaymentLineItemPaymentLookup__r.EntityDefinition.QualifiedApiName,
                        PaymentLineItemLineItemLookup__r.QualifiedApiName,
                        PaymentLineItemLineItemLookup__r.RelationshipName,
                        PaymentLineItemLineItemLookup__r.EntityDefinition.QualifiedApiName,
                        PaymentLineItemAmount__r.QualifiedApiName,
                        PaymentLineItemAmount__r.EntityDefinition.QualifiedApiName,
                        PaymentLineItemRevOrExpAcctLookup__r.QualifiedApiName,
                        PaymentLineItemRevOrExpAcctLookup__r.RelationshipName,
                        PaymentLineItemRevOrExpAcctLookup__r.EntityDefinition.QualifiedApiName,
                        PaymentLineItemCashOrWOAcctLookup__r.QualifiedApiName,
                        PaymentLineItemCashOrWOAcctLookup__r.RelationshipName,
                        PaymentLineItemCashOrWOAcctLookup__r.EntityDefinition.QualifiedApiName,
                        PaymentLineItemARorAPAcctLookup__r.QualifiedApiName,
                        PaymentLineItemARorAPAcctLookup__r.RelationshipName,
                        PaymentLineItemARorAPAcctLookup__r.EntityDefinition.QualifiedApiName,

                        TransactionPathToHeader__c,
                        TransactionFilter__c,
                        TransactionDateFieldPath__c,
                        TransactionAmountFieldPath__c,
                        TransactionDebitAccountFieldPath__c,
                        TransactionCreditAccountFieldPath__c,
                        TransactionObject__r.QualifiedApiName
                FROM SubledgerDefinition__mdt
                WHERE DeveloperName = :devName
                LIMIT 1
        ];

        return defs.size() > 0 ? defs.get(0) : null;
    }
}