/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 11/20/23.
 */

@IsTest
private class SubledgerDefinitionTxTest {
    @IsTest
    static void construct() {
        SubledgerDefinition__mdt sdmd = new SubledgerDefinition__mdt();
        sdmd.DeveloperName = 'Test';
        sdmd.IsEnabled__c = true;
        sdmd.HeaderFilter__c = null;
        sdmd.DefinitionType__c = 'Apportioned';
        sdmd.HeaderObject__c    = 'Opportunity';
        sdmd.HeaderDate__c      = 'CloseDate';
        sdmd.LineItemType__c    = 'Revenue';


        SubledgerDefinitionTx sda = new SubledgerDefinitionTx();
        sda.init(sdmd);
    }

    @IsTest
    static void misc() {
        SubledgerDefinitionTx sda = getDefinition();

        sda.getGroupFieldPath();
        sda.getSourceRecordsObjectType();
        sda.getObjectTypes();
        sda.getHeaderPath();
    }

    @IsTest
    static void newRecord() {
        SubledgerDefinitionTx sda = getDefinition();

        sda.getProcessor(new Opportunity(), new List<SObject>(), new List<LedgerEntry__c>());
    }

    @IsTest
    static void qualifyRecords() {
        SubledgerDefinitionTx sda = getDefinition();

        sda.qualifyRecords(new List<Opportunity>{
                new Opportunity()
        }, new Map<Id,Opportunity>{});
    }

    @IsTest
    static void getSourceRecordsQuery() {
        SubledgerDefinitionTx sda = getDefinition();

        System.Assert.areEqual('SELECT opportunity, opportunity.closedate, totalprice, debitaccount__r.id, debitaccount__r.name, debitaccount__r.glaccount__r.glcode__c, debitaccount__r.glaccount__r.accounttype__c, debitaccount__r.glaccount__r.accountsubtype__c, creditaccount__r.id, creditaccount__r.name, creditaccount__r.glaccount__r.glcode__c, creditaccount__r.glaccount__r.accounttype__c, creditaccount__r.glaccount__r.accountsubtype__c FROM OpportunityLineItem WHERE Id != \'***************\' AND (Opportunity IN :headerIds AND TotalPrice != 0 AND Opportunity.CloseDate != NULL AND DebitAccount__c != NULL AND CreditAccount__c != NULL)', sda.getSourceRecordsQuery());
    }

    @IsTest
    static void getSourceRecordsQuery_format() {
        SubledgerDefinitionTx sda = getDefinition_format();

        System.Assert.areEqual('SELECT opportunity, opportunity.closedate, totalprice, opportunity.description FROM OpportunityLineItem WHERE Id != \'***************\' AND (Opportunity IN :headerIds AND TotalPrice != 0 AND Opportunity.CloseDate != NULL)', sda.getSourceRecordsQuery());
    }

    @IsTest
    static void validate() {
        SubledgerDefinitionTx sd = getDefinition();
        sd.validate();

        LedgerAccountFormat laf_cash = LedgerAccountFormatTest.getFormat_cash();
        LedgerAccountFormat laf_rev = LedgerAccountFormatTest.getFormat_rev();

        sd.debitAccountFormatId = laf_cash.definitionRecord.Id;
        sd.creditAccountFormatId = laf_rev.definitionRecord.Id;
        sd.debitAccountFormat = laf_cash;
        sd.creditAccountFormat = laf_rev;

        try {
            sd.validate(); // should fail
        } catch (Exception e) {
            System.Assert.areEqual('One of Debit Account Field Path or Debit Account Format are required.', e.getMessage());
        }

        sd.txDebitAccountFieldPath = null;
        sd.txCreditAccountFieldPath = null;
        sd.validate(); // should pass
    }

    public static SubledgerDefinitionTx getDefinition() {
        SubledgerDefinitionTx sd = new SubledgerDefinitionTx();
        sd.definitionId         = IdUtilsTest.generateNextId(SubledgerDefinition__mdt.SObjectType);
        sd.definitionName       = 'ApportionedTest';
        sd.type                 = SubledgerDefinition.DefinitionType.Apportioned;
        sd.isEnabled            = true;

        sd.headerFilter         = null;
        sd.headerSObjectType    = Opportunity.SObjectType;
        sd.headerDateField      = Opportunity.CloseDate;

        sd.ledgerEntryHeaderLookup          = LedgerEntry__c.Opportunity__c;
        sd.ledgerEntryLineItemHeaderLookup  = LedgerEntryLineItem__c.Opportunity__c;

        sd.txSObjectType            = OpportunityLineItem.SObjectType;
        sd.txPathToHeader           = 'Opportunity';
        sd.txFilter                 = null;
        sd.txDebitAccountFieldPath  = 'DebitAccount__c';
        sd.txCreditAccountFieldPath = 'CreditAccount__c';
        sd.txDateFieldPath          = 'Opportunity.CloseDate';
        sd.txAmountFieldPath        = 'TotalPrice';
        sd.validate();

        return sd;
    }

    public static SubledgerDefinitionTx getDefinition_format() {
        SubledgerDefinitionTx sd = getDefinition();

        LedgerAccountFormat laf_cash = LedgerAccountFormatTest.getFormat_cash();
        LedgerAccountFormat laf_rev = LedgerAccountFormatTest.getFormat_rev();

        sd.debitAccountFormatId = laf_cash.definitionRecord.Id;
        sd.creditAccountFormatId = laf_rev.definitionRecord.Id;
        sd.debitAccountFormat = laf_cash;
        sd.creditAccountFormat = laf_rev;

        sd.txDebitAccountFieldPath = null;
        sd.txCreditAccountFieldPath = null;
        sd.validate();

        return sd;
    }
}