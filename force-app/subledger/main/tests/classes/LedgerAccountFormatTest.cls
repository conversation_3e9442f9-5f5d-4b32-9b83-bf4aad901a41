/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 3/30/23.
 */

@IsTest
public class LedgerAccountFormatTest {
    static final String format_record_json = '{\n' +
            '  "Id": "m02DR0000017L12YAE",\n' +
            '  "DeveloperName": "TestMock",\n' +
            '  "flmas__IsActive__c": true,\n' +
            '  "flmas__FormatValidation__c": "(\\\\d{4})-(\\\\d{5})",\n' +
            '  "flmas__SegmentSeparatorChar__c": "-",\n' +
            '  "flmas__LedgerAccountObject__c": "01IDR000003xtG0",\n' +
            '  "flmas__DefaultAccountCodeFormula__c": "\\"4200-_____\\"",\n' +
            '  "flmas__ObjectWithAccountLookup__r": {\n' +
            '    "attributes": {\n' +
            '      "type": "EntityDefinition",\n' +
            '      "url": "/services/data/v57.0/sobjects/EntityDefinition/01IDR000003xtG0.00NDR00000ky3ql"\n' +
            '    },\n' +
            '    "Id": "000000000000000AAA",\n' +
            '    "QualifiedApiName": "OpportunityLineItem"\n' +
            '  },\n' +
            '  "flmas__AccountLookupField__r": {\n' +
            '    "attributes": {\n' +
            '      "type": "FieldDefinition",\n' +
            '      "url": "/services/data/v57.0/sobjects/FieldDefinition/01IDR000003xtG0.00NDR00000ky3ql"\n' +
            '    },\n' +
            '    "Id": "000000000000000AAA",\n' +
            '    "QualifiedApiName": "flmas__RevenueAccount__c"\n' +
            '  },\n' +
            '  "flmas__Segment1Type__c": "GL_Account",\n' +
            '  "flmas__Segment1Field__r": {\n' +
            '    "attributes": {\n' +
            '      "type": "FieldDefinition",\n' +
            '      "url": "/services/data/v57.0/sobjects/FieldDefinition/01IDR000003xtG0.00NDR00000ky3ql"\n' +
            '    },\n' +
            '    "Id": "000000000000000AAA",\n' +
            '    "QualifiedApiName": "flmas__GLAccount__c"\n' +
            '  },\n' +
            '  "flmas__Segment2Type__c": "Project",\n' +
            '  "flmas__Segment2Field__r": {\n' +
            '    "attributes": {\n' +
            '      "type": "FieldDefinition",\n' +
            '      "url": "/services/data/v57.0/sobjects/FieldDefinition/01IDR000003xtG0.00NDR00000kytmx"\n' +
            '    },\n' +
            '    "Id": "000000000000000AAA",\n' +
            '    "QualifiedApiName": "flmas__Project__c"\n' +
            '  },\n' +
            '  "flmas__Segment2ValueFormula__c": "Opportunity.Description",\n' +
            '  "flmas__Segment2ReplacementRules__c": "[{\\"co\\":\\"e\\",\\"ce\\":\\"\\\\\\"Individual\\\\\\"\\",\\"v\\":\\"\\\\\\"00000\\\\\\"\\"}]"\n' +
            '}';

    private static LedgerAccountFormat format_cash;
    private static LedgerAccountFormat format_rev;
    private static LedgerAccountFormat format_ar;

    public static void setup() {
        SLSettings settings = SLSettings.getInstance();
        settings.isEnabled = true;
        settings.saveSettings();

        LedgerAccountFormat__mdt testFormat = (LedgerAccountFormat__mdt)JSON.deserialize(format_record_json, LedgerAccountFormat__mdt.class);

        LedgerAccountFormat.ALL_FORMATS.clear();
        LedgerAccountFormat.ALL_FORMATS.put(testFormat.Id, new LedgerAccountFormat(testFormat));
    }

    @IsTest
    static void test() {
        setup();
        SLSettings.getInstance().isEnabled = true;

        System.assertEquals(null, LedgerAccountFormat.getLedgerAccountFormat('abc-def'));

        LedgerAccount__c lafTest = new LedgerAccount__c(Name = '1234-00001');
        LedgerAccountFormat.sSetLedgerAccount(lafTest);

        System.assertNotEquals(null, lafTest);
        System.assertNotEquals(null, lafTest.GLAccount__r);
        System.assertNotEquals(null, lafTest.Project__r);
        System.assertEquals('1234', lafTest.GLAccount__r.GLCode__c);
        System.assertEquals('00001-'+AccountingDimensionDomain.RECORDTYPEINFOS_BY_DEVNAME.get('Project').recordTypeId, lafTest.Project__r.IndexedCode__c);
    }

    @IsTest
    static void segmentRule() {
        SLSettings.getInstance().isEnabled = true;
        LedgerAccountFormat.SegmentRule sr = new LedgerAccountFormat.SegmentRule();


        sr.co = LedgerAccountFormat.ComparisonOperator.lt;
        System.Assert.isTrue(sr.matches(1.0, '2.0')); // decimal
        System.Assert.isTrue(sr.matches(Datetime.newInstanceGMT(2023,3,2,12,1,2), '2023-03-04T12:00:00.0Z')); // Datetime
        System.Assert.isTrue(sr.matches(Date.newInstance(2023,3,2), '2023-03-04')); // Date
        System.Assert.isTrue(sr.matches('1', '2')); // String
        System.Assert.isTrue(sr.matches(false, 'true')); // Boolean

        sr.co = LedgerAccountFormat.ComparisonOperator.lte;
        System.Assert.isTrue(sr.matches(1.0, '2.0')); // decimal
        System.Assert.isTrue(sr.matches(Datetime.newInstanceGMT(2023,3,2,12,1,2), '2023-03-04T12:00:00.0Z')); // Datetime
        System.Assert.isTrue(sr.matches(Date.newInstance(2023,3,2), '2023-03-04')); // Date
        System.Assert.isTrue(sr.matches('1', '2')); // String
        System.Assert.isTrue(sr.matches(2.0, '2.0')); // decimal
        System.Assert.isTrue(sr.matches(Datetime.newInstanceGMT(2023,3,2,12,1,2), '2023-03-02T12:01:02.0Z')); // Datetime
        System.Assert.isTrue(sr.matches(Date.newInstance(2023,3,2), '2023-03-02')); // Date
        System.Assert.isTrue(sr.matches('1', '1')); // String
        System.Assert.isTrue(sr.matches(false, 'true')); // Boolean
        System.Assert.isTrue(sr.matches(true, 'true')); // Boolean
        System.Assert.isTrue(sr.matches(false, 'false')); // Boolean

        sr.co = LedgerAccountFormat.ComparisonOperator.e;
        System.Assert.isTrue(sr.matches(2.0, '2.0')); // decimal
        System.Assert.isTrue(sr.matches(Datetime.newInstanceGMT(2023,3,2,12,1,2), '2023-03-02T12:01:02.0Z')); // Datetime
        System.Assert.isTrue(sr.matches(Date.newInstance(2023,3,2), '2023-03-02')); // Date
        System.Assert.isTrue(sr.matches('1', '1')); // String
        System.Assert.isTrue(sr.matches(true, 'true')); // Boolean
        System.Assert.isTrue(sr.matches(false, 'false')); // Boolean

        sr.co = LedgerAccountFormat.ComparisonOperator.gte;
        System.Assert.isTrue(sr.matches(2.0, '1.0')); // decimal
        System.Assert.isTrue(sr.matches(Datetime.newInstanceGMT(2023,3,2,12,1,2), '2023-03-01T01:00:00.0Z')); // Datetime
        System.Assert.isTrue(sr.matches(Date.newInstance(2023,3,2), '2023-03-01')); // Date
        System.Assert.isTrue(sr.matches('2', '1')); // String
        System.Assert.isTrue(sr.matches(2.0, '2.0')); // decimal
        System.Assert.isTrue(sr.matches(Datetime.newInstanceGMT(2023,3,2,12,1,2), '2023-03-02T12:01:02.0Z')); // Datetime
        System.Assert.isTrue(sr.matches(Date.newInstance(2023,3,2), '2023-03-02')); // Date
        System.Assert.isTrue(sr.matches('1', '1')); // String
        System.Assert.isTrue(sr.matches(true, 'false')); // Boolean
        System.Assert.isTrue(sr.matches(true, 'true')); // Boolean
        System.Assert.isTrue(sr.matches(false, 'false')); // Boolean

        sr.co = LedgerAccountFormat.ComparisonOperator.gt;
        System.Assert.isTrue(sr.matches(2.0, '1.0')); // decimal
        System.Assert.isTrue(sr.matches(Datetime.newInstanceGMT(2023,3,2,12,1,2), '2023-03-01T01:00:00.0Z')); // Datetime
        System.Assert.isTrue(sr.matches(Date.newInstance(2023,3,2), '2023-03-01')); // Date
        System.Assert.isTrue(sr.matches('2', '1')); // String
        System.Assert.isTrue(sr.matches(true, 'false')); // Boolean

        sr.co = LedgerAccountFormat.ComparisonOperator.ne;
        System.Assert.isTrue(sr.matches(true, 'false')); // Boolean
        System.Assert.isTrue(sr.matches(false, 'true')); // Boolean
    }

    @IsTest
    static void parentQuery() {
        SLSettings.getInstance().isEnabled = true;
        LedgerAccountFormat.ParentQuery pq = new LedgerAccountFormat.ParentQuery(Opportunity.SObjectType);
        pq.queryFields.add('Id');
        pq.queryFields.add('Name');
        pq.queryFields.add('CloseDate');
        pq.getRecords();
    }

    @IsTest
    static void getLedgerAccount() {
        SLSettings.getInstance().isEnabled = true;
        OpportunityLineItem oli = new OpportunityLineItem();
        oli.UnitPrice           = 100;
        oli.Quantity            = 1;
        oli.Opportunity         = new Opportunity(Description = 'Individual');

        LedgerAccount__c la = getFormat_rev().getLedgerAccount(oli);
        System.Assert.isNotNull(la);
        System.Assert.areEqual('4001-00000', la.Name);
        System.Assert.areEqual('4001-00000', la.UniqueIndex__c);
    }

    @IsTest
    static void replacementRuleDynamic() {
        SLSettings.getInstance().isEnabled = true;
        Opportunity oppy = new Opportunity();
        oppy.Description = '40100';

        OpportunityLineItem oli = new OpportunityLineItem();
        oli.Opportunity         = oppy;
        oli.UnitPrice           = 100;
        oli.Quantity            = 1;

        LedgerAccount__c la = getFormat_rev_dynamicRule().getLedgerAccount(oli);
        System.debug(la);
        System.Assert.isNotNull(la);
        System.Assert.areEqual('4001-40100', la.Name);
        System.Assert.areEqual('4001-40100', la.UniqueIndex__c);
    }

    /**
     * COSI started uploading payments in bulk and for some reason the Cash/Write-Off Account was not being set, but the
     * Revenue and Receivables accounts *are* being set.
     */
    @IsTest
    static void bulkTest() {
        SLSettings.getInstance().isEnabled = true;

        insert new List<GLAccount__c>{
                new GLAccount__c(
                        Name = '5001',
                        GLCode__c = '5001',
                        AccountType__c  = AccountType.Income.name(),
                        AccountSubType__c = AccountSubType.EarnedRevenue.name()
                ),
                new GLAccount__c(
                        Name = '1200',
                        GLCode__c = '1200',
                        AccountType__c  = AccountType.Asset.name(),
                        AccountSubType__c = AccountSubType.AR_Current.name()
                ),
                new GLAccount__c(
                        Name = '1001',
                        GLCode__c = '1001',
                        AccountType__c  = AccountType.Asset.name(),
                        AccountSubType__c = AccountSubType.BankAccount.name()
                )
        };

        LedgerAccountFormat.ALL_FORMATS_BY_OBJECT.clear();
        LedgerAccountFormat.ALL_FORMATS.clear();

        // Create 3 Formats (Revenue, Receivable, Cash)
        LedgerAccountFormat fmtRev = createFormat('OLIS-Rev','"5001-00000"', 'OpportunityLineItemSchedule','flmas__RevenueAccount__c');
        LedgerAccountFormat fmtRcv = createFormat('OLIS-AR','"1200-00000"', 'OpportunityLineItemSchedule','flmas__ReceivablesAccount__c');
        LedgerAccountFormat fmtCsh = createFormat('OLIS-Cash','"1001-00000"', 'OpportunityLineItemSchedule','flmas__CashOrWOAccount__c');

        List<LedgerAccountFormat> formats = new List<LedgerAccountFormat>();
        formats.add(fmtRev);
        formats.add(fmtRcv);
        formats.add(fmtCsh);

        System.debug(LedgerAccountFormat.ALL_FORMATS_BY_OBJECT.size());


        // Create a list of OpportunityLineItemSchedules
        List<OpportunityLineItemSchedule> olis = new List<OpportunityLineItemSchedule>();
        OpportunityLineItemSchedule schd1 = new OpportunityLineItemSchedule();
        OpportunityLineItemSchedule schd2 = new OpportunityLineItemSchedule();
        olis.add(schd1);
        olis.add(schd2);

        Test.startTest();
        LedgerAccountFormat.sApplyCode(olis, null);
        Test.stopTest();
        System.debug(schd1);
        System.debug(schd2);

        System.Assert.areNotEqual(null, olis.get(0).RevenueAccount__c);
        System.Assert.areNotEqual(null, olis.get(0).ReceivablesAccount__c);
        System.Assert.areNotEqual(null, olis.get(0).CashOrWOAccount__c);

        System.Assert.areNotEqual(null, olis.get(1).RevenueAccount__c);
        System.Assert.areNotEqual(null, olis.get(1).ReceivablesAccount__c);
        System.Assert.areNotEqual(null, olis.get(1).CashOrWOAccount__c);
    }

    public static LedgerAccountFormat getFormat_cash() {
        if (format_cash == null) {
            LedgerAccountFormat__mdt testFormat = (LedgerAccountFormat__mdt)JSON.deserialize(format_record_json, LedgerAccountFormat__mdt.class);
            testFormat.flmas__DefaultAccountCodeFormula__c = '"1001-_____"';
            testFormat.Id   = IdUtilsTest.generateNextId(LedgerAccountFormat__mdt.SObjectType);

            format_cash = new LedgerAccountFormat(testFormat);

            LedgerAccountFormat.addFormat(format_cash);
        }

        return format_cash;
    }

    public static LedgerAccountFormat getFormat_rev() {
        if (format_rev == null) {
            LedgerAccountFormat__mdt testFormat = (LedgerAccountFormat__mdt)JSON.deserialize(format_record_json, LedgerAccountFormat__mdt.class);
            testFormat.flmas__DefaultAccountCodeFormula__c = '"4001-_____"';
            System.debug(testFormat.flmas__Segment2ReplacementRules__c);
            testFormat.Id   = IdUtilsTest.generateNextId(LedgerAccountFormat__mdt.SObjectType);

            format_rev = new LedgerAccountFormat(testFormat);

            LedgerAccountFormat.addFormat(format_rev);
        }

        return format_rev;
    }

    public static LedgerAccountFormat getFormat_rev_dynamicRule() {
        if (format_rev == null) {
            LedgerAccountFormat__mdt testFormat = (LedgerAccountFormat__mdt)JSON.deserialize(format_record_json, LedgerAccountFormat__mdt.class);
            testFormat.flmas__DefaultAccountCodeFormula__c = '"4001-_____"';
            testFormat.flmas__Segment2ReplacementRules__c = '[{"co":"e","ce":"\\"Individual\\"","v":"Opportunity.Description"}]';
            System.debug(testFormat.flmas__Segment2ReplacementRules__c);
            testFormat.Id   = IdUtilsTest.generateNextId(LedgerAccountFormat__mdt.SObjectType);

            format_rev = new LedgerAccountFormat(testFormat);

            LedgerAccountFormat.addFormat(format_rev);
        }

        return format_rev;
    }

    public static LedgerAccountFormat getFormat_ar() {
        if (format_ar == null) {
            LedgerAccountFormat__mdt testFormat = (LedgerAccountFormat__mdt)JSON.deserialize(format_record_json, LedgerAccountFormat__mdt.class);
            testFormat.flmas__DefaultAccountCodeFormula__c = '"1200-_____"';
            testFormat.Id   = IdUtilsTest.generateNextId(LedgerAccountFormat__mdt.SObjectType);

            format_ar = new LedgerAccountFormat(testFormat);

            LedgerAccountFormat.addFormat(format_ar);
        }

        return format_ar;
    }



    public static LedgerAccountFormat createFormat(String name, String defaultCode, String objectWithAccountLookup, String accountLookupField) {
        LedgerAccountFormat__mdt fmt = createMdt(name, '(\\d{4})-(\\d{5})', defaultCode);
        fmt = setField(fmt, 'flmas__ObjectWithAccountLookup__r', SObjectUtilsTest.mockEntityDefinition(objectWithAccountLookup));
        fmt = setField(fmt, 'flmas__AccountLookupField__r', SObjectUtilsTest.mockFieldDefinition(objectWithAccountLookup, accountLookupField));

        LedgerAccountFormat laf = new LedgerAccountFormat(fmt);
        LedgerAccountFormat.addFormat(laf);

        System.debug('LedgerAccountFormat__mdt: ' + fmt);
        System.debug(fmt.AccountLookupField__r.QualifiedApiName);
        System.debug(SObjectUtils.getSObjectField(fmt.AccountLookupField__r));
        System.debug(laf);
        return laf;
    }
    public static LedgerAccountFormat__mdt createMdt(String name, String formatValidation, String defaultAccountCode) {
        LedgerAccountFormat__mdt mdt = new LedgerAccountFormat__mdt(
                Id = IdUtilsTest.generateNextId(LedgerAccountFormat__mdt.SObjectType),
                Label = name,
                DeveloperName = name,
                IsActive__c = true,
                FormatValidation__c  = formatValidation,
                SegmentSeparatorChar__c = '-',
                DefaultAccountCodeFormula__c = defaultAccountCode,
                Segment1Type__c = 'GL_Account',
                Segment2Type__c = 'Project'
        );
        mdt = setField(mdt, 'flmas__LedgerAccountObject__c', 'flmas__LedgerAccount__c');
        mdt = setField(mdt, 'flmas__LedgerAccountObject__r', SObjectUtilsTest.mockEntityDefinition('flmas__LedgerAccount__c'));
        mdt = setField(mdt, 'flmas__Segment1Field__r', SObjectUtilsTest.mockFieldDefinition('flmas__LedgerAccount__c', 'flmas__GLAccount__c'));
        mdt = setField(mdt, 'flmas__Segment2Field__r', SObjectUtilsTest.mockFieldDefinition('flmas__LedgerAccount__c', 'flmas__Project__c'));
        return mdt;
    }
    public static LedgerAccountFormat__mdt setField(LedgerAccountFormat__mdt cmdt, String fieldName, Object val) {
        String jsn = JSON.serialize(cmdt);
        Map<String,Object> untyped = (Map<String,Object>)JSON.deserializeUntyped(jsn);
        untyped.put(fieldName, val);
        jsn = JSON.serialize(untyped);
        return (LedgerAccountFormat__mdt) JSON.deserialize(jsn, LedgerAccountFormat__mdt.class);
    }
}