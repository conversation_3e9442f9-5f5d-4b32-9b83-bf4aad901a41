/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 4/18/24.
 */

@IsTest
private class HeaderProcessorTxGroupedTest {
    @TestSetup
    static void setup(){
        SLSettings settings = SLSettings.getInstance();
        settings.isEnabled = true;
        settings.saveSettings();
    }

    @IsTest
    static void miscCoverage_constructor() {
        HeaderProcessorTest.misc_coverage_constructor(getDefinition_ARRevenue());
    }

    /*@IsTest
    static void simple() {
        LedgerAccount__c laAR   = LedgerBalanceTest.mockLedgerAccount(AccountSubType.AR_Current, 'A/R');
        LedgerAccount__c laRev  = LedgerBalanceTest.mockLedgerAccount(AccountSubType.DeferredRevenue, 'Deferred Revenue');

        LedgerBalance lbAR     = LedgerBalanceTest.mockLedgerBalance(laAR);
        LedgerBalance lbRev     = LedgerBalanceTest.mockLedgerBalance(laRev);

        HeaderProcessorTxGrouped hdr = new HeaderProcessorTxGrouped(
                getDefinition_ARRevenue()
        );
        hdr.presentBalancesByIds.put(lbRev.account.Id, lbRev);
        hdr.presentBalancesByIds.put(lbAR.account.Id, lbAR);

        //Id srcId, Decimal amount, Date txDate, LedgerBalance debitAccount, LedgerBalance creditAccount
        Id testId1 = IdUtilsTest.generateNextId(Opportunity.SObjectType);
        Id testId2 = IdUtilsTest.generateNextId(Opportunity.SObjectType);
        hdr.sourceRecords.put(testId1, new HeaderProcessorTx.SourceRecord(
                testId1,
                100,
                Date.newInstance(2023,9,19),
                lbAR,
                lbRev
        ));
        hdr.sourceRecords.put(testId2, new HeaderProcessorTx.SourceRecord(
                testId2,
                100,
                Date.newInstance(2023,9,19),
                lbAR,
                lbRev
        ));

        hdr.calculate();
        hdr.getPresentBalances();

        System.debug(lbAR.toString());
        System.debug(lbRev.toString());

        System.assertEquals(200, lbRev.getBalance());
        System.assertEquals(0, lbRev.getTotalDebits());
        System.assertEquals(200, lbRev.getTotalCredits());

        System.assertEquals(200, lbAR.getBalance());
        System.assertEquals(200, lbAR.getTotalDebits());
        System.assertEquals(0, lbAR.getTotalCredits());

        // This is the main test
        // Validate that the grouping mechanism worked
        // 2 source transactions with the same db/cr accounts should result in 1 db and 1 cr
        List<Ledger> lCreates = hdr.getLedgersToCreate();
        System.assertEquals(1, lCreates.size());
        System.assertEquals(2, lCreates.get(0).lineItems.size());

        List<LedgerEntry__c> lDeletes = hdr.getLedgersToDelete();
        System.assertEquals(0, lDeletes.size());
    }*/

    /*
     #125. When a header has Ledgers that are already posted and their Accounting Period is closed,
     Subledger was creating *new* ledgers to move the balance into the next open period. This test
     validates that this no longer happens.
     */
    @IsTest
    static void accountingPeriodChange() {
        /*
         - Create Source Data
         - Create posted Ledgers, linked to closed accounting period
         - Add source transaction for the closed period (e.g. a paid payment)
         - Generate ledgers for source
         - Validate that the new ledgers are in the new period.
         - Validate that no additional ledgers were created to adjust the original ledgers
         */
        SubledgerDefinitionTx sdt = getDefinition_CashRevenue_Simple();

        insert new List<GLAccount__c>{
                new GLAccount__c(Name = '1000', GLCode__c = '1000', AccountType__c = AccountType.Asset.name(), AccountSubType__c = AccountSubType.BankAccount.name()),
                new GLAccount__c(Name = '4000', GLCode__c = '4000', AccountType__c = AccountType.Income.name(), AccountSubType__c = AccountSubType.EarnedRevenue.name())
        };
        insert new AccountingDimension__c(
                Name = '000000',
                Code__c = '000000',
                RecordTypeId = SObjectUtils.getRecordTypeIdForObjectAndName(AccountingDimension__c.SObjectType, 'Project')
        );
        insert new List<LedgerAccount__c>{
                new LedgerAccount__c(Name = '1000-000000'),
                new LedgerAccount__c(Name = '4000-000000')
        };
        AccountingPeriod__c originalPeriod = new flmas__AccountingPeriod__c(flmas__FiscalYear__c = String.valueOf(DateTimeUtils.getFiscalYear(Date.today())), flmas__FiscalMonth__c = String.valueOf(DateTimeUtils.getFiscalMonth(Date.today())));
        insert new List<AccountingPeriod__c> {
                originalPeriod,
                new flmas__AccountingPeriod__c(flmas__FiscalYear__c = String.valueOf(DateTimeUtils.getFiscalYear(Date.today().addMonths(1))), flmas__FiscalMonth__c = String.valueOf(DateTimeUtils.getFiscalMonth(Date.today().addMonths(1))))
        };

        Product2 prd = new Product2(
                Name = 'Test'
        );
        insert prd;
        PricebookEntry pbe = new PricebookEntry(
                Product2Id          = prd.Id,
                Pricebook2Id        = Test.getStandardPricebookId(),
                IsActive            = true,
                UnitPrice           = 100
        );
        insert pbe;

        Opportunity header = new Opportunity(
                Name        = 'Test Header',
                CloseDate   = Date.today(),
                StageName   = 'Closed Won',
                Description = '1000-000000', // The Debit Code
                NextStep    = '4000-000000'  // The Credit code
        );
        insert header;
        OpportunityLineItem oliTx1 = new OpportunityLineItem(
                OpportunityId       = header.Id,
                UnitPrice           = 100,
                Quantity            = 1,
                PricebookEntryId    = pbe.Id,
                ServiceDate         = Date.today()
        );
        OpportunityLineItem oliTx2 = new OpportunityLineItem(
                OpportunityId       = header.Id,
                UnitPrice           = 100,
                Quantity            = 1,
                PricebookEntryId    = pbe.Id,
                ServiceDate         = Date.today()
        );
        Domain.suppressExecution(OpportunityLineItem.SObjectType);
        insert new List<OpportunityLineItem>{oliTx1,oliTx2};
        Domain.allowExecution(OpportunityLineItem.SObjectType);

        LedgerEntryService.processRecords(new Set<Id>{header.Id}, sdt);
        List<LedgerEntryLineItem__c> lelis = [SELECT Id, Amount__c, Type__c, LedgerAccount__r.Name, LedgerEntry__r.PostDate__c FROM LedgerEntryLineItem__c];
        System.Assert.areEqual(2, lelis.size());
        System.Assert.areEqual(Date.today(), lelis.get(0).LedgerEntry__r.PostDate__c);
        System.Assert.areEqual(Date.today(), lelis.get(1).LedgerEntry__r.PostDate__c);
        System.Assert.areEqual(200, lelis.get(0).Amount__c);
        System.Assert.areEqual(200, lelis.get(1).Amount__c);
        Id leli1 = lelis.get(0).Id;
        Id leli2 = lelis.get(1).Id;

        // Update the ledger to posted
        LedgerEntry__c le = lelis.get(0).LedgerEntry__r;
        le.Status__c = LedgerEntryStatus.CopiedToFinancials.name();
        Domain.suppressExecution(LedgerEntry__c.SObjectType); // Bypass error: FIELD_CUSTOM_VALIDATION_EXCEPTION, You don't have permissions to change Ledger Entry Status.. todo add permset to runas user
        update le;
        Domain.allowExecution(LedgerEntry__c.SObjectType);

        // Now starts the real test
        // Close the period, add a new transaction, and re-generate ledgers
        // Expected results:
        // 1) There's only 4 ledgers (i.e. no adjustments for the original 2 above)
        // 2) The ledgers above still exist
        // 3) New ledgers are created for the new source transaction only and posted to the next period.

        originalPeriod.Status__c = 'Closed';
        update originalPeriod;


        OpportunityLineItem oliTx3 = new OpportunityLineItem(
                OpportunityId       = header.Id,
                UnitPrice           = 200,
                Quantity            = 1,
                PricebookEntryId    = pbe.id,
                ServiceDate         = Date.today()
        );
        Domain.suppressExecution(OpportunityLineItem.SObjectType);
        insert oliTx3;
        Domain.allowExecution(OpportunityLineItem.SObjectType);

        Test.startTest();
        LedgerEntryService.processRecords(new Set<Id>{header.Id}, sdt);
        Test.stopTest();
        lelis = [SELECT Id, Amount__c, Type__c, LedgerAccount__r.Name, LedgerEntry__r.PostDate__c FROM LedgerEntryLineItem__c];
        System.debug('Ledger Entries:\n' + JSON.serializePretty(lelis));
        System.Assert.areEqual(4, lelis.size());
        System.Assert.areEqual(Date.today(), lelis.get(0).LedgerEntry__r.PostDate__c);
        System.Assert.areEqual(Date.today(), lelis.get(1).LedgerEntry__r.PostDate__c);
        System.Assert.areEqual(leli1, lelis.get(0).Id);
        System.Assert.areEqual(leli2, lelis.get(1).Id);
        System.Assert.areEqual(200, lelis.get(0).Amount__c);
        System.Assert.areEqual(200, lelis.get(1).Amount__c);

        System.Assert.areEqual(Date.today().addMonths(1).toStartOfMonth(), lelis.get(2).LedgerEntry__r.PostDate__c);
        System.Assert.areEqual(Date.today().addMonths(1).toStartOfMonth(), lelis.get(3).LedgerEntry__r.PostDate__c);
        System.Assert.areEqual(200, lelis.get(2).Amount__c);
        System.Assert.areEqual(200, lelis.get(3).Amount__c);
    }

    @IsTest
    static void adjustmentToPosted() {
        /*
         - Create Source Data
         - Create posted Ledgers, linked to closed accounting period
         - Add source transaction for the closed period (e.g. a paid payment)
         - Generate ledgers for source
         - Validate that the new ledgers are in the new period.
         - Validate that no additional ledgers were created to adjust the original ledgers
         */
        SubledgerDefinitionTx sdt = getDefinition_CashRevenue_Simple();

        insert new List<GLAccount__c>{
                new GLAccount__c(Name = '1000', GLCode__c = '1000', AccountType__c = AccountType.Asset.name(), AccountSubType__c = AccountSubType.BankAccount.name()),
                new GLAccount__c(Name = '4000', GLCode__c = '4000', AccountType__c = AccountType.Income.name(), AccountSubType__c = AccountSubType.EarnedRevenue.name())
        };
        insert new AccountingDimension__c(
                Name = '000000',
                Code__c = '000000',
                RecordTypeId = SObjectUtils.getRecordTypeIdForObjectAndName(AccountingDimension__c.SObjectType, 'Project')
        );
        insert new List<LedgerAccount__c>{
                new LedgerAccount__c(Name = '1000-000000'),
                new LedgerAccount__c(Name = '4000-000000')
        };
        AccountingPeriod__c originalPeriod = new flmas__AccountingPeriod__c(flmas__FiscalYear__c = String.valueOf(DateTimeUtils.getFiscalYear(Date.today())), flmas__FiscalMonth__c = String.valueOf(DateTimeUtils.getFiscalMonth(Date.today())));
        insert new List<AccountingPeriod__c> {
                originalPeriod,
                new flmas__AccountingPeriod__c(flmas__FiscalYear__c = String.valueOf(DateTimeUtils.getFiscalYear(Date.today().addMonths(1))), flmas__FiscalMonth__c = String.valueOf(DateTimeUtils.getFiscalMonth(Date.today().addMonths(1))))
        };

        Product2 prd = new Product2(
                Name = 'Test'
        );
        insert prd;
        PricebookEntry pbe = new PricebookEntry(
                Product2Id          = prd.Id,
                Pricebook2Id        = Test.getStandardPricebookId(),
                IsActive            = true,
                UnitPrice           = 100
        );
        insert pbe;

        Opportunity header = new Opportunity(
                Name        = 'Test Header',
                CloseDate   = Date.today(),
                StageName   = 'Closed Won',
                Description = '1000-000000', // The Debit Code
                NextStep    = '4000-000000'  // The Credit code
        );
        insert header;
        OpportunityLineItem oliTx1 = new OpportunityLineItem(
                OpportunityId       = header.Id,
                UnitPrice           = 100,
                Quantity            = 1,
                PricebookEntryId    = pbe.id,
                ServiceDate         = Date.today()
        );
        OpportunityLineItem oliTx2 = new OpportunityLineItem(
                OpportunityId       = header.Id,
                UnitPrice           = 100,
                Quantity            = 1,
                PricebookEntryId    = pbe.id,
                ServiceDate         = Date.today()
        );
        Domain.suppressExecution(OpportunityLineItem.SObjectType);
        insert new List<OpportunityLineItem>{oliTx1,oliTx2};
        Domain.allowExecution(OpportunityLineItem.SObjectType);

        LedgerEntryService.processRecords(new Set<Id>{header.Id}, sdt);
        List<LedgerEntryLineItem__c> lelis = [SELECT Id, Amount__c, Type__c, LedgerAccount__r.Name, LedgerEntry__r.PostDate__c FROM LedgerEntryLineItem__c];
        System.Assert.areEqual(2, lelis.size());
        System.Assert.areEqual(Date.today(), lelis.get(0).LedgerEntry__r.PostDate__c);
        System.Assert.areEqual(Date.today(), lelis.get(1).LedgerEntry__r.PostDate__c);
        System.Assert.areEqual(200, lelis.get(1).Amount__c);
        System.Assert.areEqual(200, lelis.get(1).Amount__c);
        Id leli1 = lelis.get(0).Id;
        Id leli2 = lelis.get(1).Id;

        // Update the ledger to posted
        LedgerEntry__c le = lelis.get(0).LedgerEntry__r;
        le.Status__c = LedgerEntryStatus.CopiedToFinancials.name();
        Domain.suppressExecution(LedgerEntry__c.SObjectType); // Bypass error: FIELD_CUSTOM_VALIDATION_EXCEPTION, You don't have permissions to change Ledger Entry Status.. todo add permset to runas user
        update le;
        Domain.allowExecution(LedgerEntry__c.SObjectType);

        // Now starts the real test
        // Close the period, add a new transaction, and re-generate ledgers
        // Expected results:
        // 1) There's only 4 ledgers (i.e. no adjustments for the original 2 above)
        // 2) The ledgers above still exist
        // 3) New ledgers are created for the new source transaction only and posted to the next period.

        originalPeriod.Status__c = 'Closed';
        update originalPeriod;


        OpportunityLineItem oliTx3 = new OpportunityLineItem(
                OpportunityId       = header.Id,
                UnitPrice           = 200,
                Quantity            = 1,
                PricebookEntryId    = pbe.Id,
                ServiceDate         = Date.today()
        );
        Domain.suppressExecution(OpportunityLineItem.SObjectType);
        insert oliTx3;
        Domain.allowExecution(OpportunityLineItem.SObjectType);

        Test.startTest();
        LedgerEntryService.processRecords(new Set<Id>{header.Id}, sdt);
        Test.stopTest();
        lelis = [SELECT Id, Amount__c, Type__c, LedgerAccount__r.Name, LedgerEntry__r.PostDate__c FROM LedgerEntryLineItem__c];
        System.debug('Ledger Entries:\n' + JSON.serializePretty(lelis));
        System.Assert.areEqual(4, lelis.size());
        System.Assert.areEqual(Date.today(), lelis.get(0).LedgerEntry__r.PostDate__c);
        System.Assert.areEqual(Date.today(), lelis.get(1).LedgerEntry__r.PostDate__c);
        System.Assert.areEqual(leli1, lelis.get(0).Id);
        System.Assert.areEqual(leli2, lelis.get(1).Id);
        System.Assert.areEqual(200, lelis.get(0).Amount__c);
        System.Assert.areEqual(200, lelis.get(1).Amount__c);

        System.Assert.areEqual(Date.today().addMonths(1).toStartOfMonth(), lelis.get(2).LedgerEntry__r.PostDate__c);
        System.Assert.areEqual(Date.today().addMonths(1).toStartOfMonth(), lelis.get(3).LedgerEntry__r.PostDate__c);
        System.Assert.areEqual(200, lelis.get(2).Amount__c);
        System.Assert.areEqual(200, lelis.get(3).Amount__c);
    }

    @IsTest
    static void adjustmentToUnreviewed() {
        /*
         - Create Source Data
         - Create posted Ledgers, linked to closed accounting period
         - Add source transaction for the closed period (e.g. a paid payment)
         - Generate ledgers for source
         - Validate that the new ledgers are in the new period.
         - Validate that no additional ledgers were created to adjust the original ledgers
         */
        SubledgerDefinitionTx sdt = getDefinition_CashRevenue_Simple();

        insert new List<GLAccount__c>{
                new GLAccount__c(Name = '1000', GLCode__c = '1000', AccountType__c = AccountType.Asset.name(), AccountSubType__c = AccountSubType.BankAccount.name()),
                new GLAccount__c(Name = '4000', GLCode__c = '4000', AccountType__c = AccountType.Income.name(), AccountSubType__c = AccountSubType.EarnedRevenue.name())
        };
        insert new AccountingDimension__c(
                Name = '000000',
                Code__c = '000000',
                RecordTypeId = SObjectUtils.getRecordTypeIdForObjectAndName(AccountingDimension__c.SObjectType, 'Project')
        );
        insert new List<LedgerAccount__c>{
                new LedgerAccount__c(Name = '1000-000000'),
                new LedgerAccount__c(Name = '4000-000000')
        };
        AccountingPeriod__c originalPeriod = new flmas__AccountingPeriod__c(flmas__FiscalYear__c = String.valueOf(DateTimeUtils.getFiscalYear(Date.today())), flmas__FiscalMonth__c = String.valueOf(DateTimeUtils.getFiscalMonth(Date.today())));
        insert new List<AccountingPeriod__c> {
                originalPeriod,
                new flmas__AccountingPeriod__c(flmas__FiscalYear__c = String.valueOf(DateTimeUtils.getFiscalYear(Date.today().addMonths(1))), flmas__FiscalMonth__c = String.valueOf(DateTimeUtils.getFiscalMonth(Date.today().addMonths(1))))
        };

        Product2 prd = new Product2(
                Name = 'Test'
        );
        insert prd;
        PricebookEntry pbe = new PricebookEntry(
                Product2Id          = prd.Id,
                Pricebook2Id        = Test.getStandardPricebookId(),
                IsActive            = true,
                UnitPrice           = 100
        );
        insert pbe;

        Opportunity header = new Opportunity(
                Name        = 'Test Header',
                CloseDate   = Date.today(),
                StageName   = 'Closed Won',
                Description = '1000-000000', // The Debit Code
                NextStep    = '4000-000000'  // The Credit code
        );
        insert header;
        OpportunityLineItem oliTx1 = new OpportunityLineItem(
                OpportunityId       = header.Id,
                UnitPrice           = 100,
                Quantity            = 1,
                PricebookEntryId    = pbe.id,
                ServiceDate         = Date.today()
        );
        OpportunityLineItem oliTx2 = new OpportunityLineItem(
                OpportunityId       = header.Id,
                UnitPrice           = 100,
                Quantity            = 1,
                PricebookEntryId    = pbe.id,
                ServiceDate         = Date.today()
        );
        Domain.suppressExecution(OpportunityLineItem.SObjectType);
        insert new List<OpportunityLineItem>{oliTx1,oliTx2};
        Domain.allowExecution(OpportunityLineItem.SObjectType);

        LedgerEntryService.processRecords(new Set<Id>{header.Id}, sdt);
        List<LedgerEntryLineItem__c> lelis = [SELECT Id, Amount__c, Type__c, LedgerAccount__r.Name, LedgerEntry__r.PostDate__c FROM LedgerEntryLineItem__c];
        System.Assert.areEqual(2, lelis.size());
        System.Assert.areEqual(Date.today(), lelis.get(0).LedgerEntry__r.PostDate__c);
        System.Assert.areEqual(Date.today(), lelis.get(1).LedgerEntry__r.PostDate__c);
        System.Assert.areEqual(200, lelis.get(1).Amount__c);
        System.Assert.areEqual(200, lelis.get(1).Amount__c);
        Id leli1 = lelis.get(0).Id;
        Id leli2 = lelis.get(1).Id;

        // Now starts the real test
        // Close the period, add a new transaction, and re-generate ledgers
        // Expected results:
        // 1) There's only 2 ledgers.
        //  - the ledgers above are wiped away and regenerated to now include the total of all 3 line items

        originalPeriod.Status__c = 'Closed';
        update originalPeriod;


        OpportunityLineItem oliTx3 = new OpportunityLineItem(
                OpportunityId       = header.Id,
                UnitPrice           = 200,
                Quantity            = 1,
                PricebookEntryId    = pbe.Id,
                ServiceDate         = Date.today()
        );
        Domain.suppressExecution(OpportunityLineItem.SObjectType);
        insert oliTx3;
        Domain.allowExecution(OpportunityLineItem.SObjectType);

        Test.startTest();
        LedgerEntryService.processRecords(new Set<Id>{header.Id}, sdt);
        Test.stopTest();
        lelis = [SELECT Id, Amount__c, Type__c, LedgerAccount__r.Name, LedgerEntry__r.PostDate__c FROM LedgerEntryLineItem__c];
        System.debug('Ledger Entries:\n' + JSON.serializePretty(lelis));
        System.Assert.areEqual(2, lelis.size());
        System.Assert.areEqual(Date.today().addMonths(1).toStartOfMonth(), lelis.get(0).LedgerEntry__r.PostDate__c);
        System.Assert.areEqual(Date.today().addMonths(1).toStartOfMonth(), lelis.get(1).LedgerEntry__r.PostDate__c);
        System.Assert.areNotEqual(leli1, lelis.get(0).Id);
        System.Assert.areNotEqual(leli2, lelis.get(1).Id);
        System.Assert.areEqual(400, lelis.get(0).Amount__c);
        System.Assert.areEqual(400, lelis.get(1).Amount__c);
    }

    public static SubledgerDefinitionTx getDefinition_CashRevenue_Simple() {
        SubledgerDefinition__mdt mdt                = new SubledgerDefinition__mdt();
        mdt.Id                                      = IdUtilsTest.generateNextId(SubledgerDefinition__mdt.SObjectType);
        mdt.DeveloperName                           = 'simpleMockOppy';
        mdt.DefinitionType__c                       = 'Tx';
        mdt.IsEnabled__c                            = true;
        mdt.HeaderObject__c                         = 'Opportunity';
        mdt.HeaderDate__c                           = 'CloseDate';
        mdt.TransactionPathToHeader__c              = 'OpportunityId';
        mdt.TransactionObject__c                    = 'OpportunityLineItem';
        mdt.TransactionFilter__c                    = '';
        mdt.TransactionAmountFieldPath__c           = 'UnitPrice';
        mdt.TransactionDateFieldPath__c             = 'ServiceDate';/*
        mdt.TransactionDebitAccountFieldPath__c     = 'Opportunity.Description';
        mdt.TransactionCreditAccountFieldPath__c    = 'Opportunity.NextStep';*/
        mdt.TxDebitAccountFormat__r                 = new LedgerAccountFormat__mdt(Id = IdUtilsTest.generateNextId(LedgerAccountFormat__mdt.SObjectType));
        mdt.TxCreditAccountFormat__r                = new LedgerAccountFormat__mdt(Id = IdUtilsTest.generateNextId(LedgerAccountFormat__mdt.SObjectType));
        mdt.GroupLedgersByAccount__c                = true;

        SubledgerDefinitionTx sdt =  buildSD(mdt);
        sdt.useFormatsNotRefs = true;

        sdt.debitAccountFormat                              = new LedgerAccountFormat(
                Pattern.compile('(\\d{4})-(\\d{6})'),
                Opportunity.SObjectType,
                'Opportunity.Description',
                Opportunity.SObjectType.getDescribe(),
                '-'
        );
        sdt.debitAccountFormat.segments.add(
                new LedgerAccountFormat.SegmentDefinition(
                        0,
                        'GL_Account',
                        LedgerAccount__c.GLAccount__c
                )
        );
        sdt.debitAccountFormat.segments.add(
                new LedgerAccountFormat.SegmentDefinition(
                        0,
                        'Project',
                        LedgerAccount__c.Project__c
                )
        );
        LedgerAccountFormat.ALL_FORMATS.put(mdt.TxDebitAccountFormat__r.Id, sdt.debitAccountFormat);

        sdt.creditAccountFormat                              = new LedgerAccountFormat(
                Pattern.compile('(\\d{4})-(\\d{6})'),
                Opportunity.SObjectType,
                'Opportunity.NextStep',
                Opportunity.SObjectType.getDescribe(),
                '-'
        );
        sdt.creditAccountFormat.segments.add(
                new LedgerAccountFormat.SegmentDefinition(
                        0,
                        'GL_Account',
                        LedgerAccount__c.GLAccount__c
                )
        );
        sdt.creditAccountFormat.segments.add(
                new LedgerAccountFormat.SegmentDefinition(
                        0,
                        'Project',
                        LedgerAccount__c.Project__c
                )
        );
        LedgerAccountFormat.ALL_FORMATS.put(mdt.TxCreditAccountFormat__r.Id, sdt.creditAccountFormat);

        System.debug('sdt: ' + sdt);

        return sdt;
    }

    public static SubledgerDefinitionTx getDefinition_ARRevenue() {
        SubledgerDefinition__mdt mdt                = new SubledgerDefinition__mdt();
        mdt.Id                                      = IdUtilsTest.generateNextId(SubledgerDefinition__mdt.SObjectType);
        mdt.DeveloperName                           = 'Receivables';
        mdt.DefinitionType__c                       = 'Tx';
        mdt.IsEnabled__c                            = true;
        mdt.HeaderObject__c                         = 'Opportunity';
        mdt.HeaderDate__c                           = 'CloseDate';
        mdt.TransactionObject__c                    = 'OpportunityLineItemSchedule';
        mdt.TransactionFilter__c                    = '';
        mdt.TransactionAmountFieldPath__c           = 'Revenue';
        mdt.TransactionDateFieldPath__c             = 'ScheduleDate';
        mdt.TransactionDebitAccountFieldPath__c     = 'flmas__ReceivablesAccount__c';
        mdt.TransactionCreditAccountFieldPath__c    = 'flmas__RevenueAccount__c';
        mdt.GroupLedgersByAccount__c                = true;

        return buildSD(mdt);
    }

    private static SubledgerDefinitionTx buildSD(SubledgerDefinition__mdt mdt) {
        SubledgerDefinitionTx sd            = new SubledgerDefinitionTx();
        sd.init(mdt);
        sd.headerSObjectType                = ((SObject)Type.forName(mdt.HeaderObject__c).newInstance()).getSObjectType();
        sd.headerDateField                  = sd.headerSObjectType.getDescribe().fields.getMap().get(mdt.HeaderDate__c);//Opportunity.CloseDate;
        sd.ledgerEntryHeaderLookup          = LedgerEntry__c.Opportunity__c;
        sd.ledgerEntryLineItemHeaderLookup  = LedgerEntryLineItem__c.Opportunity__c;
        System.debug(mdt.TransactionObject__c);
        if (mdt.TransactionObject__c != null && Type.forName(mdt.TransactionObject__c) != null) {
            sd.txSObjectType = ((SObject) Type.forName(mdt.TransactionObject__c).newInstance()).getSObjectType();
        }
        return sd;
    }
}