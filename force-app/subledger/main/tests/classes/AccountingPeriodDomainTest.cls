/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 3/27/23.
 */
@IsTest
public class AccountingPeriodDomainTest {

    @TestSetup
    public static void setup() {
        SLSettings settings = SLSettings.getInstance();
        settings.isEnabled = true;
        settings.saveSettings();

        AccountingPeriod__c ap  = new AccountingPeriod__c();
        ap.FiscalMonth__c     = '1';
        ap.FiscalYear__c      = '2023';
        ap.Status__c          = 'Open';
        insert ap;
    }

    @IsTest
    static void test(){
        AccountingPeriod__c ap = [
                SELECT Name, PeriodStart__c, PeriodEnd__c, FiscalYear__c, Status__c, UniqueId__c
                FROM AccountingPeriod__c
                LIMIT 1
        ];

        System.assertEquals('2023-01', ap.Name);
        //System.assertEquals(Date.newInstance(2023,1,1), ap.PeriodStart__c);
        //System.assertEquals(Date.newInstance(2023,1,31), ap.PeriodEnd__c);
        System.assertEquals('2023', ap.FiscalYear__c);
        System.assertEquals('Open', ap.Status__c);
        System.assertEquals('2023-01', ap.UniqueId__c);

        /*
         Closed periods cannot be changed.
         */
        ap.Status__c    = 'Closed';
        ap.FiscalMonth__c     = '2';
        Test.startTest();
        Database.SaveResult sr = Database.update(ap, false);
        Test.stopTest();

        System.assert(!sr.success);
        System.assertEquals(Label.AccountingPeriod_NoChange, sr.getErrors().get(0).getMessage());
    }
}