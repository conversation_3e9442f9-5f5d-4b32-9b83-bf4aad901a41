/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

public class MyAccountTestHarness {

	/**
	 * Creates a complete test environment with a Contact, AuthSession, and session token
	 * @return TestContext containing all necessary test objects and tokens
	 */
	public static TestContext setupTestContext() {
		// Create test household account and contact
		Account household = new Account(
				Name = 'Test Household',
				RecordTypeId = AccountDomain.RT_HH
		);
		insert household;

		Contact testContact = new Contact(
				FirstName = 'Test',
				LastName = 'User',
				Email = '<EMAIL>',
				AccountId = household.Id,
				MyAccountPersona__c = MyAccountConstants.USER_PERSONA_VISITOR + ';' +
						MyAccountConstants.USER_PERSONA_EDUCATOR
		);
		insert testContact;

		// Create auth session with token
		String sessionToken = generateTestSessionToken();
		MyAccountAuthSession__c authSession = new MyAccountAuthSession__c(
				Contact__c = testContact.Id,
				SessionToken__c = sessionToken,
				Expiry__c = System.now().addHours(2),
				Persona__c = MyAccountConstants.USER_PERSONA_VISITOR
		);
		insert authSession;

		// Return context with all necessary objects
		return new TestContext(testContact, authSession, sessionToken);
	}

	/**
	 * Creates a test session token that matches the format used in the application
	 * @return String A base64 encoded token string
	 */
	private static String generateTestSessionToken() {
		Blob randomBytes = Crypto.generateAesKey(128);
		return EncodingUtil.base64Encode(
				Crypto.generateDigest('SHA-256', randomBytes)
		);
	}

	/**
	 * Creates a test user that will be recognized as an Experience site user
	 * @return User configured as an Experience site user
	 */
	public static User createTestExperienceUser() {
		// Try to find a Community profile
		List<Profile> communityProfiles = [
				SELECT Id
				FROM Profile
				WHERE Name LIKE '%My Account%'
				LIMIT 1
		];

		Profile p;
		if (!communityProfiles.isEmpty()) {
			p = communityProfiles[0];
		} else {
			// If no community profile exists, use a standard profile
			p = [SELECT Id FROM Profile WHERE Name = 'Standard User' LIMIT 1];

			// Set the override flag in MyAccountUserService
			MyAccountUserService.isExperienceSiteUserOverride = true;
		}

		String uniqueUsername = 'testuser' + DateTime.now().getTime() + '@example.com';

		User u = new User(
				ProfileId = p.Id,
				Username = uniqueUsername,
				Email = uniqueUsername,
				FirstName = 'Test',
				LastName = 'User',
				Alias = 'tuser',
				EmailEncodingKey = 'UTF-8',
				LanguageLocaleKey = 'en_US',
				LocaleSidKey = 'en_US',
				TimeZoneSidKey = 'America/Los_Angeles'
		);

		insert u;

		return u;
	}

	/**
	 * Container class for all test context objects
	 */
	public class TestContext {
		public Contact contact { get; private set; }
		public MyAccountAuthSession__c authSession { get; private set; }
		public String sessionToken { get; private set; }
		public Account household { get; private set; }

		public TestContext(Contact contact, MyAccountAuthSession__c authSession, String sessionToken) {
			this.contact = contact;
			this.authSession = authSession;
			this.sessionToken = sessionToken;

			// Query for household to ensure we have the full record
			this.household = [
					SELECT Id, Name
					FROM Account
					WHERE Id = :contact.AccountId
			];
		}

		/**
		 * Creates a test affiliation for the contact
		 * @return Affiliation__c The created affiliation
		 */
		public Affiliation__c createAffiliation() {
			// Create organization account
			Account org = new Account(
					Name = 'Test Organization',
					RecordTypeId = AccountDomain.RT_ORG,
					Type = 'School',
					SchoolLevel__c = 'Elementary School',
					SchoolType__c = 'Public'
			);
			insert org;

			// Create affiliation
			Affiliation__c affiliation = new Affiliation__c(
					Contact__c = this.contact.Id,
					Organization__c = org.Id,
					Role__c = 'Teacher',
					Status__c = 'Current'
			);
			insert affiliation;

			return affiliation;
		}

		/**
		 * Updates the contact with multiple personas
		 * @param personas List of persona values to set
		 */
		public void setPersonas(List<String> personas) {
			this.contact.MyAccountPersona__c = String.join(personas, ';');
			update this.contact;

			// Refresh contact
			this.contact = [
					SELECT Id, FirstName, LastName, Email, MyAccountPersona__c
					FROM Contact
					WHERE Id = :this.contact.Id
			];
		}

		/**
		 * Updates the current session persona
		 * @param persona The persona to set as active
		 */
		public void setSessionPersona(String persona) {
			this.authSession.Persona__c = persona;
			update this.authSession;

			// Refresh auth session
			this.authSession = [
					SELECT Id, Contact__c, SessionToken__c, Expiry__c, Persona__c
					FROM MyAccountAuthSession__c
					WHERE Id = :this.authSession.Id
			];
		}
	}
}