/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

@IsTest
private class MyAccountMsgsTest {

	@IsTest
	static void testUserModel_InternalUser() {
		Profile p = [SELECT Id FROM Profile WHERE Name = 'Standard User' LIMIT 1];
		User testUser = new User(
				FirstName = 'Internal',
				LastName = 'User',
				Email = '<EMAIL>',
				Username = '<EMAIL>',
				Alias = 'tuser',
				ProfileId = p.Id,
				TimeZoneSidKey = 'America/Los_Angeles',
				LocaleSidKey = 'en_US',
				EmailEncodingKey = 'UTF-8',
				LanguageLocaleKey = 'en_US'
		);

		Test.startTest();
		MyAccountMsgs.UserModel model = new MyAccountMsgs.UserModel(testUser);
		Test.stopTest();

		System.assertEquals(testUser.Id, model.Id, 'ID should match');
		System.assertEquals(testUser.FirstName, model.firstName, 'First name should match');
		System.assertEquals(testUser.LastName, model.lastName, 'Last name should match');
		System.assertEquals(testUser.Email, model.email, 'Email should match');
		System.assertEquals(MyAccountConstants.USER_PERSONA_EDUCATOR, model.selectedPersona, 'Default persona should be set');
		System.assertEquals(true, model.isInternalUser, 'Should be internal user');
		System.assertEquals(0, model.affiliations.size(), 'Should have no affiliations');
	}

	@IsTest
	static void testUserModel_ExperienceUser() {
		MyAccountTestHarness.TestContext ctx = MyAccountTestHarness.setupTestContext();

		Contact contactWithAffiliations = [
				SELECT Id, FirstName, LastName, Email, MyAccountPersona__c, tixtrack__TicketureIdentityId__c, (
						SELECT Id, Status__c, Contact__c, Role__c, Contact__r.Email,
								Organization__r.Name, Organization__r.ShippingStreet, Organization__r.ShippingCity,
								Organization__r.ShippingState, Organization__r.ShippingCountryCode, Organization__r.ShippingPostalCode,
								Organization__r.VerificationStatus__c, SuspendBooking__c
						FROM Affiliations__r
				)
				FROM Contact
				WHERE Id = :ctx.contact.Id
		];

		Test.startTest();
		MyAccountMsgs.UserModel model = new MyAccountMsgs.UserModel(contactWithAffiliations, ctx.authSession);
		Test.stopTest();

		System.assertEquals(contactWithAffiliations.Id, model.Id, 'ID should match');
		System.assertEquals(contactWithAffiliations.FirstName, model.firstName, 'First name should match');
		System.assertEquals(contactWithAffiliations.LastName, model.lastName, 'Last name should match');
		System.assertEquals(contactWithAffiliations.Email, model.email, 'Email should match');
		System.assertEquals(ctx.authSession.Persona__c, model.selectedPersona, 'Selected persona should match');
		System.assertEquals(false, model.isInternalUser, 'Should not be internal user');
		System.assertEquals(1, model.affiliations.size(), 'Should have one affiliation');

		Account personAccount = model.toPersonAccount();
		System.assertEquals(AccountDomain.RT_PERSON_ACCOUNT, personAccount.RecordTypeId, 'Should be person account');
		System.assertEquals(model.firstName, personAccount.FirstName, 'First name should match');
		System.assertEquals(model.lastName, personAccount.LastName, 'Last name should match');
		System.assertEquals(model.email, personAccount.PersonEmail, 'Email should match');
	}
}
