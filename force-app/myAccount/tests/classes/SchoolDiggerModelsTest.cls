/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

@IsTest
private class SchoolDiggerModelsTest {
    
    /**
     * Test the AutocompleteRequest class and toQueryParams method
     */
    @IsTest
    static void testAutocompleteRequest() {
        Test.startTest();
        SchoolDiggerModels.AutocompleteRequest request = new SchoolDiggerModels.AutocompleteRequest();
        request.q = 'Test Elementary';
        request.st = 'CA';
        request.qSearchCityStateName = true;
        request.level = 'Elementary';

        request.boxLatitudeNW = 38.0;
        request.boxLongitudeNW = -123.0;
        request.boxLatitudeSE = 37.0;
        request.boxLongitudeSE = -122.0;

        Map<String, String> params = request.toQueryParams();
        Test.stopTest();

        System.assertEquals('Test Elementary', params.get('q'), 'Search query should match');
        System.assertEquals('CA', params.get('st'), 'State should match');
        System.assertEquals('true', params.get('qSearchCityStateName'), 'qSearchCityStateName should match');
        System.assertEquals('Elementary', params.get('level'), 'Level should match');
        System.assertEquals('38.0', params.get('boxLatitudeNW'), 'boxLatitudeNW should match');
        System.assertEquals('-123.0', params.get('boxLongitudeNW'), 'boxLongitudeNW should match');
        System.assertEquals('37.0', params.get('boxLatitudeSE'), 'boxLatitudeSE should match');
        System.assertEquals('-122.0', params.get('boxLongitudeSE'), 'boxLongitudeSE should match');
    }
    
    /**
     * Test the AutocompleteResponse class with JSON deserialization
     */
    @IsTest
    static void testAutocompleteResponse() {
        String json = '{"schoolMatches":[{"schoolid":"123456","schoolName":"Test Elementary School","city":"San Francisco","state":"CA","zip":"94105","schoolLevel":"Elementary"}]}';
        
        Test.startTest();
        SchoolDiggerModels.AutocompleteResponse response = 
            (SchoolDiggerModels.AutocompleteResponse)System.JSON.deserialize(json, SchoolDiggerModels.AutocompleteResponse.class);
        Test.stopTest();

        System.debug(response);
        
        System.assertEquals(1, response.schoolMatches.size(), 'School list size should match');
        
        SchoolDiggerModels.SchoolMatch school = response.schoolMatches[0];
        System.assertEquals('123456', school.schoolid, 'School ID should match');
        System.assertEquals('Test Elementary School', school.schoolName, 'School name should match');
        System.assertEquals('San Francisco', school.city, 'City should match');
        System.assertEquals('CA', school.state, 'State should match');
        System.assertEquals('94105', school.zip, 'Zip should match');
        System.assertEquals('Elementary', school.schoolLevel, 'School level should match');
    }
    
    /**
     * Test the SchoolMatch class with parsedSchoolType and parsedSchoolLevel properties
     */
    @IsTest
    static void testSchoolMatch_ParsedProperties() {
        Test.startTest();

        SchoolDiggerModels.SchoolMatch elementarySchool = new SchoolDiggerModels.SchoolMatch();
        elementarySchool.schoolLevel = 'Elementary';
        System.assertEquals('Elementary', elementarySchool.parsedSchoolLevel, 'Elementary level should be parsed correctly');

        SchoolDiggerModels.SchoolMatch publicSchool = new SchoolDiggerModels.SchoolMatch();
        publicSchool.schoolLevel = 'Public';
        System.assertEquals('Public', publicSchool.parsedSchoolType, 'Public type should be parsed correctly');

        SchoolDiggerModels.SchoolMatch invalidSchool = new SchoolDiggerModels.SchoolMatch();
        invalidSchool.schoolLevel = 'Unknown';
        System.assertEquals(null, invalidSchool.parsedSchoolType, 'Invalid type should return null');
        System.assertEquals(null, invalidSchool.parsedSchoolLevel, 'Invalid level should return null');

        SchoolDiggerModels.SchoolMatch nullSchool = new SchoolDiggerModels.SchoolMatch();
        nullSchool.schoolLevel = null;
        System.assertEquals(null, nullSchool.parsedSchoolType, 'Null type should return null');
        System.assertEquals(null, nullSchool.parsedSchoolLevel, 'Null level should return null');
        
        Test.stopTest();
    }
    
    /**
     * Test the SchoolSearchResult class in MyAccountMsgs
     */
    @IsTest
    static void testSchoolSearchResult() {
        SchoolDiggerModels.SchoolMatch match = new SchoolDiggerModels.SchoolMatch();
        match.schoolid = '123456';
        match.schoolName = 'Test Elementary School';
        match.city = 'San Francisco';
        match.state = 'CA';
        match.zip = '94105';
        match.schoolLevel = 'Elementary';
        match.ncesPrivateSchoolID = 'NCES123';
        
        Test.startTest();
        MyAccountMsgs.SchoolSearchResult result = new MyAccountMsgs.SchoolSearchResult(match);
        Test.stopTest();

        System.assertEquals('123456', result.schoolId, 'School ID should match');
        System.assertEquals('Test Elementary School', result.schoolName, 'School name should match');
        System.assertEquals('San Francisco', result.city, 'City should match');
        System.assertEquals('CA', result.state, 'State should match');
        System.assertEquals('94105', result.zip, 'Zip should match');
        System.assertEquals('NCES123', result.ncesId, 'NCES ID should match');
        System.assertEquals('Elementary', result.schoolLevel, 'School level should match');
        System.assertEquals(false, result.isExisting, 'isExisting should be false for SchoolDigger results');
    }
}
