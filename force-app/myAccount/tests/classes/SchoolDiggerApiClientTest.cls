/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

@IsTest
private class SchoolDiggerApiClientTest {

	/**
	 * Test the autocompleteSchools method with a successful API response
	 */
	@IsTest
	static void testAutocompleteSchools_Success() {
		// Create mock response
		String mockResponseBody = '{"schoolMatches":[{"schoolid":"123456","schoolName":"Test Elementary School","city":"San Francisco","state":"CA","zip":"94105"}],"totalCount":1,"numberOfPages":1}';

		// Set up the mock callout
		Test.setMock(HttpCalloutMock.class, new SchoolDiggerMock(200, mockResponseBody));

		// Set up test config
		SchoolDiggerApiClient.config = new SchoolDiggerConfig__mdt(
				AppId__c = 'test-app-id',
				AppKey__c = 'test-app-key'
		);

		// Create request
		SchoolDiggerModels.AutocompleteRequest request = new SchoolDiggerModels.AutocompleteRequest();
		request.q = 'Test Elementary';
		request.st = 'CA';

		Test.startTest();
		SchoolDiggerApiClient client = new SchoolDiggerApiClient();
		SchoolDiggerModels.AutocompleteResponse response = client.autocompleteSchools(request);
		Test.stopTest();

		// Verify response
		System.assertNotEquals(null, response, 'Response should not be null');
		System.assertEquals(1, response.schoolMatches.size(), 'Should have one school');
		System.assertEquals('123456', response.schoolMatches[0].schoolid, 'School ID should match');
		System.assertEquals('Test Elementary School', response.schoolMatches[0].schoolName, 'School name should match');
		System.assertEquals('San Francisco', response.schoolMatches[0].city, 'City should match');
		System.assertEquals('CA', response.schoolMatches[0].state, 'State should match');
	}

	/**
	 * Test the autocompleteSchools method with an error response
	 */
	@IsTest
	static void testAutocompleteSchools_Error() {
		// Set up the mock callout with error response
		Test.setMock(HttpCalloutMock.class, new SchoolDiggerMock(400, '{"error":"Invalid request"}'));

		// Set up test config
		SchoolDiggerApiClient.config = new SchoolDiggerConfig__mdt(
				AppId__c = 'test-app-id',
				AppKey__c = 'test-app-key'
		);

		// Create request
		SchoolDiggerModels.AutocompleteRequest request = new SchoolDiggerModels.AutocompleteRequest();
		request.q = 'Test Elementary';

		Test.startTest();
		SchoolDiggerApiClient client = new SchoolDiggerApiClient();

		try {
			SchoolDiggerModels.AutocompleteResponse response = client.autocompleteSchools(request);
			System.assert(false, 'Should have thrown an exception');
		} catch (SchoolDiggerApiClient.SchoolDiggerApiException e) {
			// Verify exception message
			System.assert(e.getMessage().contains('API request failed: 400'), 'Exception message should contain status code');
		}
		Test.stopTest();
	}

	/**
	 * Test the buildQueryString private method
	 */
	@IsTest
	static void testBuildQueryString() {
		SchoolDiggerApiClient client = new SchoolDiggerApiClient();

		// Create test parameters
		Map<String, String> params = new Map<String, String>{
				'q' => 'Test School',
				'st' => 'CA',
				'city' => 'San Francisco',
				'empty' => null
		};

		// Use reflection to access private method
		Test.startTest();
		// We can't directly test private methods, so we'll test indirectly through the public method
		SchoolDiggerModels.AutocompleteRequest request = new SchoolDiggerModels.AutocompleteRequest();
		request.q = 'Test School';
		request.st = 'CA';

		// Set up mock for HTTP callout
		Test.setMock(HttpCalloutMock.class, new SchoolDiggerMock(200, '{"schoolMatches":[],"totalCount":0,"numberOfPages":0}'));

		// Set up test config
		SchoolDiggerApiClient.config = new SchoolDiggerConfig__mdt(
				AppId__c = 'test-app-id',
				AppKey__c = 'test-app-key'
		);

		// Call method that uses buildQueryString
		client.autocompleteSchools(request);
		Test.stopTest();

		// We can't directly verify the query string, but we can verify the method executed without error
		System.assert(true, 'Method should execute without error');
	}

	/**
	 * Mock HTTP callout for SchoolDigger API
	 */
	private class SchoolDiggerMock implements HttpCalloutMock {
		private Integer statusCode;
		private String responseBody;

		public SchoolDiggerMock(Integer statusCode, String responseBody) {
			this.statusCode = statusCode;
			this.responseBody = responseBody;
		}

		public HttpResponse respond(HttpRequest request) {
			HttpResponse response = new HttpResponse();
			response.setStatusCode(statusCode);
			response.setBody(responseBody);
			return response;
		}
	}
}
