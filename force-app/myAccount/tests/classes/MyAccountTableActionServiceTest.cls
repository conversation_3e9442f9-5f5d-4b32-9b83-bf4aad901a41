/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

@IsTest
private class MyAccountTableActionServiceTest {

	/**
	 * Test retrieving table actions for a component
	 */
	@IsTest
	static void testGetTableActions() {
		// Set up mock table action provider
		MyAccountTableActionService.tableActionProvider = new MockTableActionProvider();

		Test.startTest();
		List<MyAccountMsgs.TableAction> actions =
				MyAccountTableActionService.getTableActions('VisitorBookings');
		Test.stopTest();

		// Verify actions
		System.assertNotEquals(null, actions, 'Actions should not be null');
		System.assertEquals(3, actions.size(), 'Should have 3 actions');

		// Verify action properties
		System.assertEquals('View Details', actions[0].label, 'First action label should match');
		System.assertEquals('viewDetails', actions[0].name, 'First action name should match');
		System.assertEquals('button', actions[0].type, 'First action type should match');
		System.assertEquals('utility:preview', actions[0].iconName, 'First action icon should match');

		System.assertEquals('Cancel', actions[1].label, 'Second action label should match');
		System.assertEquals('cancelBooking', actions[1].name, 'Second action name should match');

		System.assertEquals('Print', actions[2].label, 'Third action label should match');
		System.assertEquals('printTicket', actions[2].name, 'Third action name should match');
	}

	/**
	 * Test retrieving table actions for a component that has no actions
	 */
	@IsTest
	static void testGetTableActions_NoActions() {
		// Set up mock table action provider
		MyAccountTableActionService.tableActionProvider = new MockTableActionProvider();

		Test.startTest();
		List<MyAccountMsgs.TableAction> actions =
				MyAccountTableActionService.getTableActions('NonExistentComponent');
		Test.stopTest();

		// Verify empty list is returned
		System.assertNotEquals(null, actions, 'Actions should not be null');
		System.assertEquals(0, actions.size(), 'Should have no actions');
	}

	/**
	 * Mock provider for table action metadata
	 */
	public class MockTableActionProvider implements MyAccountTableActionService.TableActionProvider {
		public List<MyAccountTableAction__mdt> getTableActions(String componentName) {
			if (componentName == 'VisitorBookings') {
				List<MyAccountTableAction__mdt> actions = new List<MyAccountTableAction__mdt>();

				// Create mock table actions
				MyAccountTableAction__mdt viewDetails = new MyAccountTableAction__mdt();
				viewDetails.MasterLabel = 'View Details';
				viewDetails.Label__c = 'View Details';
				viewDetails.ActionEvent__c = 'viewDetails';
				viewDetails.ActionType__c = 'button';
				viewDetails.IconName__c = 'utility:preview';
				viewDetails.SortOrder__c = 1;
				actions.add(viewDetails);

				MyAccountTableAction__mdt cancel = new MyAccountTableAction__mdt();
				cancel.MasterLabel = 'Cancel Booking';
				cancel.Label__c = 'Cancel';
				cancel.ActionEvent__c = 'cancelBooking';
				cancel.ActionType__c = 'button';
				cancel.IconName__c = 'utility:close';
				cancel.SortOrder__c = 2;
				actions.add(cancel);

				MyAccountTableAction__mdt print = new MyAccountTableAction__mdt();
				print.MasterLabel = 'Print Ticket';
				print.Label__c = 'Print';
				print.ActionEvent__c = 'printTicket';
				print.ActionType__c = 'button';
				print.IconName__c = 'utility:print';
				print.SortOrder__c = 3;
				actions.add(print);

				return actions;
			}
			return new List<MyAccountTableAction__mdt>();
		}
	}
}