/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

@IsTest
private class MyAccountGeneralConfigTest {

	/**
	 * Test retrieving general configuration settings
	 */
	@IsTest
	static void testGetConfig() {
		MyAccountGeneralConfig.configProvider = new MockConfigProvider();

		Test.startTest();
		MyAccountGeneralConfig.ConfigWrapper config = MyAccountGeneralConfig.getConfig();
		Test.stopTest();

		System.assertNotEquals(null, config, 'Config should not be null');
		System.assertEquals('test-site-key', config.siteKey, 'Site key should match');
		System.assertEquals('https://example.com', config.clientMainSiteUrl, 'Client main site URL should match');
		System.assertEquals(true, config.enableSchoolDiggerSearch, 'SchoolDigger search should be enabled');
	}

	/**
	 * Test checking if SchoolDigger search is enabled
	 */
	@IsTest
	static void testIsSchoolDiggerSearchEnabled() {
		MyAccountGeneralConfig.configProvider = new MockConfigProvider();

		Test.startTest();
		Boolean isEnabled = MyAccountGeneralConfig.isSchoolDiggerSearchEnabled();
		Test.stopTest();

		System.assertEquals(true, isEnabled, 'SchoolDigger search should be enabled');
	}

	/**
	 * Test the ConfigWrapper class
	 */
	@IsTest
	static void testConfigWrapper() {
		MyAccountGeneralConfig__mdt mockConfig = new MyAccountGeneralConfig__mdt();
		mockConfig.ClientMainSiteURL__c = 'https://example.com';
		mockConfig.EnableSchoolDiggerSearch__c = true;

		mockConfig.RecaptchaConfig__r = new RecaptchaConfig__mdt();
		mockConfig.RecaptchaConfig__r.SiteKey__c = 'test-site-key';

		Test.startTest();
		MyAccountGeneralConfig.ConfigWrapper wrapper = new MyAccountGeneralConfig.ConfigWrapper(mockConfig);
		Test.stopTest();

		System.assertEquals('test-site-key', wrapper.siteKey, 'Site key should match');
		System.assertEquals('https://example.com', wrapper.clientMainSiteUrl, 'Client main site URL should match');
		System.assertEquals(true, wrapper.enableSchoolDiggerSearch, 'SchoolDigger search should be enabled');
	}

	/**
	 * Test the ConfigWrapper class with null config
	 */
	@IsTest
	static void testConfigWrapper_NullConfig() {
		Test.startTest();
		MyAccountGeneralConfig.ConfigWrapper wrapper = new MyAccountGeneralConfig.ConfigWrapper(null);
		Test.stopTest();

		System.assertEquals(null, wrapper.siteKey, 'Site key should be null');
		System.assertEquals(null, wrapper.clientMainSiteUrl, 'Client main site URL should be null');
		System.assertEquals(true, wrapper.enableSchoolDiggerSearch, 'SchoolDigger search should default to enabled');
	}

	/**
	 * Mock provider for configuration metadata
	 */
	public class MockConfigProvider implements MyAccountGeneralConfig.ConfigProvider {
		public MyAccountGeneralConfig__mdt getGeneralConfig() {
			MyAccountGeneralConfig__mdt config = new MyAccountGeneralConfig__mdt();
			config.ClientMainSiteURL__c = 'https://example.com';
			config.EnableSchoolDiggerSearch__c = true;

			config.RecaptchaConfig__r = new RecaptchaConfig__mdt();
			config.RecaptchaConfig__r.SiteKey__c = 'test-site-key';

			return config;
		}
	}
}
