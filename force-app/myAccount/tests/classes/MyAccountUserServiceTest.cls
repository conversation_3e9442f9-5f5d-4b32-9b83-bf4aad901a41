/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

@IsTest
private class MyAccountUserServiceTest {

	/**
	 * Test creating a new user
	 */
	@IsTest
	static void testCreateUser() {
		String userModelJson = '{"firstName":"Test","lastName":"User","email":"<EMAIL>"}';

		Test.startTest();
		try {
			MyAccountUserService.createUser(userModelJson);

			List<Contact> contacts = [
					SELECT Id, FirstName, LastName, Email, MyAccountPersona__c
					FROM Contact
					WHERE Email = '<EMAIL>'
			];

			System.assertEquals(1, contacts.size(), 'Should have created one contact');
			System.assertEquals('Test', contacts[0].FirstName, 'First name should match');
			System.assertEquals('User', contacts[0].LastName, 'Last name should match');
			System.assertEquals('<EMAIL>', contacts[0].Email, 'Email should match');
			System.assertEquals(MyAccountConstants.USER_PERSONA_VISITOR, contacts[0].MyAccountPersona__c, 'Persona should be Visitor');

		} catch (Exception e) {
			System.assert(false, 'Should not throw exception: ' + e.getMessage());
		}
		Test.stopTest();
	}

	/**
	 * Test creating a user with missing required fields
	 */
	@IsTest
	static void testCreateUser_MissingFields() {
		String userModelJson = '{"firstName":"","lastName":"User","email":"<EMAIL>"}';

		Test.startTest();
		try {
			MyAccountUserService.createUser(userModelJson);
			System.assert(false, 'Should have thrown an exception');
		} catch (AuraHandledException e) {
			System.assert(e.getMessage().contains('All fields are required'), 'Exception message should mention required fields');
		}
		Test.stopTest();
	}

	/**
	 * Test creating a user with an email that already exists
	 */
	@IsTest
	static void testCreateUser_DuplicateEmail() {
		Contact existingContact = new Contact(
				FirstName = 'Existing',
				LastName = 'User',
				Email = '<EMAIL>'
		);
		insert existingContact;

		String userModelJson = '{"firstName":"Test","lastName":"User","email":"<EMAIL>"}';

		Test.startTest();
		try {
			MyAccountUserService.createUser(userModelJson);
			System.assert(false, 'Should have thrown an exception');
		} catch (AuraHandledException e) {
			System.debug(e.getMessage());
			System.assert(e.getMessage().contains('already an account'), 'Exception message should mention existing account');
		}
		Test.stopTest();
	}

	/**
	 * Test sending a magic link
	 */
	@IsTest
	static void testSendMagicLink() {
		MyAccountUserService.orgWideEmailAddressProvider = new MockOrgWideEmailAddressProvider();

		Contact contact = new Contact(
				FirstName = 'Test',
				LastName = 'User',
				Email = '<EMAIL>',
				MyAccountPersona__c = MyAccountConstants.USER_PERSONA_VISITOR
		);
		insert contact;

		Test.startTest();
		Messaging.reserveSingleEmailCapacity(1);

		try {
			MyAccountUserService.sendMagicLink('<EMAIL>', '/myaccount/home');

			List<MyAccountAuthSession__c> sessions = [
					SELECT Id, Contact__c, MagicLinkToken__c, Expiry__c
					FROM MyAccountAuthSession__c
					WHERE Contact__c = :contact.Id
			];

			System.assertEquals(1, sessions.size(), 'Should have created one auth session');
			System.assertNotEquals(null, sessions[0].MagicLinkToken__c, 'Magic link token should not be null');
			System.assertNotEquals(null, sessions[0].Expiry__c, 'Expiry should not be null');

		} catch (Exception e) {
			System.assert(false, 'Should not throw exception: ' + e.getMessage());
		}
		Test.stopTest();
	}

	/**
	 * Test sending a magic link to an email that doesn't exist
	 */
	@IsTest
	static void testSendMagicLink_EmailNotFound() {
		Test.startTest();
		try {
			MyAccountUserService.sendMagicLink('<EMAIL>', '/myaccount/home');
			System.assert(false, 'Should have thrown an exception');
		} catch (AuraHandledException e) {
			System.assert(e.getMessage().contains('No account found'), 'Exception message should mention no account found');
		}
		Test.stopTest();
	}

	/**
	 * Mock provider for org-wide email addresses
	 */
	public class MockOrgWideEmailAddressProvider implements MyAccountUserService.OrgWideEmailAddressProvider {
		public OrgWideEmailAddress getOrgWideEmailAddress() {
			return new OrgWideEmailAddress(
					Id = '0D2000000000001',
					Address = '<EMAIL>',
					DisplayName = 'No Reply'
			);
		}
	}
}