/**
 * Test class for MyAccountNavigationService
 */
@IsTest
private class MyAccountNavigationServiceTest {

	@IsTest
	static void testGetNavigationRecords() {
		// Setup test context
		MyAccountTestHarness.TestContext ctx = MyAccountTestHarness.setupTestContext();

		// Set up mock navigation provider
		MyAccountNavigationService.navigationProvider = new MockNavigationProvider();

		Test.startTest();
		List<MyAccountMsgs.Navigation> navItems = MyAccountNavigationService.getNavigationRecords(ctx.sessionToken);
		Test.stopTest();

		// Verify navigation items
		System.assertNotEquals(null, navItems, 'Navigation items should not be null');
		System.assertEquals(3, navItems.size(), 'Should have 3 navigation items');

		// Verify first navigation item
		System.assertEquals('Home', navItems[0].label, 'First item label should be Home');
		System.assertEquals('home', navItems[0].target, 'First item target should be home');
		System.assertEquals('/images/house-door-fill.svg', navItems[0].icon, 'First item icon should match');
		System.assertEquals(1, navItems[0].sortOrder, 'First item sort order should be 1');

		// Verify second navigation item
		System.assertEquals('My Schools', navItems[1].label, 'Second item label should be My Schools');
		System.assertEquals('myschools', navItems[1].target, 'Second item target should be myschools');
	}

	/**
	 * Test the getNavigationRecords method with an invalid session token
	 */
	@IsTest
	static void testGetNavigationRecords_InvalidToken() {
		// Create a test user that will be recognized as an Experience site user
		User testUser = MyAccountTestHarness.createTestExperienceUser();

		System.runAs(testUser) {
			Test.startTest();
			try {
				List<MyAccountMsgs.Navigation> navItems = MyAccountNavigationService.getNavigationRecords('invalid-token');
				System.assert(false, 'Should have thrown an exception');
			} catch (AuraHandledException e) {
				// Expected exception
				System.assert(true, 'Should throw exception for invalid token');
			}
			Test.stopTest();
		}
	}

	/**
	 * Mock provider for navigation metadata
	 */
	public class MockNavigationProvider implements MyAccountNavigationService.NavigationProvider {
		public List<MyAccountNavigation__mdt> getNavigationItems(String persona) {
			List<MyAccountNavigation__mdt> items = new List<MyAccountNavigation__mdt>();

			// Create mock navigation items
			MyAccountNavigation__mdt home = new MyAccountNavigation__mdt();
			home.MasterLabel = 'Home';
			home.Label__c = 'Home';
			home.Target__c = 'home';
			home.Component__c = 'myAccountHome';
			home.Icon__c = 'house-door-fill.svg';
			home.SortOrder__c = 1;
			home.Type__c = 'link';
			home.IsNavBarVisible__c = true;
			home.Header__c = 'Welcome Home';
			home.SubHeader__c = 'Manage your account';
			items.add(home);

			MyAccountNavigation__mdt bookings = new MyAccountNavigation__mdt();
			bookings.MasterLabel = 'My Schools';
			bookings.Label__c = 'My Schools';
			bookings.Target__c = 'myschools';
			bookings.Component__c = 'myAccountMySchool';
			bookings.Icon__c = 'mortarboard-fill.svg';
			bookings.SortOrder__c = 2;
			bookings.Type__c = 'link';
			bookings.IsNavBarVisible__c = true;
			bookings.Header__c = 'Get Ready for Field Trips!';
			bookings.SubHeader__c = 'Field Trip Reservations Open August 15th, 2025. Review your schools below to make sure the school(s) you need to book trips for are added to your account.';
			items.add(bookings);

			MyAccountNavigation__mdt profile = new MyAccountNavigation__mdt();
			profile.MasterLabel = 'Preferences';
			profile.Label__c = 'Preferences';
			profile.Target__c = 'preferences';
			profile.Component__c = 'myAccountPreferences';
			profile.Icon__c = 'envelope-fill.svg';
			profile.SortOrder__c = 3;
			profile.Type__c = 'link';
			profile.IsNavBarVisible__c = true;
			profile.Header__c = 'Preferences';
			profile.SubHeader__c = 'Set communication preferences and various other settings';
			items.add(profile);

			return items;
		}
	}
}
