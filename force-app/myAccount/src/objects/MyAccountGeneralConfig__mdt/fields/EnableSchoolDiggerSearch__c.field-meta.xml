<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>EnableSchoolDiggerSearch__c</fullName>
    <defaultValue>true</defaultValue>
    <description>Controls whether the SchoolDigger API is used for external school searches</description>
    <externalId>false</externalId>
    <fieldManageability>DeveloperControlled</fieldManageability>
    <inlineHelpText>When enabled, school searches will include results from the SchoolDigger API in addition to local database results</inlineHelpText>
    <label>Enable SchoolDigger Search</label>
    <type>Checkbox</type>
</CustomField>