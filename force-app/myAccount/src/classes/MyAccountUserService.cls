/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * MyAccountUserService
 * @description: Service for user management in the My Account application
 * @author: <PERSON><PERSON>
 * @date: 2/15/25
 */

global without sharing class MyAccountUserService {

    /**
     * Provider interface for org-wide email addresses
     * Allows for dependency injection in tests
     */
    public interface OrgWideEmailAddressProvider {
        OrgWideEmailAddress getOrgWideEmailAddress();
    }

    /**
     * Default implementation of OrgWideEmailAddressProvider
     * Retrieves actual org-wide email address from the org
     */
    private class DefaultOrgWideEmailAddressProvider implements OrgWideEmailAddressProvider {
        public OrgWideEmailAddress getOrgWideEmailAddress() {
            List<OrgWideEmailAddress> owas = [
                SELECT Id, Address, DisplayName
                FROM OrgWideEmailAddress
                WHERE Purpose = 'DefaultNoreply'
                LIMIT 1
            ];
            return !owas.isEmpty() ? owas[0] : null;
        }
    }

    /**
     * Provider instance - can be overridden in tests
     */
    @TestVisible
    private static OrgWideEmailAddressProvider orgWideEmailAddressProvider = new DefaultOrgWideEmailAddressProvider();

    /**
     * @description Creates a "User" (Person Account) for the MyAccount platform
     * @param userModelString
     */
    @AuraEnabled
    public static void createUser(String userModelString) {
        MyAccountMsgs.UserModel userModel = (MyAccountMsgs.UserModel) JSON.deserialize(userModelString, MyAccountMsgs.UserModel.class);
        if (String.isEmpty(userModel.firstName) || String.isEmpty(userModel.lastName) || String.isEmpty(userModel.email)) {
            AuraHandledException e = new AuraHandledException('All fields are required.');
            e.setMessage('All fields are required.');
            throw e;
        }

        List<Contact> existingContacts = [SELECT Id FROM Contact WHERE Email =: userModel.email];

        if(!existingContacts.isEmpty()) {
            AuraHandledException e = new AuraHandledException('There is already an account for this email. Please request a login link');
            e.setMessage('There is already an account for this email. Please request a login link');
            throw e;
        }

        DB.DMLResult saveResult = DB.init(false, false, false).create(userModel.toPersonAccount());

        if(saveResult.isSuccess) {
            sendMagicLink(userModel.email, null);
        }
    }

    /**
     * Retrieves the current user's information based on their session token
     * Handles both Experience site users and internal Salesforce users differently
     *
     * @param sessionToken Authentication token for the current session
     * @return MyAccountMsgs.UserModel User information including profile and affiliations
     * @throws AuraHandledException if user authentication fails or user not found
     */
    @AuraEnabled
    global static MyAccountMsgs.UserModel getCurrentUser(String sessionToken) {
        Id userId = UserInfo.getUserId();

        User currentUser = [
                SELECT Id, Profile.UserType, FirstName, LastName, Email
                FROM User
                WHERE Id = :userId
                LIMIT 1
        ];

        System.debug('REIS');
        System.debug(currentUser);

        Boolean isExperienceUser = isExperienceSiteUser(currentUser.Profile.UserType);

        System.debug('REIS');
        System.debug(isExperienceUser);

        if (!isExperienceUser) {
            return new MyAccountMsgs.UserModel(currentUser);
        }

        MyAccountMsgs.UserModel userModel = retrieveUser(sessionToken);
        return userModel;
    }

    /**
     * Generates and sends a magic link for passwordless authentication
     * Creates an auth session record with a 15-minute expiry
     *
     * @param email User's email address
     * @param returnUrl URL to redirect to after successful authentication
     * @throws AuraHandledException if no account found for email or sending fails
     */
    @AuraEnabled
    public static void sendMagicLink(String email, String returnUrl) {
        List<Contact> contact = [
                SELECT Id, Email
                FROM Contact
                WHERE Email = :email
                LIMIT 1
        ];

        if (contact.isEmpty()) {
            AuraHandledException c = new AuraHandledException('No account found.');
            c.setMessage('No account found.');
            throw c;
        }

        String hashedMagicToken = generatedHashedToken(32);

        MyAccountAuthSession__c authSession = new MyAccountAuthSession__c(
                Contact__c = contact[0].Id,
                MagicLinkToken__c = hashedMagicToken,
                Expiry__c = System.now().addMinutes(15)
        );
        insert authSession;

        String loginUrl = Site.getBaseSecureUrl() + '/auth?token=' + EncodingUtil.urlEncode(hashedMagicToken, 'UTF-8');

        if(String.isNotBlank(returnUrl)) {
            loginUrl += '&retUrl=' + EncodingUtil.urlEncode(returnUrl, 'UTF-8');
        }

        Messaging.SingleEmailMessage emailMessage = new Messaging.SingleEmailMessage();
        emailMessage.setToAddresses(new String[]{
                contact[0].Email
        });
        emailMessage.setSubject('Your Login Link');
        emailMessage.setPlainTextBody('Click to log in: ' + loginUrl);
        emailMessage.setSaveAsActivity(true);

        // Use the provider to get the org-wide email address
        OrgWideEmailAddress owa = orgWideEmailAddressProvider.getOrgWideEmailAddress();
        if (owa != null) {
            emailMessage.setOrgWideEmailAddressId(owa.Id);
        }

        if(!Test.isRunningTest()) {
            List<Messaging.SendEmailResult> sendResults = Messaging.sendEmail(
                new Messaging.SingleEmailMessage[]{
                    emailMessage
                }
            );

            if (!sendResults.get(0).isSuccess()) {
                AuraHandledException c = new AuraHandledException('Could not send email.');
                c.setMessage('Could not send email.');
                throw c;
            }
        }
    }

    /**
     * @description Handles processing a magic link token and turning it into a valid session
     * @param token
     * @return
     */
    @AuraEnabled
    public static MyAccountMsgs.AuthenticationResponse processMagicLink(String token) {
        MyAccountAuthSession__c authSession = retrieveSession(token);

        List<Contact> userContacts = [
                SELECT Id, FirstName, LastName, Email, MyAccountPersona__c,
                    (SELECT Id, Contact__c, Organization__r.Name, Role__c, Organization__r.VerificationStatus__c, SuspendBooking__c FROM Affiliations__r)
                FROM Contact
                WHERE Id = :authSession.Contact__c
                LIMIT 1
        ];

        if (userContacts.isEmpty()) {
            throw new AuraHandledException('Unable to find user.');
        }

        Contact userContact = userContacts[0];
        userContact.MyAccountLastLogin__c = Datetime.now();
        DB.DMLResult saveResult = DB.init(false, false, false).edit(userContact);

        if(!saveResult.isSuccess) {
            throw new AuraHandledException('There was a problem logging you in');
        }

        String hashedSessionToken = generatedHashedToken(32);

        MyAccountAuthSession__c sessionRecord = authSession;
        sessionRecord.MagicLinkToken__c = null; // One-time use
        sessionRecord.SessionToken__c = hashedSessionToken;
        sessionRecord.Expiry__c = System.now().addHours(2); // 1-hour session
        List<String> personas = userContact.MyAccountPersona__c.split(';');

        if(personas.size() == 1) {
            if(personas.contains(MyAccountConstants.USER_PERSONA_VISITOR)) {
                sessionRecord.Persona__c = MyAccountConstants.USER_PERSONA_VISITOR;
            } else {
                sessionRecord.Persona__c = personas.get(0);
            }
        } else {
            sessionRecord.Persona__c = MyAccountConstants.USER_PERSONA_VISITOR;
        }

        update sessionRecord;

        return new MyAccountMsgs.AuthenticationResponse(
            hashedSessionToken,
            userContact
        );
    }

    /**
     * @description Handles updating persona on the AuthSession after they select it when
     * loggin in
     * @param sessionToken
     * @param desiredPersona
     */
    @AuraEnabled
    public static void updatePersonaSelection(String sessionToken, String desiredPersona) {
        MyAccountAuthSession__c authSession = retrieveSession(sessionToken);

        if(authSession != null) {
            MyAccountMsgs.UserModel userModel = retrieveUser(authSession);

            if(userModel.isValidPersona(desiredPersona)) {
                authSession.Persona__c = desiredPersona;
                update authSession;
            }
        }
    }

    /**
     * @param sessionToken
     * @return
     */
    @AuraEnabled
    public static MyAccountMsgs.AuthenticationResponse refreshSession(String sessionToken) {

        if (String.isEmpty(sessionToken)) {
            MyAccountMsgs.UserModel user = getCurrentUser(sessionToken);

            if(user.isInternalUser) {
                return null;
            } else {
                throw new AuraHandledException('Session token missing.');
            }
        }

        MyAccountAuthSession__c session = retrieveSession(sessionToken);

        // Extend session only if it's about to expire in the next 5 minutes
        if (session.Expiry__c < System.now().addMinutes(5)) {
            String hashedSessionToken = generatedHashedToken(32);

            session.SessionToken__c = hashedSessionToken;
            session.Expiry__c = System.now().addHours(1); // Extend session by 1 hour
            update session;

            return new MyAccountMsgs.AuthenticationResponse(
                hashedSessionToken,
                new Contact(
                    Id = session.Contact__c
                )
            );
        }

        return null;
    }

    /**
     * Generates a cryptographically secure hashed token for authentication
     * Uses SHA-256 hashing algorithm for security
     *
     * @param length Desired length of the token in bytes
     * @return String Base64-encoded hashed token
     */
    private static String generatedHashedToken(Integer length) {
        Blob randomBlob = generateRandomBytes(length);
        String magicToken = EncodingUtil.base64Encode(randomBlob);

        return EncodingUtil.base64Encode(
            Crypto.generateDigest('SHA-256', Blob.valueOf(magicToken))
        );
    }

    /**
     * Generates random bytes for use in token generation
     *
     * @param length Number of random bytes to generate
     * @return Blob Random bytes
     */
    private static Blob generateRandomBytes(Integer length) {
        if (length <= 0) {
            throw new IllegalArgumentException('Length must be greater than 0');
        }

        List<Integer> randomBytes = new List<Integer>();

        while (randomBytes.size() < length) {
            Long randomValue = Crypto.getRandomLong();
            String hexString = EncodingUtil.convertToHex(Blob.valueOf(String.valueOf(randomValue)));
            Blob byteBlob = EncodingUtil.convertFromHex(hexString);

            for (Integer i = 0; i < byteBlob.size() && randomBytes.size() < length; i++) {
                randomBytes.add(byteBlob.toString().charAt(i));
            }
        }
        return Blob.valueOf(randomBytes.toString());
    }

    /**
     * For testing purposes only - allows overriding the isExperienceSiteUser method
     */
    @TestVisible
    public static Boolean isExperienceSiteUserOverride = false;

    /**
     * Determines if the given user type is an Experience site user
     *
     * @param userType Profile.UserType from User record
     * @return Boolean True if user is an Experience site user
     */
    private static Boolean isExperienceSiteUser(String userType) {
        if (Test.isRunningTest() && isExperienceSiteUserOverride) {
            return true;
        }
        return userType == 'Customer Community' || userType == 'Customer Community Plus' || userType == 'Guest';
    }

    /**
     * Retrieves full user information including contact details and affiliations
     * Used for Experience site users only
     *
     * @param sessionToken Authentication token for the current session
     * @return MyAccountMsgs.UserModel Complete user profile information
     * @throws AuraHandledException if user retrieval fails
     */
    private static MyAccountMsgs.UserModel retrieveUser(String sessionToken) {
        MyAccountAuthSession__c session = retrieveSession(sessionToken);
        return retrieveUser(session);
    }

    /**
     * @description Helper function to fetch MyAccountMsgs.UserModel using an existing MyAccountAuthSession__c record
     * @param sessionToken
     *
     * @return
     */
    private static MyAccountMsgs.UserModel retrieveUser(MyAccountAuthSession__c session) {
        if(session == null) {
            throw new AuraHandledException('Session not provided.');
        }

        List<Contact> userContacts = [
                SELECT Id, FirstName, LastName, Email, MyAccountPersona__c, tixtrack__TicketureIdentityId__c,
                    (
                        SELECT Id, Status__c, Contact__c, Role__c, Contact__r.Email,
                                Organization__r.Name, Organization__r.ShippingStreet, Organization__r.ShippingCity,
                                Organization__r.ShippingState, Organization__r.ShippingCountryCode, Organization__r.ShippingPostalCode,
                                Organization__r.VerificationStatus__c, SuspendBooking__c
                        FROM Affiliations__r
                    )
                FROM Contact
                WHERE Id = :session.Contact__c
                LIMIT 1
        ];

        if (userContacts.isEmpty()) {
            throw new AuraHandledException('Contact not found.');
        }

        Contact userContact = userContacts[0];

        return new MyAccountMsgs.UserModel(userContact, session);
    }

    /**
     * @description Helper function to handle finding the most recent active session
     * based on the sessionToken passed in as well as throwing an exception if we don't find one
     * @param sessionToken
     *
     * @return MyAccountAuthSession__c
     */
    public static MyAccountAuthSession__c retrieveSession(String sessionToken) {
        System.debug('REIS');
        System.debug(sessionToken);
        if (String.isEmpty(sessionToken)) {
            System.debug('Session token missing.');
            throw new AuraHandledException('Session token missing.');
        }

        List<MyAccountAuthSession__c> session = [
                SELECT Id, Contact__c, Expiry__c, Persona__c
                FROM MyAccountAuthSession__c
                WHERE (SessionToken__c = :sessionToken OR MagicLinkToken__c =: sessionToken)
                AND Expiry__c > :System.now()
                ORDER BY Expiry__c DESC
                LIMIT 1
        ];
        System.debug('REIS');
        System.debug('Session: ' + session);

        if (session.isEmpty()) {
            throw new AuraHandledException('Invalid or expired session.');
        }

        return session[0];
    }
}