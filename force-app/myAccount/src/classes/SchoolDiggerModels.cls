/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * SchoolDiggerApiClient
 * @description:
 * @author: <PERSON><PERSON>
 * @date: 3/21/25
 */

public class SchoolDiggerModels {

	private static final Set<String> VALID_TYPES = new Set<String>{
			'Public',
			'Private',
			'Alt'
	};

	private static final Set<String> VALID_LEVELS = new Set<String>{
			'Elementary',
			'Middle',
			'High'
	};

	public class AutocompleteRequest {
		@AuraEnabled public String q;
		@AuraEnabled public Boolean qSearchCityStateName;
		@AuraEnabled public String st;
		@AuraEnabled public String level;
		@AuraEnabled public Double boxLatitudeNW;
		@AuraEnabled public Double boxLongitudeNW;
		@AuraEnabled public Double boxLatitudeSE;
		@AuraEnabled public Double boxLongitudeSE;
		@AuraEnabled public Integer returnCount;

		public Map<String, String> toQueryParams() {
			Map<String, String> params = new Map<String, String>();
			params.put('q', this.q);
			if (this.qSearchCityStateName != null) params.put('qSearchCityStateName', String.valueOf(this.qSearchCityStateName));
			if (this.st != null) params.put('st', this.st);
			if (this.level != null) params.put('level', this.level);
			if (this.boxLatitudeNW != null) params.put('boxLatitudeNW', String.valueOf(this.boxLatitudeNW));
			if (this.boxLongitudeNW != null) params.put('boxLongitudeNW', String.valueOf(this.boxLongitudeNW));
			if (this.boxLatitudeSE != null) params.put('boxLatitudeSE', String.valueOf(this.boxLatitudeSE));
			if (this.boxLongitudeSE != null) params.put('boxLongitudeSE', String.valueOf(this.boxLongitudeSE));
			if (this.returnCount != null) params.put('returnCount', String.valueOf(this.returnCount));
			return params;
		}
	}

	public class AutocompleteResponse {
		@AuraEnabled public List<SchoolMatch> schoolMatches;

		public AutocompleteResponse() {
			this.schoolMatches = new List<SchoolMatch>();
		}
	}

	public class SchoolMatch {
		@AuraEnabled public String schoolid;
		@AuraEnabled public String schoolName;
		@AuraEnabled public String city;
		@AuraEnabled public String state;
		@AuraEnabled public String zip;
		@AuraEnabled public String schoolLevel;
		@AuraEnabled public String lowGrade;
		@AuraEnabled public String highGrade;
		@AuraEnabled public Decimal latitude;
		@AuraEnabled public Decimal longitude;
		@AuraEnabled public Boolean hasBoundary;
		@AuraEnabled public Integer rank;
		@AuraEnabled public Integer rankOf;
		@AuraEnabled public Integer rankStars;
		@AuraEnabled public String ncesPrivateSchoolID;

		@AuraEnabled
		public String parsedSchoolType {
			get {
				if (String.isBlank(schoolLevel) || VALID_TYPES == null) return null;
				return VALID_TYPES.contains(schoolLevel) ? schoolLevel : null;
			}
		}

		@AuraEnabled
		public String parsedSchoolLevel {
			get {
				if (String.isBlank(schoolLevel) || VALID_LEVELS == null) return null;
				return VALID_LEVELS.contains(schoolLevel) ? schoolLevel : null;
			}
		}

		public SchoolMatch() {}
	}
}