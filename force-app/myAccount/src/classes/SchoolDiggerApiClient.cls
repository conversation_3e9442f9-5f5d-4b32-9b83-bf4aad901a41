/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * SchoolDiggerApiClient
 * @description:
 * @author: <PERSON><PERSON>
 * @date: 3/21/25
 */

public with sharing class SchoolDiggerApiClient {
    private static final String NAMED_CREDENTIAL = 'callout:SchoolDigger';

    /**
     * Configuration metadata for SchoolDigger API
     * Lazily loaded and cached for performance
     */
    @TestVisible
    private static SchoolDiggerConfig__mdt config {
        get {
            if (config == null) {
                config = SchoolDiggerConfig__mdt.getInstance('Default');
            }
            return config;
        }
        set;
    }

    /**
     * Performs an autocomplete search for schools using the SchoolDigger API
     * Includes API credentials from named credential in the request
     *
     * @param request AutocompleteRequest containing search parameters
     * @return SchoolDiggerModels.AutocompleteResponse Response containing matching schools
     * @throws SchoolDiggerApiException if API request fails or returns non-200 status
     */
    public SchoolDiggerModels.AutocompleteResponse autocompleteSchools(SchoolDiggerModels.AutocompleteRequest request) {
        String endpoint = NAMED_CREDENTIAL + '/autocomplete/schools';

        Map<String, String> queryParams = request.toQueryParams();
        queryParams.put('appID', config.AppId__c);
        queryParams.put('appKey', config.AppKey__c);

        Http http = new Http();
        HttpRequest req = new HttpRequest();
        req.setEndpoint(endpoint + buildQueryString(queryParams));
        req.setMethod('GET');

        HttpResponse res = http.send(req);

        if (res.getStatusCode() == 200) {
            return (SchoolDiggerModels.AutocompleteResponse) JSON.deserialize(
                res.getBody(),
                SchoolDiggerModels.AutocompleteResponse.class
            );
        }

        throw new SchoolDiggerApiException('API request failed: ' + res.getStatusCode() + ' ' + res.getStatus());
    }

    /**
     * Builds a URL query string from a map of parameters
     * Handles URL encoding of parameter values
     *
     * @param params Map of parameter names to values
     * @return String Formatted query string starting with '?'
     */
    private String buildQueryString(Map<String, String> params) {
        List<String> queryParams = new List<String>();
        for (String key : params.keySet()) {
            if (params.get(key) != null) {
                queryParams.add(key + '=' + EncodingUtil.urlEncode(params.get(key), 'UTF-8'));
            }
        }
        return '?' + String.join(queryParams, '&');
    }

    /**
     * Custom exception class for SchoolDigger API errors
     * Used to distinguish API-specific errors from general exceptions
     */
    public class SchoolDiggerApiException extends Exception {}
}