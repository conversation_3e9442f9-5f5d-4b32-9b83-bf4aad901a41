/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * MyAccountMsgs
 * @description: 
 * @author: <PERSON><PERSON>
 * @date: 2/15/25
 */

global with sharing class MyAccountMsgs {

	public class Navigation implements Comparable {
		@AuraEnabled
		public String target { get; set; }
		@AuraEnabled
		public String component { get; set; }
		@AuraEnabled
		public String label { get; set; }
		@AuraEnabled
		public String header { get; set; }
		@AuraEnabled
		public String subHeader { get; set; }
		@AuraEnabled
		public String pageRefType { get; set; }
		@AuraEnabled
		public String linkType { get; set; }
		@AuraEnabled
		public String icon { get; set; }
		@AuraEnabled
		public Boolean navbarVisible { get; set; }
		@AuraEnabled
		public Decimal sortOrder { get; set; }

		public Navigation(MyAccountNavigation__mdt navigation) {
			this.target = navigation.Target__c;
			this.component = navigation.Component__c;
			this.label = navigation.Label__c;
			this.header = navigation.Header__c;
			this.subHeader = navigation.SubHeader__c;
			this.linkType = navigation.Type__c;
			this.icon = '/images/' + navigation.Icon__c;
			this.navbarVisible = navigation.IsNavBarVisible__c;
			this.sortOrder = navigation.SortOrder__c;
		}

		public Integer compareTo(Object objToCompare) {
			Navigation nav = (Navigation) objToCompare;
			if (sortOrder == nav.sortOrder) {
				return 0;
			} else if (sortOrder < nav.sortOrder) {
				return -1;
			} else {
				return 1;
			}
		}
	}

	global class UserModel {
		@AuraEnabled global String Id;
		@AuraEnabled global String firstName;
		@AuraEnabled global String lastName;
		@AuraEnabled global String email;
		@AuraEnabled global String selectedPersona;
		@AuraEnabled global String ticketureIdentityId;
		@AuraEnabled global List<String> availablePersonas;
		@AuraEnabled global Boolean isInternalUser;
		@AuraEnabled global List<AffiliationModel> affiliations;


		public UserModel() {
		}

		// Constructor for internal users (User object)
		public UserModel(User user) {
			this.Id = user.Id;
			this.firstName = user.FirstName;
			this.lastName = user.LastName;
			this.email = user.Email;
			this.selectedPersona = MyAccountConstants.USER_PERSONA_EDUCATOR;  // Not applicable for User
			this.isInternalUser = true;
			this.affiliations = new List<AffiliationModel>();
		}

		// Constructor for Experience users (Contact object)
		public UserModel(Contact contact, MyAccountAuthSession__c session) {
			this.Id = contact.Id;
			this.firstName = contact.FirstName;
			this.lastName = contact.LastName;
			this.email = contact.Email;
			this.selectedPersona = session.Persona__c;
			this.ticketureIdentityId = contact.tixtrack__TicketureIdentityId__c;
			this.availablePersonas = String.isNotBlank(contact.MyAccountPersona__c) ? contact.MyAccountPersona__c.split(';') : new List<String>();
			this.isInternalUser = false;
			this.affiliations = new List<AffiliationModel>();

			for (Affiliation__c aff : contact.Affiliations__r) {
				this.affiliations.add(new AffiliationModel(aff));
			}
		}

		public Account toPersonAccount() {
			Account pa = new Account();
			pa.RecordTypeId = AccountDomain.RT_PERSON_ACCOUNT;
			pa.FirstName = this.firstName;
			pa.LastName = this.lastName;
			pa.PersonEmail = this.email;
			pa.MyAccountPersona__pc = MyAccountConstants.USER_PERSONA_VISITOR;
			return pa;
		}

		/**
		 * @description Verifies that the desired persona is available for the given user
		 * @param desiredPersona
		 *
		 * @return
		 */
		public Boolean isValidPersona(String desiredPersona) {
			if (this.availablePersonas.contains(desiredPersona)) {
				return true;
			} else {
				throw new AuraHandledException('Persona not available.');
			}
		}
	}

	public class Persona {
		@AuraEnabled public String name;
		@AuraEnabled public String description;

		public Persona(Affiliation__c affiliation) {
			this.name = affiliation.Role__c; // TODO: not sure if these are right but mapping them for now
			this.description = affiliation.Title__c; // TODO: not sure if these are right but mapping them for now
		}

		public Persona(String name) {
			this.name = name;
		}
	}

	public class AuthenticationResponse {
		@AuraEnabled public String sessionToken;
		@AuraEnabled public Id contactId;
		@AuraEnabled public List<Persona> personas;
		@AuraEnabled public Boolean requirePersonaSelection {
			get {
				return personas != null ? personas.size() > 1 : false;
			}
		}

//        public AuthenticationResponse(String sessionToken, Id contactId, List<Affiliation__c> affiliations) {
//            this.sessionToken = sessionToken;
//            this.contactId = contactId;
//            this.personas = new List<Persona>();
//
//            for(Affiliation__c aff: affiliations) {
//                this.personas.add(new Persona(aff));
//            }
//        }
//
//        public AuthenticationResponse(String sessionToken, Contact c) {
//            this(sessionToken, c.Id, new List<Affiliation__c>());
//        }

		public AuthenticationResponse(String sessionToken, Contact c) {
			this.sessionToken = sessionToken;
			this.contactId = c.Id;
			this.personas = new List<Persona>();

			for (String persona : c.MyAccountPersona__c.split(';')) {
				this.personas.add(new Persona(persona));
			}
		}
	}

	global class AffiliationModel {
		private Affiliation__c affiliation;
		private Account organization;

		private String idValue;
		private String schoolNameValue;
		private String streetValue;
		private String cityValue;
		private String stateValue;
		private String countryValue;
		private String postalCodeValue;
		private String ncesValue;
		private String roleValue;
		private String statusValue;
		private String typeValue;
		private String emailValue;
		private String verificationStatusValue;

		@AuraEnabled global String id {
			get {
				return organization != null ? organization.Id : idValue;
			}
			set {
				idValue = value;
			}
		}


		@AuraEnabled global String name {
			get {
				return organization != null ? organization.Name : schoolNameValue;
			}
			set {
				schoolNameValue = value;
			}
		}

		@AuraEnabled global String street {
			get {
				return organization != null && organization.ShippingStreet != null
						? organization.ShippingStreet
						: streetValue;
			}
			set {
				streetValue = value;
			}
		}

		@AuraEnabled global String city {
			get {
				return organization != null && organization.ShippingCity != null
						? organization.ShippingCity
						: cityValue;
			}
			set {
				cityValue = value;
			}
		}

		@AuraEnabled global String state {
			get {
				return organization != null && organization.ShippingState != null
						? organization.ShippingState
						: stateValue;
			}
			set {
				stateValue = value;
			}
		}

		@AuraEnabled global String country {
			get {
				return organization != null && organization.ShippingCountryCode != null
						? organization.ShippingCountryCode
						: countryValue;
			}
			set {
				countryValue = value;
			}
		}

		@AuraEnabled global String postalCode {
			get {
				return organization != null && organization.ShippingPostalCode != null
						? organization.ShippingPostalCode
						: postalCodeValue;
			}
			set {
				postalCodeValue = value;
			}
		}

		@AuraEnabled global String address {
			get {
				String fullAddress = '';

				if (this.street != '') {
					fullAddress += this.street + ', ';
				}
				if (this.city != '') {
					fullAddress += this.city + ', ';
				}
				if (this.state != '') {
					fullAddress += this.state + ', ';
				}
				if (this.country != '') {
					fullAddress += this.country + ' ';
				}
				if (this.postalCode != '') {
					fullAddress += this.postalCode;
				}

				return fullAddress.trim().replaceAll(', $', '');
			}
		}

		@AuraEnabled global String nces {
			get {
				return ncesValue != null ? ncesValue : null;
			}
			set {
				ncesValue = value;
			}
		}

		@AuraEnabled global String role {
			get {
				return roleValue != null ? roleValue : (affiliation != null ? affiliation.Role__c : '');
			}
			set {
				roleValue = value;
			}
		}

		@AuraEnabled global String status {
			get {
				return statusValue != null ? statusValue : (affiliation != null ? affiliation.Status__c : '');
			}
			set {
				statusValue = value;
			}
		}

		@AuraEnabled global String email {
			get {
				return emailValue != null ? emailValue : (affiliation != null ? affiliation.Contact__r.Email : '');
			}
			set {
				emailValue = value;
			}
		}

		@AuraEnabled global String type {
			get {
				return typeValue != null ? typeValue : 'School';
			}
			set {
				typeValue = value;
			}
		}

		@AuraEnabled global String verificationStatus {
			get {
				return verificationStatusValue != null ? verificationStatusValue : (organization != null ? organization.VerificationStatus__c : 'Submitted for Verification');
			}

			set {
				verificationStatusValue = value;
			}
		}


		@AuraEnabled global Boolean suspendBooking {
			get {
				return affiliation != null ? affiliation.SuspendBooking__c : false;
			}
		}

		public AffiliationModel() {
		}

		public AffiliationModel(Affiliation__c aff) {
			this.affiliation = aff;
			this.organization = aff.Organization__r;
		}

		public Account toOrganization() {
			Account a = new Account();
			a.Name = this.name;
			a.ShippingStreet = this.street;
			a.ShippingCity = this.city;

			if (OrgInfoUtils.isStateAndCountryPicklistsEnabled()) {
				a.put('ShippingStateCode', this.state);
			} else {
				a.ShippingState = this.state;
			}

			if (OrgInfoUtils.isStateAndCountryPicklistsEnabled()) {
				a.put('ShippingCountryCode', this.country);
			} else {
				a.ShippingCountry = this.country;
			}

			a.ShippingPostalCode = this.postalCode;
			a.NCESId__c = this.nces;
			a.VerificationStatus__c = this.verificationStatus;

			return a;
		}

		public Affiliation__c toAffiliation(Id contactId, Id accountId) {
			Affiliation__c aff = new Affiliation__c();
			aff.Role__c = this.role;
			aff.Status__c = this.status;
			aff.Contact__c = contactId;
			aff.Organization__c = accountId;

			return aff;
		}
	}

	public class AffiliationRequest {
		@AuraEnabled public String role;
		@AuraEnabled public Id accountId;

		public Affiliation__c toAffiliation(Id contactId) {
			Affiliation__c aff = new Affiliation__c();
			aff.Role__c = this.role;
			aff.Status__c = 'Current';
			aff.Contact__c = contactId;
			aff.Organization__c = this.accountId;
			return aff;
		}
	}

	public class SchoolAffiliationRequest {
		@AuraEnabled public String role;
		@AuraEnabled public String schoolId;  // This could be either Salesforce Id or SchoolDigger Id
		@AuraEnabled public String schoolName;
		@AuraEnabled public String schoolType;
		@AuraEnabled public String schoolLevel;
		@AuraEnabled public String street;
		@AuraEnabled public String city;
		@AuraEnabled public String state;
		@AuraEnabled public String postalCode;
		@AuraEnabled public String ncesId;
		@AuraEnabled public Boolean isExternalSchool; // True if from SchoolDigger

		public Account toAccount() {
			Account school = new Account();
			school.Name = this.schoolName;
			school.ShippingStreet = this.street;
			school.ShippingCity = this.city;

			// Set state field dynamically based on picklist enablement
			if (OrgInfoUtils.isStateAndCountryPicklistsEnabled()) {
				school.put('ShippingStateCode', this.state);
				school.put('ShippingCountryCode', 'US'); // TODO: this probably needs to be dynamic at some point
			} else {
				school.ShippingState = this.state;
				school.ShippingCountry = 'United States'; // TODO: this probably needs to be dynamic at some point
			}

			school.ShippingPostalCode = this.postalCode;
			school.ShippingCountryCode = 'US'; // TODO: this probably needs to be dynamic at some point
			school.NCESId__c = this.ncesId;
			school.SchoolId__c = this.schoolId;
			school.RecordTypeId = AccountDomain.RT_ORG;
			school.Type = 'School';
			school.SchoolLevel__c = this.schoolLevel;
			school.SchoolType__c = this.schoolType;
			return school;
		}

		public Affiliation__c toAffiliation(Id contactId, Id accountId) {
			Affiliation__c aff = new Affiliation__c();
			aff.Role__c = this.role;
			aff.Status__c = 'Current';  // Default to Current
			aff.Contact__c = contactId;
			aff.Organization__c = accountId;
			return aff;
		}
	}

	public class SchoolSearchResult {
		// Salesforce Id or SchoolDigger Id
		@AuraEnabled public String schoolId;

		@AuraEnabled public String schoolType;
		@AuraEnabled public String schoolLevel;
		@AuraEnabled public String schoolName;
		@AuraEnabled public String city;
		@AuraEnabled public String state;
		@AuraEnabled public String zip;
		@AuraEnabled public String ncesId;

		// true if from Salesforce
		@AuraEnabled public Boolean isExisting;

		// Constructor for SchoolDigger results
		public SchoolSearchResult(SchoolDiggerModels.SchoolMatch match) {
			this.schoolId = match.schoolid;
			this.schoolName = match.schoolName;
			this.city = match.city;
			this.state = match.state;
			this.zip = match.zip;
			this.ncesId = match.ncesPrivateSchoolID;
			this.schoolType = match.parsedSchoolType;
			this.schoolLevel = match.parsedSchoolLevel;
			this.isExisting = false;
		}

		// Constructor for Salesforce Account results
		public SchoolSearchResult(Account acc) {
			this.schoolId = acc.Id;
			this.schoolName = acc.Name;
			this.city = acc.ShippingCity;

			// Get state value based on picklist enablement
			if (OrgInfoUtils.isStateAndCountryPicklistsEnabled()) {
				this.state = (String) acc.get('ShippingStateCode');
			} else {
				this.state = acc.ShippingState;
			}

			this.zip = acc.ShippingPostalCode;
			this.ncesId = acc.NCESId__c;
			this.schoolType = acc.SchoolType__c;
			this.schoolLevel = acc.SchoolLevel__c;
			this.isExisting = true;
		}

		public SchoolSearchResult() {
		}
	}

	public class TableAction {
		@AuraEnabled
		public String label { get; set; }
		@AuraEnabled
		public String name { get; set; }
		@AuraEnabled
		public String type { get; set; }
		@AuraEnabled
		public String iconName { get; set; }

		public TableAction(String label, String name, String type, String iconName) {
			this.label = label;
			this.name = name;
			this.type = type;
			this.iconName = iconName;
		}
	}
}
