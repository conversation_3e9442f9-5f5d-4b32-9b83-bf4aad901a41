/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * MyAccountNavigationService
 * @description: 
 * @author: <PERSON><PERSON>
 * @date: 2/15/25
 */

public with sharing class MyAccountNavigationService {

    /**
     * Provider interface for navigation metadata
     * Allows for dependency injection in tests
     */
    public interface NavigationProvider {
        List<MyAccountNavigation__mdt> getNavigationItems(String persona);
        }

    /**
     * Default implementation of NavigationProvider
     * Retrieves actual navigation metadata from the org
     */
    private class DefaultNavigationProvider implements NavigationProvider {
        public List<MyAccountNavigation__mdt> getNavigationItems(String persona) {
            // In the real implementation, we'd query for navigation items based on persona
            // This is a simplified version that matches the existing implementation
            return [
                SELECT Id,
                        Icon__c,
                        Label__c,
                        Type__c,
                        IsNavbarVisible__c,
                        Header__c,
                        SubHeader__c,
                        Target__c,
                        Component__c,
                        SortOrder__c
                FROM MyAccountNavigation__mdt
                WHERE Id IN :getAvailableNavigationIds(persona)
                ORDER BY SortOrder__c ASC
        ];
        }

        private Set<Id> getAvailableNavigationIds(String persona) {
            // Get the user persona record
            MyAccountUserPersona__mdt userPersona = getUserPersona(persona);

            // Get navigation items for this persona
            Set<Id> availableNavs = new Set<Id>();
            List<MyAccountUserPersonaNavigation__mdt> userPersonaNavs = [
                    SELECT Navigation__c
                    FROM MyAccountUserPersonaNavigation__mdt
                    WHERE UserPersona__c = :userPersona.Id
                    AND Active__c = TRUE
            ];

            for (MyAccountUserPersonaNavigation__mdt utn : userPersonaNavs) {
                if (String.isNotBlank(utn.Navigation__c)) {
                    availableNavs.add(utn.Navigation__c);
                }
            }

            return availableNavs;
        }

        private MyAccountUserPersona__mdt getUserPersona(String persona) {
            Map<String, MyAccountUserPersona__mdt> userPersonaMap = getUserPersonaMap();
            MyAccountUserPersona__mdt userPersona = userPersonaMap.get(persona);

            if (userPersona == null) {
                userPersona = userPersonaMap.get(MyAccountConstants.USER_PERSONA_VISITOR);
            }

            return userPersona;
        }
    }

    /**
     * Provider instance - can be overridden in tests
     */
    @TestVisible
    private static NavigationProvider navigationProvider = new DefaultNavigationProvider();

    @AuraEnabled(Cacheable=true)
    public static List<MyAccountMsgs.Navigation> getNavigationRecords(String sessionToken) {
        MyAccountMsgs.UserModel user = MyAccountUserService.getCurrentUser(sessionToken);
        Map<String, MyAccountUserPersona__mdt> userPersonaMap = getUserPersonaMap();
        MyAccountUserPersona__mdt userPersona = userPersonaMap.get(user.selectedPersona);

        if (userPersona == null) {
            userPersona = userPersonaMap.get(MyAccountConstants.USER_PERSONA_VISITOR);
        }

        List<MyAccountMsgs.Navigation> navigations = new List<MyAccountMsgs.Navigation>();

        // Use the provider to get navigation items
        List<MyAccountNavigation__mdt> navigationRecords = navigationProvider.getNavigationItems(user.selectedPersona);

        for (MyAccountNavigation__mdt navigation : navigationRecords) {
            navigations.add(new MyAccountMsgs.Navigation(navigation));
        }

        return navigations;
    }

    /**
     * @description Helper function
     * to get us a map of the MyAccountUserPersona__mdt
     * CMDT records
     * @return
     */
    private static Map<String, MyAccountUserPersona__mdt> getUserPersonaMap() {
        Map<String, MyAccountUserPersona__mdt> userPersonaMap = new Map<String, MyAccountUserPersona__mdt>();
        for (MyAccountUserPersona__mdt userPersona : [
                SELECT Id, MasterLabel
                FROM MyAccountUserPersona__mdt
        ]) {
            userPersonaMap.put(userPersona.MasterLabel, userPersona);
        }
        return userPersonaMap;
    }
}