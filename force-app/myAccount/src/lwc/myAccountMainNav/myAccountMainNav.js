/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * myAccountMainNav
 * @description:
 * @author: <PERSON><PERSON>
 * @date: 2/15/25
 */
import {api, LightningElement, wire} from 'lwc';
import {NavigationMixin} from "lightning/navigation";
import resources from '@salesforce/resourceUrl/MyAccountResources';
import {APPLICATION_SCOPE, MessageContext, subscribe, unsubscribe} from "lightning/messageService";
import {Linktypes} from "c/myAccountConstants";
import {classSet} from "c/utils";
import navigation from '@salesforce/messageChannel/NavigationChannel__c';

export default class MyAccountMainNav extends NavigationMixin(LightningElement) {
    @api menuItems;
    @api selectedPage;

    subscription = null;

    error;
    expanded = false;

    @wire(MessageContext)
    messageContext;

    get navStyles() {
        return classSet(
            'background--gray nav-sticky transition-25-min-width adjusted-height-100 border-radius--small'
        )
            .add({
                'nav-width': this.expanded,
                'mini-nav-width': !this.expanded
            })
            .toString();
    }

    get logoutPage() {
        return {
            target: 'logout',
            label:'Logout',
            subHeader: 'Logout',
            pageRefType: 'InternalLink',
            linkType: Linktypes.Link,
            icon: resources + '/images/box-arrow-left.svg',
            navbarVisible: true,
        }
    }

    connectedCallback() {
        this.subscribeToMessageChannel();
    }

    disconnectedCallback() {
        this.unsubscribeToMessageChannel();
    }

    handleToggleExpanded() {
        this.expanded = !this.expanded;
    }

    handleSelection(event) {
        if(!this.expanded && (!event.detail.pageNavigation)) {
            this.handleToggleExpanded();
        }
    }

    handleMouseOver() {
        this.expanded = true;
    }

    handleMouseLeave() {
        this.expanded = false;
    }

    subscribeToMessageChannel() {
        if (!this.subscription) {
            this.subscription = subscribe(
                this.messageContext,
                navigation,
                (message) => this.handleSelection(message),
                { scope: APPLICATION_SCOPE }
            );
        }
    }

    unsubscribeToMessageChannel() {
        unsubscribe(this.subscription);
        this.subscription = null;
    }
}