/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * myAccountNavigation
 * @description:
 * @author: <PERSON><PERSON>
 * @date: 2/20/25
 */

import { createMessageContext } from 'lightning/messageService';
import {publish} from "lightning/messageService";
import navigate from '@salesforce/messageChannel/NavigationChannel__c';

/**
 * Navigates to a specific page
 * @param {String} target - The target page
 * @param {Object} params - Additional parameters for the navigation
 */
const navigateTo = (target, params) => {
    let messageContext = createMessageContext();

    let page = {
        target: target,
        pageNavigation: true
    };

    if(params) {
        page = {
            ...page,
            ...params
        }
    }

    publish(messageContext, navigate, {detail: page});
}

/**
 * Navigates using a page reference object
 * @param {Object} pageRef - The page reference object
 */
const navigateToPageRef = (pageRef) => {
    let messageContext = createMessageContext();
    pageRef.pageNavigation = true;

    if(pageRef) {
        publish(messageContext, navigate, {detail: pageRef});
    }
}

/**
 * Sets up a custom event listener for navigation events
 * @param {HTMLElement} element - The element to attach the listener to
 * @param {Function} callback - Optional callback function to execute after navigation
 * @returns {Function} - Function to remove the event listener
 */
const setupNavigationEventListener = (element, callback) => {
    const handleNavigationEvent = (event) => {
        const { target, params } = event.detail;
        navigateTo(target, params);

        if (typeof callback === 'function') {
            callback(event.detail);
        }
    };

    // Add the event listener
    element.addEventListener('navigate', handleNavigationEvent);

    // Return a function to remove the listener
    return () => {
        element.removeEventListener('navigate', handleNavigationEvent);
    };
}

export { navigateTo, navigateToPageRef, setupNavigationEventListener }
