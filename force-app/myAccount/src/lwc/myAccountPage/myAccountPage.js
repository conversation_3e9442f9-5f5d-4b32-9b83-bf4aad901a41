/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * myAccountPage
 * @description:
 * @author: <PERSON><PERSON>
 * @date: 2/20/25
 */
import {api, LightningElement} from 'lwc';
import AuthUtils from "c/myAccountAuthUtils";

export default class MyAccountPage extends LightningElement {
    _currentPage;
    _currentUser;
    sessionToken;

    rendered = false;

    constructor() {
        super();
        this.sessionToken = AuthUtils.getSessionToken();

        if (!this.sessionToken) {
            console.error('No session token found.');
        }
    }

    @api
    get currentPage() {
        return this._currentPage;
    }
    set currentPage(value) {
        this._currentPage = value;
    }

    @api
    get currentUser() {
        return this._currentUser;
    }
    set currentUser(value) {
        if(value) {
            this._currentUser = value;
            this.rendered = true;
        }
    }

    get pageMainHeader() {
        if(this.currentPage) {
            return this.currentPage.header ? this.currentPage.header : this.currentPage.label;
        }
        return null;
    }

    get pageSubHeader() {
        if(this.currentPage) {
            return this.currentPage.subHeader;
        }
        return null;
    }
}