<!--
  ~ Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
  ~
  ~ The code below is part of the Foglight Arts & Culture package and is not
  ~ to be redistributed without express written consent by Foglight.
  -->

<!--
 * myAccountTable
 * @description: 
 * @author: <PERSON><PERSON>
 * @date: 3/21/25
 -->

<!-- My Account Table -->
<template>
    <div class={tableContainerParentClasses}>
        <div class={tableContainerClasses}>
            <div class="full-width">
                <c-my-account-loading show={loading}></c-my-account-loading>
            </div>

            <!-- Empty state message when no records -->
            <template if:true={showEmptyState}>
                <div class="flex flex-direction-column justify-content-center align-items-center full-width min-height-medium background--white">
                    <div class="text-align-center ptl pbl pll prl background--gray">
                        <template if:true={emptyStateIcon}>
                            <c-my-account-icon
                                icon-name={emptyStateIcon}
                                width="48"
                                height="48"
                                class-name="empty-state-icon">
                            </c-my-account-icon>
                        </template>
                        <h3 class={emptyStateTitleClass}>{emptyStateTitle}</h3>
                        <p class="p mts">{emptyStateMessage}</p>
                    </div>
                </div>
            </template>

            <!-- Table with records -->
            <template if:false={showEmptyState}>
            <table lwc:ref="table"
                   class={tableClasses}
                   onscroll={handleScroll}>
                <thead>
                <tr class="full-width align-items-center relative overflow-visible background--white top-sticky">
                    <template for:each={columns} for:item="column">
                        <c-my-account-table-header-column
                                class={column.cellAttributes.styles}
                                key={column.fieldName}
                                column={column}
                                sort-arrow={sortArrow}
                                sort-direction={queryParams.sortDirection}
                                sorted-by={queryParams.sortedBy}
                                onsort={handleSort}
                                onrowhover={setArrow}
                                onrowexit={setArrow}>
                        </c-my-account-table-header-column>
                    </template>
                </tr>
                </thead>
                <tbody>
                <template for:each={records} for:item="row">
                    <c-my-account-table-row
                            key={row.Id}
                            class={rowClasses}
                            columns={columns}
                            record={row}
                            draft-values={draftValues}
                            oncellchange={handleCellChange}>
                    </c-my-account-table-row>
                </template>
                </tbody>
            </table>
            </template>
        </div>
    </div>
</template>