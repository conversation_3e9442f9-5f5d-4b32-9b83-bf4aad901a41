/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * myAccountConstants
 * @description:
 * @author: <PERSON><PERSON>
 * @date: 2/16/25
 */
class MyAccountConstants {

}

const getActionMessages = () =>{
    return {
        EXPAND_ACTION: 'expand',
        COLLAPSE_ACTION:  'collapse',
        SCROLL_TOP: 'scroll-top'
    }
};

const getPages = () => {
    return {
        HOME: 'Home',
        MY_ACCOUNT: 'myaccount',
        MY_SCHOOLS: 'myschools',
        PREFERENCES: 'preferences',
        MEMBERSHIP: 'membership',
        DONATIONS: 'donations',
        ORDERS_TICKETS: 'orderstickets',
        TAX_DOCUMENTS: 'taxdocuments',
        LOGIN: 'Login'
    }
};

const getEvents = () =>{
    return {
        CLOSE_EVT: 'close',
        REFRESH_EVT: 'refresh',
        SUBMIT_EVT: 'submit'
    }
};

const getRenderMessages = () =>{
    return {
        MULTI_TAB_RENDERED: 'multi-tab-rendered',
        SECTION_POPUP_RENDERED: 'section-popup-rendered',
    }
};

const Linktypes = {
    Dropdown: 'dropdown',
    SubItem: 'subitem',
    Link: 'link'
};

const US_STATES = [
    { label: 'Alabama', value: 'AL' },
    { label: 'Alaska', value: 'AK' },
    { label: 'Arizona', value: 'AZ' },
    { label: 'Arkansas', value: 'AR' },
    { label: 'California', value: 'CA' },
    { label: 'Colorado', value: 'CO' },
    { label: 'Connecticut', value: 'CT' },
    { label: 'Delaware', value: 'DE' },
    { label: 'Florida', value: 'FL' },
    { label: 'Georgia', value: 'GA' },
    { label: 'Hawaii', value: 'HI' },
    { label: 'Idaho', value: 'ID' },
    { label: 'Illinois', value: 'IL' },
    { label: 'Indiana', value: 'IN' },
    { label: 'Iowa', value: 'IA' },
    { label: 'Kansas', value: 'KS' },
    { label: 'Kentucky', value: 'KY' },
    { label: 'Louisiana', value: 'LA' },
    { label: 'Maine', value: 'ME' },
    { label: 'Maryland', value: 'MD' },
    { label: 'Massachusetts', value: 'MA' },
    { label: 'Michigan', value: 'MI' },
    { label: 'Minnesota', value: 'MN' },
    { label: 'Mississippi', value: 'MS' },
    { label: 'Missouri', value: 'MO' },
    { label: 'Montana', value: 'MT' },
    { label: 'Nebraska', value: 'NE' },
    { label: 'Nevada', value: 'NV' },
    { label: 'New Hampshire', value: 'NH' },
    { label: 'New Jersey', value: 'NJ' },
    { label: 'New Mexico', value: 'NM' },
    { label: 'New York', value: 'NY' },
    { label: 'North Carolina', value: 'NC' },
    { label: 'North Dakota', value: 'ND' },
    { label: 'Ohio', value: 'OH' },
    { label: 'Oklahoma', value: 'OK' },
    { label: 'Oregon', value: 'OR' },
    { label: 'Pennsylvania', value: 'PA' },
    { label: 'Rhode Island', value: 'RI' },
    { label: 'South Carolina', value: 'SC' },
    { label: 'South Dakota', value: 'SD' },
    { label: 'Tennessee', value: 'TN' },
    { label: 'Texas', value: 'TX' },
    { label: 'Utah', value: 'UT' },
    { label: 'Vermont', value: 'VT' },
    { label: 'Virginia', value: 'VA' },
    { label: 'Washington', value: 'WA' },
    { label: 'West Virginia', value: 'WV' },
    { label: 'Wisconsin', value: 'WI' },
    { label: 'Wyoming', value: 'WY' }
];

const COUNTRIES = [
    { label: 'United States', value: 'US' },
    { label: 'Canada', value: 'CA' },
    { label: 'Mexico', value: 'MX' },
    { label: 'United Kingdom', value: 'GB' },
    { label: 'France', value: 'FR' },
    { label: 'Germany', value: 'DE' },
    { label: 'Italy', value: 'IT' },
    { label: 'Spain', value: 'ES' },
    { label: 'Australia', value: 'AU' },
    { label: 'New Zealand', value: 'NZ' },
    { label: 'China', value: 'CN' },
    { label: 'Japan', value: 'JP' },
    { label: 'South Korea', value: 'KR' },
    { label: 'India', value: 'IN' },
    { label: 'Brazil', value: 'BR' },
    { label: 'Argentina', value: 'AR' },
    { label: 'South Africa', value: 'ZA' },
    { label: 'Nigeria', value: 'NG' },
    { label: 'Egypt', value: 'EG' },
    { label: 'Israel', value: 'IL' }
];

export {
    getActionMessages,
    getRenderMessages,
    getPages,
    getEvents,
    Linktypes,
    US_STATES,
    COUNTRIES
};