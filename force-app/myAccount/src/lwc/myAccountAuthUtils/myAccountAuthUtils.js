/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Fog<PERSON>.
 */

/**
 * Authentication utilities for handling session management
 */

// TODO: Temporarily commenting this as it's causing issues
// import refreshSession from '@salesforce/apex/MyAccountUserService.refreshSession';

const COOKIE_CONFIG = {
    SESSION_TOKEN: 'sessionToken',
    PATH: '/',
    SECURE: true,
    SAME_SITE: 'Strict'
};

export default class AuthUtils {
    /**
     * Creates a new cookie with the specified parameters
     * @private
     * @param {string} name - Cookie name
     * @param {string} value - Cookie value
     * @param {Object} options - Cookie options (expires, path, etc.)
     */
    static _setCookie(name, value, options = {}) {
        const cookieString = `${name}=${value}; Path=${COOKIE_CONFIG.PATH}`;
        const secureFlag = COOKIE_CONFIG.SECURE ? '; Secure' : '';
        const sameSiteFlag = COOKIE_CONFIG.SAME_SITE ? `; SameSite=${COOKIE_CONFIG.SAME_SITE}` : '';
        const expiresFlag = options.expires ? `; Expires=${options.expires}` : '';

        document.cookie = cookieString + secureFlag + sameSiteFlag + expiresFlag;
    }

    /**
     * Retrieves a cookie value by name, handling LSKey prefixes
     * @private
     * @param {string} name - Cookie name to find
     * @returns {string|null} Cookie value if found, null otherwise
     */
    static _getCookie(name) {
        const cookies = document.cookie.split('; ');
        const regex = new RegExp(`(?:LSKey-[^$]+\\$)?(${name})=([^;]+)`);

        for (let cookie of cookies) {
            const match = cookie.match(regex);
            if (match) {
                return match[2];
            }
        }
        return null;
    }

    /**
     * Finds all cookies matching a pattern
     * @private
     * @param {RegExp} pattern - Pattern to match cookie names against
     * @returns {Array<string>} Array of matching cookie names
     */
    static _findCookies(pattern) {
        const cookies = document.cookie.split('; ');
        return cookies
            .filter(cookie => cookie.match(pattern))
            .map(cookie => cookie.split('=')[0]);
    }

    /**
     * Retrieves the current session token
     * @returns {string|null} Session token if exists, null otherwise
     */
    static getSessionToken() {
        return this._getCookie(COOKIE_CONFIG.SESSION_TOKEN);
    }

    /**
     * Attempts to refresh the user session
     * @returns {Promise<void>}
     */
    static async refreshUserSession() {
        const token = this.getSessionToken();
        if (!token) return;

        try {
            // const response = await refreshSession({sessionToken: token});
            if (response?.sessionToken) {
                this._setCookie(COOKIE_CONFIG.SESSION_TOKEN, response.sessionToken);
            }
        } catch (error) {
            console.error('Failed to refresh session:', error);
        }
    }

    /**
     * Logs out the user by clearing all session-related cookies
     */
    static logout() {
        const sessionCookies = this._findCookies(
            new RegExp(`(?:LSKey-[^$]+\\$)?${COOKIE_CONFIG.SESSION_TOKEN}`)
        );

        const pastDate = new Date(0).toUTCString();

        sessionCookies.forEach(cookieName => {
            this._setCookie(cookieName, '', { expires: pastDate });
        });
    }
}