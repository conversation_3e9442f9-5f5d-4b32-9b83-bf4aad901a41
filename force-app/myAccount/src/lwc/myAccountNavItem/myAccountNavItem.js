/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * myAccountNavItem
 * @description:
 * @author: <PERSON><PERSON>
 * @date: 2/20/25
 */
import { LightningElement, api, track, wire } from 'lwc';
import { NavigationMixin } from 'lightning/navigation';
import { classSet } from "c/utils";
import { publish, MessageContext } from 'lightning/messageService';
import { getPages } from 'c/myAccountConstants';
import navigation from '@salesforce/messageChannel/NavigationChannel__c';
import AuthUtils from "c/myAccountAuthUtils";
import { navigateTo } from "c/myAccountNavigation";
const PAGES = getPages();

export default class MyAccountNavItem extends NavigationMixin(LightningElement) {
    @wire(MessageContext)
    messageContext;

    /**
     * @description: holds the actual page
     * the is currently selected
     * selected
     * @private
     */
    @track _item;

    /**
     * @description: holds the top level base page that this
     * nav item represents.
     * @type {{}}
     */
    @api item = {};

    _expanded;
    selectedPageTarget;

    @api
    get selectedPage() {
        return this.selectedPageTarget;
    }
    set selectedPage(page) {
        if(page) {
            this.selectedPageTarget = page.target;
        }
    }

    @api
    get expanded() {
        return this._expanded;
    }
    set expanded(value) {
        this._expanded = value;
    }

    get selected() {
        return this.selectedPage === this._item?.target;
    }

    get linkImageStyles() {
        return classSet(
            "image"
        )
            .add({
                'selected-img': this.selected
            })
            .toString();
    }

    get linkTextStyles() {
        return classSet(
            "text text-no-wrap"
        )
            .add({
                'selected-text': this.selected
            })
            .toString();
    }

    get computedContainerClass() {
        return classSet(
            "sizing"
        )
            .add({
                "hover": this.expanded,
                'selected-nav-item': this.selected
            })
            .toString();
    }

    connectedCallback() {
        if(!this._item) {
            this._item = this.item;
        }
    }

    handleNavigation(evt) {
        evt.preventDefault();
        evt.stopPropagation();

        this._item = this.item;

        if (this._item.target === 'logout') {
            this.handleLogout();
            return;
        }

        this.navigate();
    }

    handleLogout() {
        AuthUtils.logout();
        navigateTo(PAGES.LOGIN, this.messageContext);
    }

    navigate() {
        publish(
            this.messageContext,
            navigation,
            {detail: this._item}
        );
    }
}