/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * myAccountSelectableTile
 * @description:
 * @author: <PERSON><PERSON>
 * @date: 3/13/25
 */
import {track, api, LightningElement} from 'lwc';
import resources from "@salesforce/resourceUrl/MyAccountResources";
import {classSet} from "c/utils";

export default class MyAccountSelectableTile extends LightningElement {
    @track _record;
    @api icon;

    @api
    get record() {
        return this._record;
    }
    set record(value) {
        console.log(JSON.stringify(value));
        this._record = value;
    }

    get iconUrl() {
        return resources + '/images/' + this.icon;
    }

    get tileClasses() {
        return classSet(
            'flex flex-gap-sp pll prl ptr pbr pointer border border-radius--small'
        )
            .add({
                'background--gray': this._record?.selected,
                'background--white': !this._record?.selected,
            })
            .toString();
    }

    handleSelect() {
        this.dispatchEvent(
            new CustomEvent(
                'select',
                {
                    detail: this.record,
                    bubbles: true,
                    composed: true
                }
            )
        )
    }
}