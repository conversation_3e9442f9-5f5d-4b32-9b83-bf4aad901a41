/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

@import "c/myAccountGlobalStyles";

.checkbox-group {
    margin-bottom: var(--sp);
}

.checkbox-options {
    display: flex;
    flex-direction: column;
    gap: var(--xsp);
}

.checkbox-option {
    display: flex;
    align-items: center;
    gap: var(--xsp);
}

.option-label {
    margin-bottom: 0;
    cursor: pointer;
}

.error-message {
    color: var(--danger-color);
    font-size: var(--sfs);
    margin-top: var(--xsp);
}

.help-text {
    color: var(--text-color-light);
    font-size: var(--sfs);
    margin-top: var(--xsp);
}

input[type="radio"] {
    appearance: none;
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    border: 1px solid var(--border-color);
    border-radius: 50%;
    background-color: var(--white);
    cursor: pointer;
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

input[type="radio"]:checked {
    background-color: var(--white);
    border-color: var(--green);
}

input[type="radio"]:checked::after {
    content: "";
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: var(--green);
    position: absolute;
}

/* Custom checkbox styling */
input[type="checkbox"] {
    appearance: none;
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    border: 1px solid var(--border-color);
    border-radius: 3px;
    background-color: var(--white);
    cursor: pointer;
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

input[type="checkbox"]:checked {
    background-color: var(--green);
    border-color: var(--green);
}

input[type="checkbox"]:checked::after {
    content: "";
    width: 5px;
    height: 10px;
    border-right: 2px solid var(--white);
    border-bottom: 2px solid var(--white);
    transform: rotate(45deg);
    position: absolute;
    top: 2px;
}