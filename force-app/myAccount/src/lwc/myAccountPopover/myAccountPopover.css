/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * myAccountPopover
 * @description: 
 * @author: <PERSON><PERSON>
 * @date: 3/26/25
 */
@import 'c/myAccountGlobalStyles';


.layout {
    right: 0;
    box-sizing: border-box;
}

.layout > :first-child {
    flex-grow: 1;
    overflow: auto;
}

.hidden,
.reveal {
    position: fixed;
    top: 0;
    height: 100vh;
    width: 50vw;
    z-index: 9988;
    transition: right 0.5s ease-in-out;
    overflow: auto;
    background-color: var(--white);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;
    border-left: 1px solid var(--border-outline-color);
}

.hidden {
    right: -50vw;
}

.reveal {
    right: 0;
}

.image {
    cursor: pointer;
}

.layout2 {
    width: fit-content;
    min-width: 550px;
    border: 1px solid var(--border-color);
    border-image: initial;
    padding: var(--mp);
    background-color: var(--background-color-white);
    z-index: 9998;
    border-radius: var(--border-radius-medium);
    box-shadow: var(--modal-box-shadow);
}

.content-positioning {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.attached-footer {
    position: relative;
    width: 100%;
    min-width: 550px;
    border-width: 1px;
    border-style: solid;
    border-color: var(--border-color);
    border-image: initial;
    padding: var(--mp);
    margin-top: var(--rp);
    background-color: var(--background-color-white);
    z-index: 9998;
    border-radius: var(--border-radius-medium);
    box-shadow: var(--modal-box-shadow);
}

.overlay2 {
    z-index: 9997;
    width: 100%;
    height: 100%;
}

.reveal2 {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--background-color-overlay);
    cursor: default;
    z-index: 9999;
}