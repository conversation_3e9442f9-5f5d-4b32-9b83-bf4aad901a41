/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

@import 'c/myAccountGlobalStyles';

.button {
    /* Typography */
    font-family: var(--button-font-family);
    font-size: var(--button-font-size);
    font-style: var(--button-font-style);
    font-weight: var(--button-font-weight);
    line-height: var(--button-line-height);
    letter-spacing: var(--button-letter-spacing);
    text-decoration: var(--button-text-decoration);
    text-transform: var(--button-text-transform);

    /* Sizing and spacing */
    padding-block-start: var(--button-padding-block-start);
    padding-block-end: var(--button-padding-block-end);
    padding-inline-start: var(--button-padding-inline-start);
    padding-inline-end: var(--button-padding-inline-end);
    border-radius: var(--button-standard-radius);

    /* Default styling */
    min-width: 120px;
    border: 1px solid var(--button-color-border);
    background: var(--button-color-background);
    color: var(--button-color-text);
    transition: all 0.2s ease;
    outline: none;
}

.button:hover {
    border-color: var(--button-color-border-hover);
    background: var(--button-color-background-hover);
    color: var(--button-color-text-hover);
}

.button:focus-visible {
    border-color: var(--button-color-border-focus);
    background: var(--button-color-background-focus);
    color: var(--button-color-text-focus);
    box-shadow: var(--button-box-shadow);
}

/* Primary Button */
.button.variant-brand {
    background: var(--button-brand-color-background);
    border-color: var(--button-brand-color-border);
    color: var(--button-brand-color-text);
}

.button.variant-brand:hover {
    background: var(--button-brand-color-background-hover);
    border-color: var(--button-brand-color-border-hover);
    color: var(--button-brand-color-text-hover);
}

.button.variant-brand:focus-visible {
    background: var(--button-brand-color-background-focus);
    border-color: var(--button-brand-color-border-focus);
    color: var(--button-brand-color-text-focus);
}

/* Destructive Button */
.button.variant-destructive {
    background: var(--button-destructive-color-background);
    border-color: var(--button-destructive-color-border);
    color: var(--button-destructive-color-text);
}

.button.variant-destructive:hover {
    background: var(--button-destructive-color-background-hover);
    border-color: var(--button-destructive-color-border-hover);
    color: var(--button-destructive-color-text-hover);
}

.button.variant-destructive:focus-visible {
    background: var(--button-destructive-color-background-focus);
    border-color: var(--button-destructive-color-border-focus);
    color: var(--button-destructive-color-text-focus);
}

/* Button Sizes */
.button.size-small {
    font-size: var(--button-small-font-size);
    padding-block-start: var(--button-small-padding-block-start);
    padding-block-end: var(--button-small-padding-block-end);
    padding-inline-start: var(--button-small-padding-inline-start);
    padding-inline-end: var(--button-small-padding-inline-end);
    border-radius: var(--button-small-radius);
    min-width: 80px;
}

.button.size-large {
    font-size: var(--button-large-font-size);
    padding-block-start: var(--button-large-padding-block-start);
    padding-block-end: var(--button-large-padding-block-end);
    padding-inline-start: var(--button-large-padding-inline-start);
    padding-inline-end: var(--button-large-padding-inline-end);
    border-radius: var(--button-large-radius);
    min-width: 160px;
}

.button.disabled {
    opacity: 0.2;
    pointer-events: none;
}

@media (max-width: 768px) {
    .button {
        min-width: 100px;
    }

    .button.size-small {
        min-width: 70px;
    }

    .button.size-large {
        min-width: 130px;
    }
}