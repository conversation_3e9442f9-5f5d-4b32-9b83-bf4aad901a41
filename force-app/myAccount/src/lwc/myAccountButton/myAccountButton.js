/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * myAccountButton
 * @description:
 * @author: <PERSON><PERSON>
 * @date: 3/31/25
 */
import { LightningElement, api } from 'lwc';
import resources from "@salesforce/resourceUrl/MyAccountResources";
import {normalizeBoolean} from "c/utilsPrivate";

export default class MyAccountButton extends LightningElement {
    @api label;
    @api variant = 'neutral';
    @api loading = false;
    @api iconName;
    @api iconPosition = 'left';
    @api type = 'button';
    @api size = 'standard'; // 'small', 'standard', or 'large'

    _disabled;

    /**
     * Gets the disabled state of the popover
     * @returns {boolean} Current disabled state
     */
    @api
    get disabled() {
        return this._disabled;
    }

    /**
     * Sets the disabled state of the popover
     * @param {boolean} value - The disabled state to set
     */
    set disabled(value) {
        this._disabled = normalizeBoolean(value);
    }

    get iconUrl() {
        return `${resources}/images/${this.iconName}`;
    }

    /**
     * Computes CSS classes for the button element
     * Combines base classes with conditional classes based on variant and state
     * @returns {string} Space-separated list of CSS classes
     */
    get computedButtonClass() {
        const baseClasses = [
            'button',
            'border-box',
            'flex',
            'center'
        ];
        
        if (this.variant) {
            baseClasses.push(`variant-${this.variant}`);
        }
        
        if (this.size === 'small') {
            baseClasses.push('size-small');
        } else if (this.size === 'large') {
            baseClasses.push('size-large');
        }

        if (this.disabled) {
            baseClasses.push('disabled', 'cursor-default');
        } else {
            baseClasses.push('pointer');
        }

        return baseClasses.join(' ');
    }

    /**
     * Computes CSS classes for the button content wrapper
     * @returns {string} Space-separated list of CSS classes for content layout
     */
    get buttonContentClass() {
        return `button-content flex center flex-gap-xsp`;
    }

    /**
     * Determines the appropriate spinner variant based on button variant
     * @returns {string} 'inverse' for brand/destructive variants, 'brand' for others
     */
    get spinnerVariant() {
        return this.variant === 'brand' || this.variant === 'destructive' ? 'inverse' : 'brand';
    }

    /**
     * Determines if the icon should be shown on the left side
     * @returns {boolean} True if iconName is set and position is 'left'
     */
    get showLeftIcon() {
        return this.iconName && this.iconPosition === 'left';
    }

    /**
     * Determines if the icon should be shown on the right side
     * @returns {boolean} True if iconName is set and position is 'right'
     */
    get showRightIcon() {
        return this.iconName && this.iconPosition === 'right';
    }

    /**
     * Handles button click events
     * Prevents default behavior if button is disabled or loading
     * @param {Event} event - The click event object
     */
    handleClick(event) {
        event.preventDefault();
        event.stopPropagation();
        event.stopImmediatePropagation();

        if (this.disabled || this.loading) {
            return;
        }

        this.dispatchEvent(new CustomEvent('click', {
            bubbles: true,
            composed: true
        }));
    }
}