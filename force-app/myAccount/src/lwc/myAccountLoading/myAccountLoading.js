/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * myAccountLoading
 * @description:
 * @author: <PERSON><PERSON>
 * @date: 3/24/25
 */
import {api, LightningElement} from 'lwc';
import { classSet } from 'c/utils';

export default class MyAccountLoading extends LightningElement {
    @api show = false;
    @api label;
    @api size;
    @api styleClasses;

    get hideRevealClass() {
        return classSet(
            this.styleClasses ? this.styleClasses : ``
        )
            .add({
                'reveal': this.show,
                'hidden' : !this.show,
            })
            .toString();
    }
}