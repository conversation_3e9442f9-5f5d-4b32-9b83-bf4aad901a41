/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * myAccountIcon
 * @description: Reusable component for displaying icons from the MyAccount static resource
 * @author: <PERSON><PERSON>
 * @date: 5/15/25
 */
import { LightningElement, api } from 'lwc';
import resources from "@salesforce/resourceUrl/MyAccountResources";
import { classSet } from 'c/utils';

export default class MyAccountIcon extends LightningElement {
    /**
     * The name of the icon to display
     * Can be just the filename (e.g., "edit") or a path (e.g., "actions/edit")
     */
    @api iconName;
    
    /**
     * Alternative text for the icon (for accessibility)
     */
    @api alternativeText;
    
    /**
     * Title attribute for the icon (displays on hover)
     */
    @api title;
    
    /**
     * Size of the icon in pixels (sets both width and height)
     * @default 20
     */
    @api size;

    /**
     * Width of the icon in pixels
     * @default 20
     */
    @api width = '20';
    
    /**
     * Height of the icon in pixels
     * @default 20
     */
    @api height = '20';
    
    /**
     * CSS class to apply to the icon
     */
    @api className;
    
    /**
     * Whether the icon should be clickable
     * @default false
     */
    @api clickable = false;

    /**
     * Gets the width value, prioritizing the size attribute if provided
     * @returns {String} The width in pixels
     */
    get computedWidth() {
        return this.size || this.width;
    }

    /**
     * Gets the height value, prioritizing the size attribute if provided
     * @returns {String} The height in pixels
     */
    get computedHeight() {
        return this.size || this.height;
    }

    /**
     * Gets the full URL to the icon in the static resource
     * @returns {String} The URL to the icon
     */
    get iconUrl() {
        if (!this.iconName) {
            return '';
        }
        
        // If the iconName already includes a file extension, use it as is
        if (this.iconName.includes('.')) {
            return `${resources}/images/${this.iconName}`;
        }
        
        // Otherwise, assume it's an SVG
        return `${resources}/images/${this.iconName}.svg`;
    }
    
    /**
     * Computes the CSS classes for the icon
     * @returns {String} The CSS classes
     */
    get computedClass() {
        return classSet(this.className || '')
            .add({
                'pointer': this.clickable
            })
            .toString();
    }
    
    /**
     * Handles click events on the icon
     * @param {Event} event - The click event
     */
    handleClick(event) {
        if (!this.clickable) {
            return;
        }
        
        event.preventDefault();
        event.stopPropagation();
        
        this.dispatchEvent(new CustomEvent('click', {
            bubbles: true,
            composed: true
        }));
    }
}