/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * myAccountRegister
 * @description:
 * @author: <PERSON><PERSON>
 * @date: 3/2/25
 */
import {LightningElement, track, wire} from 'lwc';
import resources from "@salesforce/resourceUrl/MyAccountResources";
import createUser from '@salesforce/apex/MyAccountUserService.createUser';
import getConfig from '@salesforce/apex/MyAccountGeneralConfig.getConfig';
import {classSet} from "c/utils";
import {ShowToastEvent} from "c/showToastEvent";
import {NavigationMixin} from "lightning/navigation";

export default class MyAccountRegister extends NavigationMixin(LightningElement) {
    @track userCredentials = {}
    recaptchaSiteKey;

    loading = false;
    rendered = true;
    showSuccess = false;
    mainSiteUrl;

    /**
     * Determines if the submit button should be disabled
     * @returns {boolean} True if required fields are missing or form is loading
     */
    get submitDisabled() {
        return !this.userCredentials.firstName || !this.userCredentials.lastName || !this.userCredentials.email || this.loading;
    }

    /**
     * Determines if registration form should be displayed
     * @returns {boolean} False if success message is showing, true otherwise
     */
    get showForm() {
        return !this.showSuccess;
    }

    /**
     * Gets the base path for logo resources
     * @returns {string} Path to the images directory in static resources
     */
    get logoResource() {
        return resources + '/images/';
    }

    /**
     * Computes CSS classes for the login container
     * @returns {string} Space-separated list of CSS classes
     */
    get loginContainerClasses() {
        return classSet(
            'login flex flex-direction-column flex-gap-sp border border-radius--small ptm pbm prm plm shadow-bottom'
        )
            .add({
                'hidden': !this.rendered
            })
            .toString();
    }

    /**
     * Wire adapter for configuration data
     * Sets recaptcha site key and main site URL
     * @param {Object} param0 - Wire service data/error object
     */
    @wire(getConfig)
    wiredConfig({ error, data }) {
        if (data) {
            this.recaptchaSiteKey = data.siteKey;
            this.mainSiteUrl = data.clientMainSiteUrl;
            this.error = undefined;
        } else if(error) {
            this.dispatchEvent(
                new ShowToastEvent({
                    label: 'Error',
                    message: 'There was a problem retrieving site config',
                    mode: 'sticky',
                    variant: 'error',
                })
            );
        }
    }

    /**
     * Handles image load event
     * Sets rendered state to true when logo is loaded
     */
    handleImgLoad() {
        this.rendered = true;
    }

    /**
     * Handles input field changes
     * Updates userCredentials object with new values
     * @param {Event} event - Input change event
     */
    handleInputChange(event) {
        const { name, value } = event.target;
        this.userCredentials[name] = value;
    }

    /**
     * Handles form submission
     * Validates required fields, verifies recaptcha, and creates user
     */
    handleSubmit() {
        const firstNameInput = this.refs?.firstNameInput;
        const lastNameInput = this.refs?.lastNameInput;
        const emailInput = this.refs?.emailInput;

        const firstNameValid = firstNameInput ? firstNameInput.reportValidity() : false;
        const lastNameValid = lastNameInput ? lastNameInput.reportValidity() : false;
        const emailValid = emailInput ? emailInput.reportValidity() : false;

        if (!this.userCredentials.firstName || !this.userCredentials.lastName || !this.userCredentials.email ||
            !firstNameValid || !lastNameValid || !emailValid) {
            this.dispatchEvent(
                new ShowToastEvent({
                    label: 'Error',
                    message: 'All fields are required.',
                    mode: 'sticky',
                    variant: 'error',
                })
            );
            return;
        }

        this.loading = true;

        this.refs.captcha.refreshAndVerifyRecaptcha()
            .then((isValidToken) => {
                if(!isValidToken) {
                    this.loading = false;
                    this.dispatchEvent(
                        new ShowToastEvent({
                            label: 'Error',
                            message: 'Unsuccessful captcha validation attempt',
                            mode: 'sticky',
                            variant: 'error',
                        })
                    );
                    return;
                }

                createUser({
                    userModelString:  JSON.stringify(this.userCredentials),
                })
                    .then(() => {
                        this.dispatchEvent(
                            new ShowToastEvent({
                                label: 'Success',
                                message: 'Account created!',
                                mode: 'sticky',
                                variant: 'success',
                            })
                        );
                        this.showSuccess = true;
                    })
                    .catch(error => {
                        console.error(error);
                        this.dispatchEvent(
                            new ShowToastEvent({
                                label: 'Error',
                                message: error?.message || error?.body?.message || 'Error creating account.',
                                mode: 'sticky',
                                variant: 'error',
                            })
                        );
                    }).finally(() => {
                        this.loading = false;
                    })
            }).catch((error)=> {
                console.error(error);
                this.dispatchEvent(
                    new ShowToastEvent({
                        label: 'Error',
                        message: error?.body?.message || error?.message || 'Error creating account.',
                        mode: 'sticky',
                        variant: 'error',
                    })
                );
            }).finally(()=> {
                this.loading = false;
            });
    }

    /**
     * Navigates to the login page
     * Uses NavigationMixin to handle routing
     */
    handleNavigateToLogin() {
        this[NavigationMixin.Navigate]({
            type: 'comm__namedPage',
            attributes: {
                name: 'Login'
            }
        });
    }

    /**
     * Navigates to the main client site
     * Uses NavigationMixin to handle external navigation
     */
    handleNavigateToClientSite() {
        this[NavigationMixin.Navigate]({
                type: 'standard__webPage',
                attributes: {
                    url: this.mainSiteUrl
                }
            }, false
        );
    }

    /**
     * Resets the registration form
     * Clears all user credentials
     */
    resetForm() {
        this.userCredentials = {};
    }
}