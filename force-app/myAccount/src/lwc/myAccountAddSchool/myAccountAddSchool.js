/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * myAccountAddSchool
 * @description:
 * @author: <PERSON><PERSON>
 * @date: 3/26/25
 */
import { LightningElement, track, api } from 'lwc';
import { getEvents, US_STATES } from 'c/myAccountConstants';
import createAffiliation from '@salesforce/apex/MyAccountSchoolService.createAffiliation';
import { ShowToastEvent } from 'c/showToastEvent';
import AuthUtils from "c/myAccountAuthUtils";

const EVENTS = getEvents();

export default class MyAccountAddSchool extends LightningElement {
    @track selectedSchool;
    @track stateSelection;
    @track isSubmitting = false;
    stateOptions = US_STATES;

    /**
     * Computed property that determines if the submit button should be disabled
     * @returns {boolean} True if no school is selected or submission is in progress
     */
    get isSubmitDisabled() {
        return !this.selectedSchool || this.isSubmitting;
    }

    /**
     * Handles school selection from the school lookup component
     * Updates the selectedSchool tracking property
     * @param {CustomEvent} event - Contains school details in event.detail
     */
    handleSchoolSelect(event) {
        this.selectedSchool = event.detail;
    }

    /**
     * Handles state selection from the state picklist
     * Updates the stateSelection tracking property
     * @param {CustomEvent} event - Contains selected state value in event.detail.value
     */
    handleStateSelect(event) {
        this.stateSelection = event.detail.value;
    }

    /**
     * Clears the currently selected state
     * Resets stateSelection to null
     */
    handleStateClear() {
        this.stateSelection = null;
    }

    /**
     * Handles cancel button click
     * Dispatches close event to parent component
     */
    handleCancel() {
        this.dispatchEvent(
            new CustomEvent(EVENTS.CLOSE_EVT, {
                bubbles: true,
                composed: true
            })
        );
    }

    /**
     * Handles form submission to create school affiliation
     * Creates affiliation record and handles success/error scenarios
     * Dispatches appropriate events based on the outcome
     * @async
     */
    async handleSubmit() {
        if (!this.selectedSchool) {
            return;
        }

        this.isSubmitting = true;

        try {
            const affiliationRequest = {
                role: 'Teacher',
                schoolId: this.selectedSchool.schoolId,
                schoolName: this.selectedSchool.schoolName,
                schoolType: this.selectedSchool.schoolType,
                schoolLevel: this.selectedSchool.schoolLevel,
                street: this.selectedSchool.street,
                city: this.selectedSchool.city,
                state: this.selectedSchool.state,
                postalCode: this.selectedSchool.zip,
                ncesId: this.selectedSchool.ncesId,
                isExternalSchool: !this.selectedSchool.schoolId.startsWith('001')
            };

            await createAffiliation({
                sessionToken: AuthUtils.getSessionToken(),
                affiliationRequestString: JSON.stringify(affiliationRequest)
            });

            this.dispatchEvent(
                new ShowToastEvent({
                    label: 'Success',
                    message: 'School affiliation created successfully',
                    variant: 'success'
                })
            );

            this.dispatchEvent(new CustomEvent(EVENTS.CLOSE_EVT, {
                bubbles: true,
                composed: true
            }));

            this.dispatchEvent(new CustomEvent('affiliationadded', {
                bubbles: true,
                composed: true
            }));

        } catch (error) {
            console.error('Error creating affiliation:', error);
            this.dispatchEvent(
                new ShowToastEvent({
                    label: 'Error',
                    message: error.body?.message || 'Failed to create school affiliation',
                    variant: 'error'
                })
            );
        } finally {
            this.isSubmitting = false;
        }
    }

    /**
     * Handles school creation event from child component
     * Updates component state with newly created school details
     * @param {CustomEvent} event - Contains new school details in event.detail
     */
    handleSchoolCreated(event) {
        const newSchool = event.detail;

        const formattedSchool = {
            schoolId: newSchool.schoolId,
            schoolName: newSchool.name,
            street: newSchool.street,
            city: newSchool.city,
            state: newSchool.state,
            zip: newSchool.postalCode,
            country: newSchool.country
        };

        this.selectedSchool = formattedSchool;
        this.stateSelection = formattedSchool.state;
    }
}