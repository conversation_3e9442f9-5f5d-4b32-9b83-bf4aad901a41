/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * myAccountTableHeaderColumn
 * @description: 
 * @author: <PERSON><PERSON>
 * @date: 3/21/25
 */

@import 'c/myAccountGlobalStyles';


:host {
    display: table-cell;
}

.header-content {
    display: flex;
    align-items: center;
    gap: var(--xsp);
    height: 100%;
}

.sort-arrow {
    font-size: 12px;
    line-height: 1;
    display: flex;
    align-items: center;
}

.label-text {
    line-height: 1.2;
}
