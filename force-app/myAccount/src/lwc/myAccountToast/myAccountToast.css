/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * myAccountToast
 * @description: 
 * @author: <PERSON><PERSON>
 * @date: 2/15/25
 */

@import 'c/myAccountGlobalStyles';

.toast {
    display: flex;
    align-items: center;
    padding: var(--mp);
    border-radius: var(--border-radius-medium);
    box-shadow: var(--card-box-shadow);
    min-width: 320px;
    max-width: 480px;
    animation: fadeIn 0.3s ease-in;
    margin: var(--sp);
}

.toast.closing {
    animation: fadeOut 0.3s ease-out;
}

.toast.info {
    background-color: var(--background-color-white);
    border-left: 4px solid var(--border-color-green);
}

.toast.success {
    background-color: var(--background-color-white);
    border-left: 4px solid var(--success-color);
}

.toast.warning {
    background-color: var(--background-color-white);
    border-left: 4px solid var(--pending-color);
}

.toast.error {
    background-color: var(--background-color-white);
    border-left: 4px solid var(--danger-color);
}

.toast-content {
    flex: 1;
    font-size: var(--rfs);
    color: var(--text-color);
    word-break: break-word;
}

.close-button-container {
    margin-left: var(--sp);
}

.toast-container {
    position: fixed;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    pointer-events: none;
}

.toast-container.top-left {
    top: var(--mp);
    left: var(--mp);
    align-items: flex-start;
}

.toast-container.top-middle {
    top: var(--mp);
    left: 50%;
    transform: translateX(-50%);
    align-items: center;
}

.toast-container.top-right {
    top: var(--mp);
    right: var(--mp);
    align-items: flex-end;
}

.toast-container.bottom-left {
    bottom: var(--mp);
    left: var(--mp);
    align-items: flex-start;
}

.toast-container.bottom-middle {
    bottom: var(--mp);
    left: 50%;
    transform: translateX(-50%);
    align-items: center;
}

.toast-container.bottom-right {
    bottom: var(--mp);
    right: var(--mp);
    align-items: flex-end;
}

.toast {
    pointer-events: auto;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-20px);
    }
}

/* Toast Container CSS */

.top {
    top: 0;
    bottom: unset;
}

.bottom {
    bottom: 0;
    top: unset;
}

.left {
    left: -30%;
    right: auto;
}

.right {
    left: auto;
    right: -30%;
}

.middle {
    transform: none;
    right: 50%;
}

@media (max-width: 30rem) {
    .middle {
        transform: none;
        right: 0;
        left: 0;
    }
}

.slds-notify_toast {
    padding: 16px;
    border-radius: 6px;
    box-shadow: 0 4px 12px #0000001a;
    width: 356px;
    font-size: 13px;
    display: flex;
    align-items: center;
    gap: 6px;
    margin: auto;
    font-weight: 500;
}

.success {
    background: #ECFDF3;
    border: 1px solid #CCFFFF;
    color: var(--primary-color);
    position: fixed;
    right: 20px;
    bottom: 20px;
}

.warning {
    background: #f8e263c2;
    border: 1px solid #CCFFFF;
    position: fixed;
    right: 20px;
    bottom: 20px;
}

.error {
    background: #FFF0F0;
    border: 1px solid #FFE0E1;
    color: var(--danger-color);
    position: fixed;
    right: 20px;
    bottom: 20px;
}

.close-icon {
    width: 16px;
    height: 16px;
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.close-icon:hover {
    opacity: 1;
}

.button-reset {
    padding: 0;
    background: none;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.toast-icon {
    width: 20px;
    height: 20px;
}

.icon-container {
    display: flex;
    align-items: center;
    justify-content: center;
}

/*.toast.info .toast-icon {*/
/*    filter: var(--info-icon-filter, brightness(0) saturate(100%) invert(45%) sepia(36%) saturate(2538%) hue-rotate(175deg) brightness(101%) contrast(101%));*/
/*}*/

/*.toast.success .toast-icon {*/
/*    filter: var(--success-icon-filter, brightness(0) saturate(100%) invert(39%) sepia(74%) saturate(1096%) hue-rotate(93deg) brightness(93%) contrast(88%));*/
/*}*/

/*.toast.warning .toast-icon {*/
/*    filter: var(--warning-icon-filter, brightness(0) saturate(100%) invert(77%) sepia(38%) saturate(5392%) hue-rotate(359deg) brightness(105%) contrast(104%));*/
/*}*/

/*.toast.error .toast-icon {*/
/*    filter: var(--error-icon-filter, brightness(0) saturate(100%) invert(29%) sepia(94%) saturate(7471%) hue-rotate(356deg) brightness(99%) contrast(118%));*/
/*}*/

