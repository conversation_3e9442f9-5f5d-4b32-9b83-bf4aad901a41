<!--
  ~ Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
  ~
  ~ The code below is part of the Foglight Arts & Culture package and is not
  ~ to be redistributed without express written consent by Foglight.
  -->

<!--
 * myAccountToast
 * @description: 
 * @author: <PERSON><PERSON>
 * @date: 2/15/25
 -->

<!-- My Account Toast -->
<template>
    <div class={containerClass}>
        <div class={toastClass}
             role="status"
             onanimationend={handleAnimationEnd}>
            <div class="icon-container">
                <img src={iconUrl} alt={iconAlternativeText} class="toast-icon" />
            </div>
            <div class="toast-content mls">
                {message}
            </div>
            <div class="close-button-container">
                <button class="button-reset pointer" onclick={handleCloseClick}>
                    <img src={closeIconUrl} alt="Close" class="close-icon" />
                </button>
            </div>
        </div>
    </div>
</template>