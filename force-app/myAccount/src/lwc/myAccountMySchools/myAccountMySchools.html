<!--
  ~ Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
  ~
  ~ The code below is part of the Foglight Arts & Culture package and is not
  ~ to be redistributed without express written consent by Foglight.
  -->

<!--
 * myAccountMySchools
 * @description: 
 * @author: <PERSON><PERSON>
 * @date: 2/20/25
 -->

<!-- My Account My Schools -->
<template>
    <div class="full-width mtm mbl">
        <c-my-account-page-header
                main-header={pageMainHeader}
                sub-header={pageSubHeader}>
        </c-my-account-page-header>

        <div class="prl pll pbl mrm mlm mbm">
            <c-my-account-card
                    class="full-width mtm"
                    header="Your School Affiliations"
                    icon="mortarboard-fill.svg">

                <div slot="content" class="min-height-medium">
                    <c-my-account-table
                        records={schools}
                        columns={schoolColumns}
                        onsort={handleSort}
                        lazy-loading="false"
                        empty-state-title="No School Affiliations"
                        empty-state-message="You don't have any school affiliations yet. Click 'Add School' to get started.">
                    </c-my-account-table>
                </div>

                <div slot="button">
                    <c-my-account-popover
                            type="popup"
                            blur-close=true
                            button-type="brand"
                            button-name="Add School"
                            button-title="Add School">
                        <c-my-account-add-school
                            onaffiliationadded={refreshSchools}>
                        </c-my-account-add-school>
                    </c-my-account-popover>
                </div>
            </c-my-account-card>
        </div>
    </div>
</template>