/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

@import 'c/myAccountGlobalStyles';

.result-item {
    padding: var(--sp);
    transition: background-color 0.2s ease;
    outline: none;
}

.result-item:hover,
.result-item:focus {
    background-color: var(--background-color-gray);
}

.result-item:focus-visible {
    background-color: var(--background-color-gray);
    position: relative;
    z-index: 1;
}

.result-item:not(:last-child) {
    border-bottom: 1px solid var(--border-color);
}