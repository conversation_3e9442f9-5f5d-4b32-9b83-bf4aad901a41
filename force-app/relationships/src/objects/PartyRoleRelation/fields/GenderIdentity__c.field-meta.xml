<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>GenderIdentity__c</fullName>
    <description>The gender identity that drives reciprocal relationships. This picklist must match the options available on the Contact's standard Gender Identity field.

Note: "Neutral" MUST be in this list and used for non-gendered relationship roles like "Spouse-Spouse".</description>
    <inlineHelpText>The Gender Identity of this CCR's Contact</inlineHelpText>
    <label>Gender Identity</label>
    <required>false</required>
    <trackFeedHistory>false</trackFeedHistory>
    <type>Picklist</type>
    <valueSet>
        <restricted>true</restricted>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>Male</fullName>
                <default>false</default>
                <label>Male</label>
            </value>
            <value>
                <fullName>Female</fullName>
                <default>false</default>
                <label>Female</label>
            </value>
            <value>
                <fullName>Nonbinary</fullName>
                <default>false</default>
                <label>Nonbinary</label>
            </value>
            <value>
                <fullName>Neutral</fullName>
                <default>false</default>
                <label>Neutral</label>
            </value>
        </valueSetDefinition>
    </valueSet>
</CustomField>
