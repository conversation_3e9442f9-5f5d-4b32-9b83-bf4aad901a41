<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>Type__c</fullName>
    <description>This field drives automatic population of the Party Role Relation lookup by finding a PRR whose name matches this field's value. Picklist values for this field are autobuilt as PRRs are added.</description>
    <inlineHelpText>Field describes how the Related Contact is connected to the Contact.</inlineHelpText>
    <label>Type</label>
    <required>false</required>
    <trackHistory>false</trackHistory>
    <type>Picklist</type>
    <valueSet>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>Child</fullName>
                <default>false</default>
                <label>Child</label>
            </value>
            <value>
                <fullName>Mother</fullName>
                <default>false</default>
                <label>Mother</label>
            </value>
            <value>
                <fullName>Parent</fullName>
                <default>false</default>
                <label>Parent</label>
            </value>
            <value>
                <fullName>Son</fullName>
                <default>false</default>
                <label>Son</label>
            </value>
            <value>
                <fullName>Spouse</fullName>
                <default>false</default>
                <label>Spouse</label>
            </value>
            <value>
                <fullName>Aunt</fullName>
                <default>false</default>
                <label>Aunt</label>
            </value>
            <value>
                <fullName>Brother</fullName>
                <default>false</default>
                <label>Brother</label>
            </value>
            <value>
                <fullName>Cousin</fullName>
                <default>false</default>
                <label>Cousin</label>
            </value>
            <value>
                <fullName>Coworker</fullName>
                <default>false</default>
                <label>Coworker</label>
            </value>
            <value>
                <fullName>Daughter</fullName>
                <default>false</default>
                <label>Daughter</label>
            </value>
            <value>
                <fullName>Employee</fullName>
                <default>false</default>
                <label>Employee</label>
            </value>
            <value>
                <fullName>Family</fullName>
                <default>false</default>
                <label>Family</label>
            </value>
            <value>
                <fullName>Father</fullName>
                <default>false</default>
                <label>Father</label>
            </value>
            <value>
                <fullName>Friend</fullName>
                <default>false</default>
                <label>Friend</label>
            </value>
            <value>
                <fullName>Grandchild</fullName>
                <default>false</default>
                <label>Grandchild</label>
            </value>
            <value>
                <fullName>Granddaughter</fullName>
                <default>false</default>
                <label>Granddaughter</label>
            </value>
            <value>
                <fullName>Grandfather</fullName>
                <default>false</default>
                <label>Grandfather</label>
            </value>
            <value>
                <fullName>Grandmother</fullName>
                <default>false</default>
                <label>Grandmother</label>
            </value>
            <value>
                <fullName>Grandparent</fullName>
                <default>false</default>
                <label>Grandparent</label>
            </value>
            <value>
                <fullName>Grandson</fullName>
                <default>false</default>
                <label>Grandson</label>
            </value>
            <value>
                <fullName>Husband</fullName>
                <default>false</default>
                <label>Husband</label>
            </value>
            <value>
                <fullName>Manager</fullName>
                <default>false</default>
                <label>Manager</label>
            </value>
            <value>
                <fullName>Partner</fullName>
                <default>false</default>
                <label>Partner</label>
            </value>
            <value>
                <fullName>Sibling</fullName>
                <default>false</default>
                <label>Sibling</label>
            </value>
            <value>
                <fullName>Sister</fullName>
                <default>false</default>
                <label>Sister</label>
            </value>
            <value>
                <fullName>Uncle</fullName>
                <default>false</default>
                <label>Uncle</label>
            </value>
            <value>
                <fullName>Wife</fullName>
                <default>false</default>
                <label>Wife</label>
            </value>
        </valueSetDefinition>
    </valueSet>
</CustomField>
