/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 5/16/25.
 */

/**
 * Automatically sets the Party Relationship Role based on the selected "Type" matching a PRR and gender settings.
 */
public without sharing class TRG_CCR_PRR extends Domain {
    private static final Set<Id> originalRecordsDeleted = new Set<Id>();
    private static final String NEUTRAL_KEY = 'neutral-neutral';

    public TRG_CCR_PRR() {
        super();
    }
    public TRG_CCR_PRR(List<Affiliation__c> triggerRecords) {
        super(triggerRecords);
    }

    public override void doBeforeInsert() {
        defaultPRR(this.triggerRecords);
    }

    public override void doAfterDelete() {
        deleteInverseRecord(this.triggerRecords);
    }

    public override void doAfterUndelete() {
        undeleteInverseRecord(this.triggerRecords);
    }

    /*

    PRR Id          | Role     | Gender Id  | Related Role Name | Related Gender Id | Related PRR Id  | RelationshipObjectName
    ----------------+----------+------------+-------------------+-------------------+-----------------+-----------------------------
    Mother-Son      | Mother   | Female     | Son               | Male              | Son-Mother      | Contact_Contact_Relationship
    Son-Mother      | Son      | Male       | Mother            | Female            | Mother-Son      | Contact_Contact_Relationship
    Mother-Daughter | Mother   | Female     | Daughter          | Female            | Mother-Daughter | Contact_Contact_Relationship
    Daughter-Mother | Daughter | Female     | Mother            | Female            | Daughter-Mother | Contact_Contact_Relationship
    Mother-Child    | Mother   | Female     | Child             | Non-binary        | Mother-Child    | Contact_Contact_Relationship
    Child-Mother    | Child    | Non-binary | Mother            | Female            | Child-Mother    | Contact_Contact_Relationship
    Spouse-Spouse   | Spouse   | Neutral    | Spouse            | Neutral           | -NULL-          | Contact_Contact_Relationship

    Rules for creating reciprocal CCR:
     - Find PRRs where (RelationshipObjectName = Contact_Contact_Relationship)
     - Find PRRs where Role matches CCR's Type
     - If CCR's Type has multiple PRRs:
        - Use the PRR where the Contact's Gender matches the PRR's Related Role Gender
        - If no matching record, and the CCR has a PRR with "Neutral" gender, use it for both records.
     */
    public static void defaultPRR(List<ContactContactRelation> records) {
        Set<String> selectedTypes = new Set<String>();
        Map<Id, Contact> allContacts = new Map<Id, Contact>();
        for (ContactContactRelation ccr : records) {
            if (ccr.PartyRoleRelationId == null && String.isNotBlank(ccr.Type__c)) {
                selectedTypes.add(ccr.Type__c);
                allContacts.put(ccr.ContactId, null);
                allContacts.put(ccr.RelatedContactId, null);
            }
        }

        if (selectedTypes.size() == 0) {
            return;
        }

        // Get all the needed Party Relationship Roles grouped by role and related gender
        Map<String, Map<String, PartyRoleRelation>> prrsByTypeByGender = new Map<String, Map<String, PartyRoleRelation>>();
        //  RoleName => { Gender-Compound-Key => PRR}
        for (PartyRoleRelation prr : [
                SELECT Id,
                        Name,
                        RoleName,
                        GenderIdentity__c,
                        RelatedRoleName,
                        RelatedGenderIdentity__c,
                        RelatedInverseRecordId
                FROM PartyRoleRelation
                WHERE RelationshipObjectName = 'Contact_Contact_Relationship'
                AND RoleName IN :selectedTypes
        ]) {
            String roleKey = prr.RoleName.toLowerCase();
            //String genderCompoundKey = prr.GenderIdentity__c?.toLowerCase() + '-' + prr.RelatedGenderIdentity__c?.toLowerCase();

            Map<String, PartyRoleRelation> prrsByGender = prrsByTypeByGender.get(roleKey) ?? new Map<String, PartyRoleRelation>();
            prrsByTypeByGender.put(roleKey, prrsByGender);
            prrsByGender.put(prr.RelatedGenderIdentity__c?.toLowerCase(), prr);
        }

        // Get all the contacts and related contacts... we need their genders
        allContacts = new Map<Id, Contact>([
                SELECT Id, GenderIdentity
                FROM Contact
                WHERE Id IN :allContacts.keySet()
        ]);

        // Now set the PRR
        for (ContactContactRelation ccr : records) {
            if (ccr.PartyRoleRelationId == null && String.isNotBlank(ccr.Type__c)) {
                Contact contact         = allContacts.get(ccr.ContactId);

                String roleKey          = ccr.Type__c?.toLowerCase();
                String contactsGender   = contact?.GenderIdentity?.toLowerCase();

                Map<String, PartyRoleRelation> prrsByGender = prrsByTypeByGender.get(roleKey) ?? new Map<String, PartyRoleRelation>();
                System.debug(JSON.serializePretty(prrsByGender));
                System.debug(roleKey);
                System.debug(contactsGender);

                if (prrsByGender.containsKey(contactsGender)) {
                    ccr.PartyRoleRelationId = prrsByGender.get(contactsGender).Id;
                } else if (prrsByGender.containsKey('neutral')) {
                    ccr.PartyRoleRelationId = prrsByGender.get('neutral').Id;
                } else {
                    ccr.addError('A Party Role Relation record could not be found that matches the specified type and the contact\'s gender (or neutral)');
                }
            }
            System.debug(ccr);
        }
    }

    public static void deleteInverseRecord(List<ContactContactRelation> records) {
        List<Id> recordIdsToDelete = new List<Id>();
        for (ContactContactRelation ccr : records) {
            originalRecordsDeleted.add(ccr.Id);
            if (
                    ccr.RelatedInverseRecordId != null &&
                    !originalRecordsDeleted.contains(ccr.RelatedInverseRecordId)
            ) {
                recordIdsToDelete.add(ccr.RelatedInverseRecordId);
            }
        }

        if (recordIdsToDelete.size() > 0) {
            Database.delete(recordIdsToDelete);
        }
    }

    public static void undeleteInverseRecord(List<ContactContactRelation> records) {
        List<ContactContactRelation> recordsToUndelete = [
                SELECT Id
                FROM ContactContactRelation
                WHERE IsDeleted = TRUE AND RelatedInverseRecordId IN :records
                ALL ROWS
        ];

        if (recordsToUndelete.size() > 0) {
            Database.undelete(recordsToUndelete);
        }
    }
}