<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>61.0</apiVersion>
    <environments>Default</environments>
    <interviewLabel>CCR: Keep Start Dates and End Dates in Sync {!$Flow.CurrentDateTime}</interviewLabel>
    <isOverridable>true</isOverridable>
    <label>CCR: Keep Start Dates and End Dates in Sync</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <name>Update_Related_CCR</name>
        <label>Update Related CCR</label>
        <locationX>176</locationX>
        <locationY>323</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.RelatedInverseRecord.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>EndDate</field>
            <value>
                <elementReference>$Record.EndDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>IsActive</field>
            <value>
                <elementReference>$Record.IsActive</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>StartDate</field>
            <value>
                <elementReference>$Record.StartDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <elementReference>$Record.Status__c</elementReference>
            </value>
        </inputAssignments>
        <object>ContactContactRelation</object>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Update_Related_CCR</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>StartDate</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>EndDate</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>IsActive</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <object>ContactContactRelation</object>
        <recordTriggerType>Update</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
